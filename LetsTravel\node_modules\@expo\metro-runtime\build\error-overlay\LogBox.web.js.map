{"version": 3, "file": "LogBox.web.js", "sourceRoot": "", "sources": ["../../src/error-overlay/LogBox.web.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAOH,IAAI,MAAe,CAAC;AAapB;;GAEG;AACH,IAAI,OAAO,EAAE;IACX,MAAM,UAAU,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAChD,MAAM,EAAE,cAAc,EAAE,kBAAkB,EAAE,GAC1C,OAAO,CAAC,uBAAuB,CAA2C,CAAC;IAE7E,IAAI,oBAAsD,CAAC;IAC3D,IAAI,gBAAkD,CAAC;IAEvD,IAAI,iBAAiB,GAAY,KAAK,CAAC;IAEvC,MAAM,GAAG;QACP,OAAO;YACL,IAAI,iBAAiB,EAAE;gBACrB,OAAO;aACR;YAED,iBAAiB,GAAG,IAAI,CAAC;YAEzB,yCAAyC;YACzC,kDAAkD;YAElD,wEAAwE;YACxE,mEAAmE;YACnE,0BAA0B;YAC1B,MAAM,cAAc,GAAG,oBAAoB,IAAI,IAAI,CAAC;YACpD,IAAI,cAAc,EAAE;gBAClB,oBAAoB,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAEnD,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;oBAC1B,gBAAgB,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC9B,CAAC,CAAC;aACH;YAED,gBAAgB,GAAG,aAAa,CAAC;YAEjC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE;gBACnC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aAC9B;QACH,CAAC;QAED,SAAS;YACP,IAAI,CAAC,iBAAiB,EAAE;gBACtB,OAAO;aACR;YAED,iBAAiB,GAAG,KAAK,CAAC;YAE1B,yEAAyE;YACzE,iDAAiD;YACjD,6DAA6D;YAC7D,oEAAoE;YACpE,gBAAgB,GAAG,oBAAoB,CAAC;YACxC,OAAQ,OAAe,CAAC,aAAa,CAAC;QACxC,CAAC;QAED,WAAW;YACT,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAED,UAAU,CAAC,QAAkC;YAC3C,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;QAED,aAAa,CAAC,KAAe;YAC3B,UAAU,CAAC,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,YAAY;YACV,UAAU,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;QAED,MAAM,CAAC,GAAY;YACjB,IAAI,iBAAiB,EAAE;gBACrB,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACxB;QACH,CAAC;QAED,YAAY,CAAC,KAA4B;YACvC,IAAI,iBAAiB,EAAE;gBACrB,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;aAChC;QACH,CAAC;KACF,CAAC;IAEF,MAAM,sBAAsB,GAAG,CAAC,GAAG,IAAS,EAAE,EAAE;QAC9C,OAAO,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACxE,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,GAAG,IAAsC,EAAQ,EAAE;QACxE,gDAAgD;QAChD,IAAI,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5C,oBAAoB,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAChC,OAAO;SACR;QAED,IAAI;YACF,IAAI,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAC,EAAE;gBACpC,qEAAqE;gBACrE,6EAA6E;gBAC7E,2EAA2E;gBAC3E,0EAA0E;gBAC1E,EAAE;gBACF,+EAA+E;gBAC/E,0EAA0E;gBAC1E,oBAAoB,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;gBAChC,OAAO;aACR;YAED,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;YAEnE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACjD,wEAAwE;gBACxE,yFAAyF;gBACzF,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC9C,oBAAoB,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAErD,UAAU,CAAC,MAAM,CAAC;oBAChB,oEAAoE;oBACpE,+BAA+B;oBAC/B,KAAK,EAAE,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO;oBAC1E,QAAQ;oBACR,OAAO;oBACP,cAAc;iBACf,CAAC,CAAC;aACJ;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,UAAU,CAAC,2BAA2B,CAAC,GAAG,CAAC,CAAC;SAC7C;IACH,CAAC,CAAC;CACH;KAAM;IACL,MAAM,GAAG;QACP,OAAO,KAAU,CAAC;QAClB,SAAS,KAAU,CAAC;QACpB,WAAW;YACT,OAAO,KAAK,CAAC;QACf,CAAC;QACD,UAAU,CAAC,QAAkC,IAAS,CAAC;QACvD,aAAa,CAAC,KAAe,IAAS,CAAC;QACvC,YAAY,KAAU,CAAC;QACvB,MAAM,CAAC,GAAY,IAAS,CAAC;QAC7B,YAAY,CAAC,EAAyB,IAAS,CAAC;KACjD,CAAC;CACH;AAED,kBAAe,MAAM,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport { IgnorePattern, LogData } from './Data/LogBoxData';\nimport { ExtendedExceptionData } from './Data/parseLogBoxLog';\n\nexport { LogData, ExtendedExceptionData, IgnorePattern };\n\nlet LogBox: ILogBox;\n\ninterface ILogBox {\n  install(): void;\n  uninstall(): void;\n  isInstalled(): boolean;\n  ignoreLogs(patterns: readonly IgnorePattern[]): void;\n  ignoreAllLogs(ignore?: boolean): void;\n  clearAllLogs(): void;\n  addLog(log: LogData): void;\n  addException(error: ExtendedExceptionData): void;\n}\n\n/**\n * LogBox displays logs in the app.\n */\nif (__DEV__) {\n  const LogBoxData = require('./Data/LogBoxData');\n  const { parseLogBoxLog, parseInterpolation } =\n    require('./Data/parseLogBoxLog') as typeof import('./Data/parseLogBoxLog');\n\n  let originalConsoleError: typeof console.error | undefined;\n  let consoleErrorImpl: typeof console.error | undefined;\n\n  let isLogBoxInstalled: boolean = false;\n\n  LogBox = {\n    install(): void {\n      if (isLogBoxInstalled) {\n        return;\n      }\n\n      isLogBoxInstalled = true;\n\n      // Trigger lazy initialization of module.\n      // require(\"../NativeModules/specs/NativeLogBox\");\n\n      // IMPORTANT: we only overwrite `console.error` and `console.warn` once.\n      // When we uninstall we keep the same reference and only change its\n      // internal implementation\n      const isFirstInstall = originalConsoleError == null;\n      if (isFirstInstall) {\n        originalConsoleError = console.error.bind(console);\n\n        console.error = (...args) => {\n          consoleErrorImpl?.(...args);\n        };\n      }\n\n      consoleErrorImpl = registerError;\n\n      if (process.env.NODE_ENV === 'test') {\n        LogBoxData.setDisabled(true);\n      }\n    },\n\n    uninstall(): void {\n      if (!isLogBoxInstalled) {\n        return;\n      }\n\n      isLogBoxInstalled = false;\n\n      // IMPORTANT: we don't re-assign to `console` in case the method has been\n      // decorated again after installing LogBox. E.g.:\n      // Before uninstalling: original > LogBox > OtherErrorHandler\n      // After uninstalling:  original > LogBox (noop) > OtherErrorHandler\n      consoleErrorImpl = originalConsoleError;\n      delete (console as any).disableLogBox;\n    },\n\n    isInstalled(): boolean {\n      return isLogBoxInstalled;\n    },\n\n    ignoreLogs(patterns: readonly IgnorePattern[]): void {\n      LogBoxData.addIgnorePatterns(patterns);\n    },\n\n    ignoreAllLogs(value?: boolean): void {\n      LogBoxData.setDisabled(value == null ? true : value);\n    },\n\n    clearAllLogs(): void {\n      LogBoxData.clear();\n    },\n\n    addLog(log: LogData): void {\n      if (isLogBoxInstalled) {\n        LogBoxData.addLog(log);\n      }\n    },\n\n    addException(error: ExtendedExceptionData): void {\n      if (isLogBoxInstalled) {\n        LogBoxData.addException(error);\n      }\n    },\n  };\n\n  const isWarningModuleWarning = (...args: any) => {\n    return typeof args[0] === 'string' && args[0].startsWith('Warning: ');\n  };\n\n  const registerError = (...args: Parameters<typeof console.error>): void => {\n    // Let errors within LogBox itself fall through.\n    if (LogBoxData.isLogBoxErrorMessage(args[0])) {\n      originalConsoleError?.(...args);\n      return;\n    }\n\n    try {\n      if (!isWarningModuleWarning(...args)) {\n        // Only show LogBox for the 'warning' module, otherwise pass through.\n        // By passing through, this will get picked up by the React console override,\n        // potentially adding the component stack. React then passes it back to the\n        // React Native ExceptionsManager, which reports it to LogBox as an error.\n        //\n        // The 'warning' module needs to be handled here because React internally calls\n        // `console.error('Warning: ')` with the component stack already included.\n        originalConsoleError?.(...args);\n        return;\n      }\n\n      const { category, message, componentStack } = parseLogBoxLog(args);\n\n      if (!LogBoxData.isMessageIgnored(message.content)) {\n        // Interpolate the message so they are formatted for adb and other CLIs.\n        // This is different than the message.content above because it includes component stacks.\n        const interpolated = parseInterpolation(args);\n        originalConsoleError?.(interpolated.message.content);\n\n        LogBoxData.addLog({\n          // Always show the static rendering issues as full screen since they\n          // are too confusing otherwise.\n          level: /did not match\\. Server:/.test(message.content) ? 'fatal' : 'error',\n          category,\n          message,\n          componentStack,\n        });\n      }\n    } catch (err) {\n      LogBoxData.reportUnexpectedLogBoxError(err);\n    }\n  };\n} else {\n  LogBox = {\n    install(): void {},\n    uninstall(): void {},\n    isInstalled(): boolean {\n      return false;\n    },\n    ignoreLogs(patterns: readonly IgnorePattern[]): void {},\n    ignoreAllLogs(value?: boolean): void {},\n    clearAllLogs(): void {},\n    addLog(log: LogData): void {},\n    addException(ex: ExtendedExceptionData): void {},\n  };\n}\n\nexport default LogBox;\n"]}