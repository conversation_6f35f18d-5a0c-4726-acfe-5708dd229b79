{"version": 3, "names": ["getHeaderTitle", "Header", "SafeAreaProviderCompat", "Screen", "React", "Platform", "StyleSheet", "SafeAreaInsetsContext", "BottomTabBarHeightCallbackContext", "BottomTabBarHeightContext", "BottomTabBar", "getTabBarHeight", "MaybeScreen", "MaybeScreenContainer", "BottomTabView", "props", "tabBar", "state", "navigation", "descriptors", "safeAreaInsets", "detachInactiveScreens", "OS", "sceneContainerStyle", "focusedRouteKey", "routes", "index", "key", "loaded", "setLoaded", "useState", "includes", "dimensions", "initialMetrics", "frame", "tabBarHeight", "setTabBarHeight", "layout", "width", "height", "insets", "style", "options", "tabBarStyle", "renderTabBar", "top", "right", "bottom", "left", "styles", "container", "map", "route", "descriptor", "lazy", "unmountOnBlur", "isFocused", "freezeOnBlur", "header", "name", "headerShown", "headerStatusBarHeight", "headerTransparent", "absoluteFill", "zIndex", "render", "create", "flex", "overflow"], "sourceRoot": "../../../src", "sources": ["views/BottomTabView.tsx"], "mappings": ";AAAA,SACEA,cAAc,EACdC,MAAM,EACNC,sBAAsB,EACtBC,MAAM,QACD,4BAA4B;AAKnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AACnD,SAASC,qBAAqB,QAAQ,gCAAgC;AAUtE,OAAOC,iCAAiC,MAAM,4CAA4C;AAC1F,OAAOC,yBAAyB,MAAM,oCAAoC;AAC1E,OAAOC,YAAY,IAAIC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,WAAW,EAAEC,oBAAoB,QAAQ,kBAAkB;AAQpE,eAAe,SAASC,aAAa,CAACC,KAAY,EAAE;EAClD,MAAM;IACJC,MAAM,GAAID,KAAwB,iBAAK,oBAAC,YAAY,EAAKA,KAAK,CAAI;IAClEE,KAAK;IACLC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,qBAAqB,GAAGhB,QAAQ,CAACiB,EAAE,KAAK,KAAK,IAC3CjB,QAAQ,CAACiB,EAAE,KAAK,SAAS,IACzBjB,QAAQ,CAACiB,EAAE,KAAK,KAAK;IACvBC;EACF,CAAC,GAAGR,KAAK;EAET,MAAMS,eAAe,GAAGP,KAAK,CAACQ,MAAM,CAACR,KAAK,CAACS,KAAK,CAAC,CAACC,GAAG;EACrD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,CAACN,eAAe,CAAC,CAAC;EAE7D,IAAI,CAACI,MAAM,CAACG,QAAQ,CAACP,eAAe,CAAC,EAAE;IACrCK,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAEJ,eAAe,CAAC,CAAC;EACzC;EAEA,MAAMQ,UAAU,GAAG9B,sBAAsB,CAAC+B,cAAc,CAACC,KAAK;EAC9D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhC,KAAK,CAAC0B,QAAQ,CAAC,MACrDnB,eAAe,CAAC;IACdM,KAAK;IACLE,WAAW;IACXa,UAAU;IACVK,MAAM,EAAE;MAAEC,KAAK,EAAEN,UAAU,CAACM,KAAK;MAAEC,MAAM,EAAE;IAAE,CAAC;IAC9CC,MAAM,EAAE;MACN,GAAGtC,sBAAsB,CAAC+B,cAAc,CAACO,MAAM;MAC/C,GAAGzB,KAAK,CAACK;IACX,CAAC;IACDqB,KAAK,EAAEtB,WAAW,CAACF,KAAK,CAACQ,MAAM,CAACR,KAAK,CAACS,KAAK,CAAC,CAACC,GAAG,CAAC,CAACe,OAAO,CAACC;EAC5D,CAAC,CAAC,CACH;EAED,MAAMC,YAAY,GAAG,MAAM;IACzB,oBACE,oBAAC,qBAAqB,CAAC,QAAQ,QAC3BJ,MAAM,IACNxB,MAAM,CAAC;MACLC,KAAK,EAAEA,KAAK;MACZE,WAAW,EAAEA,WAAW;MACxBD,UAAU,EAAEA,UAAU;MACtBsB,MAAM,EAAE;QACNK,GAAG,EAAE,CAAAzB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyB,GAAG,MAAIL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,GAAG,KAAI,CAAC;QAC5CC,KAAK,EAAE,CAAA1B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0B,KAAK,MAAIN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM,KAAK,KAAI,CAAC;QAClDC,MAAM,EAAE,CAAA3B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2B,MAAM,MAAIP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,MAAM,KAAI,CAAC;QACrDC,IAAI,EAAE,CAAA5B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4B,IAAI,MAAIR,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,IAAI,KAAI;MAChD;IACF,CAAC,CAAC,CAE2B;EAErC,CAAC;EAED,MAAM;IAAEvB;EAAO,CAAC,GAAGR,KAAK;EAExB,oBACE,oBAAC,sBAAsB,qBACrB,oBAAC,oBAAoB;IACnB,OAAO,EAAEI,qBAAsB;IAC/B,YAAY;IACZ,KAAK,EAAE4B,MAAM,CAACC;EAAU,GAEvBzB,MAAM,CAAC0B,GAAG,CAAC,CAACC,KAAK,EAAE1B,KAAK,KAAK;IAC5B,MAAM2B,UAAU,GAAGlC,WAAW,CAACiC,KAAK,CAACzB,GAAG,CAAC;IACzC,MAAM;MAAE2B,IAAI,GAAG,IAAI;MAAEC;IAAc,CAAC,GAAGF,UAAU,CAACX,OAAO;IACzD,MAAMc,SAAS,GAAGvC,KAAK,CAACS,KAAK,KAAKA,KAAK;IAEvC,IAAI6B,aAAa,IAAI,CAACC,SAAS,EAAE;MAC/B,OAAO,IAAI;IACb;IAEA,IAAIF,IAAI,IAAI,CAAC1B,MAAM,CAACG,QAAQ,CAACqB,KAAK,CAACzB,GAAG,CAAC,IAAI,CAAC6B,SAAS,EAAE;MACrD;MACA,OAAO,IAAI;IACb;IAEA,MAAM;MACJC,YAAY;MACZC,MAAM,GAAG;QAAA,IAAC;UAAErB,MAAM;UAAEK;QAA8B,CAAC;QAAA,oBACjD,oBAAC,MAAM,eACDA,OAAO;UACX,MAAM,EAAEL,MAAO;UACf,KAAK,EAAErC,cAAc,CAAC0C,OAAO,EAAEU,KAAK,CAACO,IAAI;QAAE,GAC3C;MAAA,CACH;MACDC,WAAW;MACXC,qBAAqB;MACrBC;IACF,CAAC,GAAGT,UAAU,CAACX,OAAO;IAEtB,oBACE,oBAAC,WAAW;MACV,GAAG,EAAEU,KAAK,CAACzB,GAAI;MACf,KAAK,EAAE,CAACrB,UAAU,CAACyD,YAAY,EAAE;QAAEC,MAAM,EAAER,SAAS,GAAG,CAAC,GAAG,CAAC;MAAE,CAAC,CAAE;MACjE,OAAO,EAAEA,SAAU;MACnB,OAAO,EAAEnC,qBAAsB;MAC/B,YAAY,EAAEoC;IAAa,gBAE3B,oBAAC,yBAAyB,CAAC,QAAQ;MAAC,KAAK,EAAEtB;IAAa,gBACtD,oBAAC,MAAM;MACL,OAAO,EAAEqB,SAAU;MACnB,KAAK,EAAEH,UAAU,CAACD,KAAM;MACxB,UAAU,EAAEC,UAAU,CAACnC,UAAW;MAClC,WAAW,EAAE0C,WAAY;MACzB,qBAAqB,EAAEC,qBAAsB;MAC7C,iBAAiB,EAAEC,iBAAkB;MACrC,MAAM,EAAEJ,MAAM,CAAC;QACbrB,MAAM,EAAEL,UAAU;QAClBoB,KAAK,EAAEC,UAAU,CAACD,KAAK;QACvBlC,UAAU,EACRmC,UAAU,CAACnC,UAAoD;QACjEwB,OAAO,EAAEW,UAAU,CAACX;MACtB,CAAC,CAAE;MACH,KAAK,EAAEnB;IAAoB,GAE1B8B,UAAU,CAACY,MAAM,EAAE,CACb,CAC0B,CACzB;EAElB,CAAC,CAAC,CACmB,eACvB,oBAAC,iCAAiC,CAAC,QAAQ;IAAC,KAAK,EAAE7B;EAAgB,GAChEQ,YAAY,EAAE,CAC4B,CACtB;AAE7B;AAEA,MAAMK,MAAM,GAAG3C,UAAU,CAAC4D,MAAM,CAAC;EAC/BhB,SAAS,EAAE;IACTiB,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC"}