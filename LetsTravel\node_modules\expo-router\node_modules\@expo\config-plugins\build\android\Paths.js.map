{"version": 3, "file": "Paths.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_fs", "_glob", "path", "_interopRequireWildcard", "_errors", "_modules", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "obj", "getProjectFilePath", "projectRoot", "name", "filePath", "globSync", "join", "assert", "getLanguage", "extension", "extname", "UnexpectedError", "getFileInfo", "normalize", "contents", "fs", "readFileSync", "language", "getMainApplicationAsync", "getMainActivityAsync", "getGradleFilePath", "gradleName", "groo<PERSON><PERSON><PERSON>", "resolve", "ktPath", "isGroovy", "existsSync", "isKotlin", "Error", "getProjectBuildGradleFilePath", "getProjectBuildGradleAsync", "getSettingsGradleFilePath", "getSettingsGradleAsync", "getAppBuildGradleFilePath", "getAppBuildGradleAsync", "getProjectPathOrThrowAsync", "projectPath", "directoryExistsAsync", "getAndroidManifestAsync", "getResourceFolderAsync", "getResourceXMLPathAsync", "kind", "resourcePath"], "sources": ["../../src/android/Paths.ts"], "sourcesContent": ["import assert from 'assert';\nimport fs from 'fs';\nimport { sync as globSync } from 'glob';\nimport * as path from 'path';\n\nimport { ResourceKind } from './Resources';\nimport { UnexpectedError } from '../utils/errors';\nimport { directoryExistsAsync } from '../utils/modules';\n\nexport interface ProjectFile<L extends string = string> {\n  path: string;\n  language: L;\n  contents: string;\n}\n\nexport type ApplicationProjectFile = ProjectFile<'java' | 'kt'>;\nexport type GradleProjectFile = ProjectFile<'groovy' | 'kt'>;\n\nexport function getProjectFilePath(projectRoot: string, name: string): string {\n  const filePath = globSync(\n    path.join(projectRoot, `android/app/src/main/java/**/${name}.@(java|kt)`)\n  )[0];\n  assert(\n    filePath,\n    `Project file \"${name}\" does not exist in android project for root \"${projectRoot}\"`\n  );\n\n  return filePath;\n}\n\nfunction getLanguage(filePath: string): 'java' | 'groovy' | 'kt' {\n  const extension = path.extname(filePath);\n  switch (extension) {\n    case '.java':\n      return 'java';\n    case '.kts':\n    case '.kt':\n      return 'kt';\n    case '.groovy':\n    case '.gradle':\n      return 'groovy';\n    default:\n      throw new UnexpectedError(`Unexpected Android file extension: ${extension}`);\n  }\n}\n\nexport function getFileInfo(filePath: string) {\n  return {\n    path: path.normalize(filePath),\n    contents: fs.readFileSync(filePath, 'utf8'),\n    language: getLanguage(filePath) as any,\n  };\n}\n\nexport async function getMainApplicationAsync(\n  projectRoot: string\n): Promise<ApplicationProjectFile> {\n  const filePath = getProjectFilePath(projectRoot, 'MainApplication');\n  return getFileInfo(filePath);\n}\n\nexport async function getMainActivityAsync(projectRoot: string): Promise<ApplicationProjectFile> {\n  const filePath = getProjectFilePath(projectRoot, 'MainActivity');\n  return getFileInfo(filePath);\n}\n\nexport function getGradleFilePath(projectRoot: string, gradleName: string): string {\n  const groovyPath = path.resolve(projectRoot, `${gradleName}.gradle`);\n  const ktPath = path.resolve(projectRoot, `${gradleName}.gradle.kts`);\n\n  const isGroovy = fs.existsSync(groovyPath);\n  const isKotlin = !isGroovy && fs.existsSync(ktPath);\n\n  if (!isGroovy && !isKotlin) {\n    throw new Error(`Failed to find '${gradleName}.gradle' file for project: ${projectRoot}.`);\n  }\n  const filePath = isGroovy ? groovyPath : ktPath;\n  return filePath;\n}\n\nexport function getProjectBuildGradleFilePath(projectRoot: string): string {\n  return getGradleFilePath(path.join(projectRoot, 'android'), 'build');\n}\n\nexport async function getProjectBuildGradleAsync(projectRoot: string): Promise<GradleProjectFile> {\n  return getFileInfo(getProjectBuildGradleFilePath(projectRoot));\n}\n\nexport function getSettingsGradleFilePath(projectRoot: string): string {\n  return getGradleFilePath(path.join(projectRoot, 'android'), 'settings');\n}\n\nexport async function getSettingsGradleAsync(projectRoot: string): Promise<GradleProjectFile> {\n  return getFileInfo(getSettingsGradleFilePath(projectRoot));\n}\n\nexport function getAppBuildGradleFilePath(projectRoot: string): string {\n  return getGradleFilePath(path.join(projectRoot, 'android', 'app'), 'build');\n}\n\nexport async function getAppBuildGradleAsync(projectRoot: string): Promise<GradleProjectFile> {\n  return getFileInfo(getAppBuildGradleFilePath(projectRoot));\n}\n\nexport async function getProjectPathOrThrowAsync(projectRoot: string): Promise<string> {\n  const projectPath = path.join(projectRoot, 'android');\n  if (await directoryExistsAsync(projectPath)) {\n    return projectPath;\n  }\n  throw new Error(`Android project folder is missing in project: ${projectRoot}`);\n}\n\nexport async function getAndroidManifestAsync(projectRoot: string): Promise<string> {\n  const projectPath = await getProjectPathOrThrowAsync(projectRoot);\n  const filePath = path.join(projectPath, 'app/src/main/AndroidManifest.xml');\n  return filePath;\n}\n\nexport async function getResourceFolderAsync(projectRoot: string): Promise<string> {\n  const projectPath = await getProjectPathOrThrowAsync(projectRoot);\n  return path.join(projectPath, `app/src/main/res`);\n}\n\nexport async function getResourceXMLPathAsync(\n  projectRoot: string,\n  { kind = 'values', name }: { kind?: ResourceKind; name: 'colors' | 'strings' | 'styles' | string }\n): Promise<string> {\n  const resourcePath = await getResourceFolderAsync(projectRoot);\n\n  const filePath = path.join(resourcePath, `${kind}/${name}.xml`);\n  return filePath;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,KAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,SAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,QAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAwD,SAAAS,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAjB,uBAAA6B,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAhB,UAAA,GAAAgB,GAAA,KAAAf,OAAA,EAAAe,GAAA;AAWjD,SAASC,kBAAkBA,CAACC,WAAmB,EAAEC,IAAY,EAAU;EAC5E,MAAMC,QAAQ,GAAG,IAAAC,YAAQ,EACvB9B,IAAI,CAAD,CAAC,CAAC+B,IAAI,CAACJ,WAAW,EAAE,gCAAgCC,IAAI,aAAa,CAC1E,CAAC,CAAC,CAAC,CAAC;EACJ,IAAAI,iBAAM,EACJH,QAAQ,EACR,iBAAiBD,IAAI,iDAAiDD,WAAW,GACnF,CAAC;EAED,OAAOE,QAAQ;AACjB;AAEA,SAASI,WAAWA,CAACJ,QAAgB,EAA4B;EAC/D,MAAMK,SAAS,GAAGlC,IAAI,CAAD,CAAC,CAACmC,OAAO,CAACN,QAAQ,CAAC;EACxC,QAAQK,SAAS;IACf,KAAK,OAAO;MACV,OAAO,MAAM;IACf,KAAK,MAAM;IACX,KAAK,KAAK;MACR,OAAO,IAAI;IACb,KAAK,SAAS;IACd,KAAK,SAAS;MACZ,OAAO,QAAQ;IACjB;MACE,MAAM,KAAIE,yBAAe,EAAC,sCAAsCF,SAAS,EAAE,CAAC;EAChF;AACF;AAEO,SAASG,WAAWA,CAACR,QAAgB,EAAE;EAC5C,OAAO;IACL7B,IAAI,EAAEA,IAAI,CAAD,CAAC,CAACsC,SAAS,CAACT,QAAQ,CAAC;IAC9BU,QAAQ,EAAEC,aAAE,CAACC,YAAY,CAACZ,QAAQ,EAAE,MAAM,CAAC;IAC3Ca,QAAQ,EAAET,WAAW,CAACJ,QAAQ;EAChC,CAAC;AACH;AAEO,eAAec,uBAAuBA,CAC3ChB,WAAmB,EACc;EACjC,MAAME,QAAQ,GAAGH,kBAAkB,CAACC,WAAW,EAAE,iBAAiB,CAAC;EACnE,OAAOU,WAAW,CAACR,QAAQ,CAAC;AAC9B;AAEO,eAAee,oBAAoBA,CAACjB,WAAmB,EAAmC;EAC/F,MAAME,QAAQ,GAAGH,kBAAkB,CAACC,WAAW,EAAE,cAAc,CAAC;EAChE,OAAOU,WAAW,CAACR,QAAQ,CAAC;AAC9B;AAEO,SAASgB,iBAAiBA,CAAClB,WAAmB,EAAEmB,UAAkB,EAAU;EACjF,MAAMC,UAAU,GAAG/C,IAAI,CAAD,CAAC,CAACgD,OAAO,CAACrB,WAAW,EAAE,GAAGmB,UAAU,SAAS,CAAC;EACpE,MAAMG,MAAM,GAAGjD,IAAI,CAAD,CAAC,CAACgD,OAAO,CAACrB,WAAW,EAAE,GAAGmB,UAAU,aAAa,CAAC;EAEpE,MAAMI,QAAQ,GAAGV,aAAE,CAACW,UAAU,CAACJ,UAAU,CAAC;EAC1C,MAAMK,QAAQ,GAAG,CAACF,QAAQ,IAAIV,aAAE,CAACW,UAAU,CAACF,MAAM,CAAC;EAEnD,IAAI,CAACC,QAAQ,IAAI,CAACE,QAAQ,EAAE;IAC1B,MAAM,IAAIC,KAAK,CAAC,mBAAmBP,UAAU,8BAA8BnB,WAAW,GAAG,CAAC;EAC5F;EACA,MAAME,QAAQ,GAAGqB,QAAQ,GAAGH,UAAU,GAAGE,MAAM;EAC/C,OAAOpB,QAAQ;AACjB;AAEO,SAASyB,6BAA6BA,CAAC3B,WAAmB,EAAU;EACzE,OAAOkB,iBAAiB,CAAC7C,IAAI,CAAD,CAAC,CAAC+B,IAAI,CAACJ,WAAW,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;AACtE;AAEO,eAAe4B,0BAA0BA,CAAC5B,WAAmB,EAA8B;EAChG,OAAOU,WAAW,CAACiB,6BAA6B,CAAC3B,WAAW,CAAC,CAAC;AAChE;AAEO,SAAS6B,yBAAyBA,CAAC7B,WAAmB,EAAU;EACrE,OAAOkB,iBAAiB,CAAC7C,IAAI,CAAD,CAAC,CAAC+B,IAAI,CAACJ,WAAW,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC;AACzE;AAEO,eAAe8B,sBAAsBA,CAAC9B,WAAmB,EAA8B;EAC5F,OAAOU,WAAW,CAACmB,yBAAyB,CAAC7B,WAAW,CAAC,CAAC;AAC5D;AAEO,SAAS+B,yBAAyBA,CAAC/B,WAAmB,EAAU;EACrE,OAAOkB,iBAAiB,CAAC7C,IAAI,CAAD,CAAC,CAAC+B,IAAI,CAACJ,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC;AAC7E;AAEO,eAAegC,sBAAsBA,CAAChC,WAAmB,EAA8B;EAC5F,OAAOU,WAAW,CAACqB,yBAAyB,CAAC/B,WAAW,CAAC,CAAC;AAC5D;AAEO,eAAeiC,0BAA0BA,CAACjC,WAAmB,EAAmB;EACrF,MAAMkC,WAAW,GAAG7D,IAAI,CAAD,CAAC,CAAC+B,IAAI,CAACJ,WAAW,EAAE,SAAS,CAAC;EACrD,IAAI,MAAM,IAAAmC,+BAAoB,EAACD,WAAW,CAAC,EAAE;IAC3C,OAAOA,WAAW;EACpB;EACA,MAAM,IAAIR,KAAK,CAAC,iDAAiD1B,WAAW,EAAE,CAAC;AACjF;AAEO,eAAeoC,uBAAuBA,CAACpC,WAAmB,EAAmB;EAClF,MAAMkC,WAAW,GAAG,MAAMD,0BAA0B,CAACjC,WAAW,CAAC;EACjE,MAAME,QAAQ,GAAG7B,IAAI,CAAD,CAAC,CAAC+B,IAAI,CAAC8B,WAAW,EAAE,kCAAkC,CAAC;EAC3E,OAAOhC,QAAQ;AACjB;AAEO,eAAemC,sBAAsBA,CAACrC,WAAmB,EAAmB;EACjF,MAAMkC,WAAW,GAAG,MAAMD,0BAA0B,CAACjC,WAAW,CAAC;EACjE,OAAO3B,IAAI,CAAD,CAAC,CAAC+B,IAAI,CAAC8B,WAAW,EAAE,kBAAkB,CAAC;AACnD;AAEO,eAAeI,uBAAuBA,CAC3CtC,WAAmB,EACnB;EAAEuC,IAAI,GAAG,QAAQ;EAAEtC;AAA8E,CAAC,EACjF;EACjB,MAAMuC,YAAY,GAAG,MAAMH,sBAAsB,CAACrC,WAAW,CAAC;EAE9D,MAAME,QAAQ,GAAG7B,IAAI,CAAD,CAAC,CAAC+B,IAAI,CAACoC,YAAY,EAAE,GAAGD,IAAI,IAAItC,IAAI,MAAM,CAAC;EAC/D,OAAOC,QAAQ;AACjB", "ignoreList": []}