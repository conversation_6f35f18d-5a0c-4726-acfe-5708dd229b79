{"version": 3, "file": "statusbar.js", "sourceRoot": "", "sources": ["../../src/utils/statusbar.ts"], "names": [], "mappings": ";;;;;;AAAA,oEAAuC;AACvC,+CAAwC;AAE3B,QAAA,yCAAyC,GACpD,uBAAQ,CAAC,EAAE,KAAK,KAAK;IACrB,CAAC,CAAC,wBAAS,CAAC,UAAU,EAAE,GAAG,EAAE,SAAS,EAAE,wCAAwC,CAAC", "sourcesContent": ["import Constants from 'expo-constants';\nimport { Platform } from 'react-native';\n\nexport const hasViewControllerBasedStatusBarAppearance =\n  Platform.OS === 'ios' &&\n  !!Constants.expoConfig?.ios?.infoPlist?.UIViewControllerBasedStatusBarAppearance;\n"]}