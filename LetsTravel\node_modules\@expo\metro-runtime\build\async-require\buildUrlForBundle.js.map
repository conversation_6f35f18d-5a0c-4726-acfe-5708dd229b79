{"version": 3, "file": "buildUrlForBundle.js", "sourceRoot": "", "sources": ["../../src/async-require/buildUrlForBundle.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,UAAkB;IAClD,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;QACpC,OAAO,UAAU,CAAC;KACnB;IACD,iFAAiF;IACjF,kFAAkF;IAClF,OAAO,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAC9C,CAAC;AAPD,8CAOC", "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n/**\n * Given a path and some optional additional query parameters, create the dev server bundle URL.\n * @param bundlePath like `/foobar`\n * @param params like `{ platform: \"web\" }`\n * @returns a URL like \"/foobar.bundle?platform=android&modulesOnly=true&runModule=false&runtimeBytecodeVersion=null\"\n */\nexport function buildUrlForBundle(bundlePath: string): string {\n  if (bundlePath.match(/^https?:\\/\\//)) {\n    return bundlePath;\n  }\n  // NOTE(EvanBacon): This must come from the window origin (at least in dev mode).\n  // Otherwise Metro will crash from attempting to load a bundle that doesn't exist.\n  return '/' + bundlePath.replace(/^\\/+/, '');\n}\n"]}