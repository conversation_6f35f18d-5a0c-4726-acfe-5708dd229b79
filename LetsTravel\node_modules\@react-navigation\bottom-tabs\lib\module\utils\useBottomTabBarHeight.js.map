{"version": 3, "names": ["React", "BottomTabBarHeightContext", "useBottomTabBarHeight", "height", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useBottomTabBarHeight.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,yBAAyB,MAAM,6BAA6B;AAEnE,eAAe,SAASC,qBAAqB,GAAG;EAC9C,MAAMC,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACH,yBAAyB,CAAC;EAE1D,IAAIE,MAAM,KAAKE,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,2FAA2F,CAC5F;EACH;EAEA,OAAOH,MAAM;AACf"}