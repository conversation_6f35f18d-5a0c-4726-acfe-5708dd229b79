{"version": 3, "file": "Location.native.js", "sourceRoot": "", "sources": ["../../src/location/Location.native.ts"], "names": [], "mappings": ";AAAA,mCAAmC;AACnC,0EAA0E;;;AAE1E,MAAM,YAAa,SAAQ,KAAK;IAC9B,YAAY,OAAe,EAAE,IAAY;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;CACF;AAED,6EAA6E;AAC7E,uEAAuE;AACvE,gCAAgC;AAChC,+EAA+E;AAC/E,uDAAuD;AACvD,MAAM,QAAQ;IACZ,YAAY,OAAsB,IAAI;QACpC,MAAM,GAAG,GAAG,IAAI,GAAG;QACjB,mBAAmB;QACnB,IAAI,CACL,CAAC;QAEF,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC;QAElB,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,IAAI,EAAE;gBACJ,GAAG;oBACD,OAAO,GAAG,CAAC,IAAI,CAAC;gBAClB,CAAC;gBACD,GAAG;oBACD,MAAM,IAAI,YAAY,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC;gBAC7E,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,IAAI,EAAE;gBACJ,GAAG;oBACD,OAAO,GAAG,CAAC,IAAI,CAAC;gBAClB,CAAC;gBACD,GAAG;oBACD,MAAM,IAAI,YAAY,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC;gBAC7E,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,QAAQ,EAAE;gBACR,GAAG;oBACD,OAAO,GAAG,CAAC,QAAQ,CAAC;gBACtB,CAAC;gBACD,GAAG;oBACD,MAAM,IAAI,YAAY,CAAC,iCAAiC,EAAE,mBAAmB,CAAC,CAAC;gBACjF,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,IAAI,EAAE;gBACJ,GAAG;oBACD,OAAO,GAAG,CAAC,IAAI,CAAC;gBAClB,CAAC;gBACD,GAAG;oBACD,MAAM,IAAI,YAAY,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC;gBAC7E,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,MAAM,EAAE;gBACN,GAAG;oBACD,OAAO,GAAG,CAAC,MAAM,CAAC;gBACpB,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,QAAQ,EAAE;gBACR,GAAG;oBACD,OAAO,GAAG,CAAC,QAAQ,CAAC;gBACtB,CAAC;gBACD,GAAG;oBACD,MAAM,IAAI,YAAY,CAAC,iCAAiC,EAAE,mBAAmB,CAAC,CAAC;gBACjF,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,IAAI,EAAE;gBACJ,GAAG;oBACD,OAAO,GAAG,CAAC,IAAI,CAAC;gBAClB,CAAC;gBACD,GAAG;oBACD,MAAM,IAAI,YAAY,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC;gBAC7E,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,QAAQ,EAAE;gBACR,GAAG;oBACD,OAAO,GAAG,CAAC,QAAQ,CAAC;gBACtB,CAAC;gBACD,GAAG;oBACD,MAAM,IAAI,YAAY,CAAC,iCAAiC,EAAE,mBAAmB,CAAC,CAAC;gBACjF,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,MAAM,EAAE;gBACN,GAAG;oBACD,OAAO,GAAG,CAAC,MAAM,CAAC;gBACpB,CAAC;gBACD,GAAG;oBACD,MAAM,IAAI,YAAY,CAAC,+BAA+B,EAAE,mBAAmB,CAAC,CAAC;gBAC/E,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,eAAe,EAAE;gBACf,GAAG;oBACD,OAAO;wBACL,MAAM,EAAE,CAAC;wBACT,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI;wBAChB,QAAQ,EAAE,GAAG,EAAE,CAAC,KAAK;qBACtB,CAAC;gBACJ,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,SAAS,MAAM;oBACpB,MAAM,IAAI,YAAY,CAAC,kCAAkC,EAAE,mBAAmB,CAAC,CAAC;gBAClF,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,SAAS,MAAM;oBACpB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;wBACzC,8FAA8F;wBAC9F,mGAAmG;wBACnG,wGAAwG;wBACxG,6EAA6E;wBAC7E,MAAM,WAAW,GAAI,OAAO,CAAC,cAAc,CAAmC;6BAC3E,WAAW,CAAC;wBACf,OAAO,WAAW,CAAC,MAAM,EAAE,CAAC;qBAC7B;yBAAM;wBACL,MAAM,IAAI,YAAY,CAAC,kCAAkC,EAAE,mBAAmB,CAAC,CAAC;qBACjF;gBACH,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,SAAS,OAAO;oBACrB,MAAM,IAAI,YAAY,CAAC,mCAAmC,EAAE,mBAAmB,CAAC,CAAC;gBACnF,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,SAAS,QAAQ;oBACtB,OAAO,GAAG,CAAC,IAAI,CAAC;gBAClB,CAAC;gBACD,UAAU,EAAE,IAAI;aACjB;YACD,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC,EAAE;gBACzC,KAAK,CAAC,OAAY;oBAChB,MAAM,MAAM,GAAG;wBACb,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;qBACpB,CAAC;oBACF,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvD,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,EAAE;IAC1C,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE;QACnB,KAAK,EAAE,UAAU;QACjB,YAAY,EAAE,IAAI;KACnB;CACF,CAAC,CAAC;AAEH,IAAI,QAAQ,GAAyB,SAAS,CAAC;AAE/C,SAAgB,eAAe,CAAC,IAAY;IAC1C,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC;AAFD,0CAEC;AAED,SAAgB,OAAO;IACrB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,EAAE;QACxC,KAAK,EAAE,QAAQ;QACf,YAAY,EAAE,IAAI;QAClB,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;IAEH,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,EAAE;QACxC,GAAG;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,GAAG;YACD,MAAM,IAAI,YAAY,CAAC,wBAAwB,EAAE,mBAAmB,CAAC,CAAC;QACxE,CAAC;QACD,UAAU,EAAE,IAAI;KACjB,CAAC,CAAC;AACL,CAAC;AAhBD,0BAgBC", "sourcesContent": ["// Copyright © 2023 650 Industries.\n// Copyright 2018-2023 the Deno authors. All rights reserved. MIT license.\n\nclass DOMException extends Error {\n  constructor(message: string, name: string) {\n    super(message);\n    this.name = name;\n  }\n}\n\n// The differences between the definitions of `Location` and `WorkerLocation`\n// are because of the `LegacyUnforgeable` attribute only specified upon\n// `Location`'s properties. See:\n// - https://html.spec.whatwg.org/multipage/history.html#the-location-interface\n// - https://heycam.github.io/webidl/#LegacyUnforgeable\nclass Location {\n  constructor(href: string | null = null) {\n    const url = new URL(\n      // @ts-expect-error\n      href\n    );\n\n    url.username = '';\n\n    url.password = '';\n    Object.defineProperties(this, {\n      hash: {\n        get() {\n          return url.hash;\n        },\n        set() {\n          throw new DOMException(`Cannot set \"location.hash\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      host: {\n        get() {\n          return url.host;\n        },\n        set() {\n          throw new DOMException(`Cannot set \"location.host\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      hostname: {\n        get() {\n          return url.hostname;\n        },\n        set() {\n          throw new DOMException(`Cannot set \"location.hostname\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      href: {\n        get() {\n          return url.href;\n        },\n        set() {\n          throw new DOMException(`Cannot set \"location.href\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      origin: {\n        get() {\n          return url.origin;\n        },\n        enumerable: true,\n      },\n      pathname: {\n        get() {\n          return url.pathname;\n        },\n        set() {\n          throw new DOMException(`Cannot set \"location.pathname\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      port: {\n        get() {\n          return url.port;\n        },\n        set() {\n          throw new DOMException(`Cannot set \"location.port\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      protocol: {\n        get() {\n          return url.protocol;\n        },\n        set() {\n          throw new DOMException(`Cannot set \"location.protocol\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      search: {\n        get() {\n          return url.search;\n        },\n        set() {\n          throw new DOMException(`Cannot set \"location.search\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      ancestorOrigins: {\n        get() {\n          return {\n            length: 0,\n            item: () => null,\n            contains: () => false,\n          };\n        },\n        enumerable: true,\n      },\n      assign: {\n        value: function assign() {\n          throw new DOMException(`Cannot call \"location.assign()\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      reload: {\n        value: function reload() {\n          if (process.env.NODE_ENV !== 'production') {\n            // NOTE: This does change how native fast refresh works. The upstream metro-runtime will check\n            // if `location.reload` exists before falling back on an implementation that is nearly identical to\n            // this. The main difference is that on iOS there is a \"reason\" message sent, but at the time of writing\n            // this, that message is unused (ref: `RCTTriggerReloadCommandNotification`).\n            const DevSettings = (require('react-native') as typeof import('react-native'))\n              .DevSettings;\n            return DevSettings.reload();\n          } else {\n            throw new DOMException(`Cannot call \"location.reload()\".`, 'NotSupportedError');\n          }\n        },\n        enumerable: true,\n      },\n      replace: {\n        value: function replace() {\n          throw new DOMException(`Cannot call \"location.replace()\".`, 'NotSupportedError');\n        },\n        enumerable: true,\n      },\n      toString: {\n        value: function toString() {\n          return url.href;\n        },\n        enumerable: true,\n      },\n      [Symbol.for('Expo.privateCustomInspect')]: {\n        value(inspect: any) {\n          const object = {\n            hash: this.hash,\n            host: this.host,\n            hostname: this.hostname,\n            href: this.href,\n            origin: this.origin,\n            pathname: this.pathname,\n            port: this.port,\n            protocol: this.protocol,\n            search: this.search,\n          };\n          return `${this.constructor.name} ${inspect(object)}`;\n        },\n      },\n    });\n  }\n}\n\nObject.defineProperties(Location.prototype, {\n  [Symbol.toString()]: {\n    value: 'Location',\n    configurable: true,\n  },\n});\n\nlet location: Location | undefined = undefined;\n\nexport function setLocationHref(href: string) {\n  location = new Location(href);\n}\n\nexport function install() {\n  Object.defineProperty(global, 'Location', {\n    value: Location,\n    configurable: true,\n    writable: true,\n  });\n\n  Object.defineProperty(window, 'location', {\n    get() {\n      return location;\n    },\n    set() {\n      throw new DOMException(`Cannot set \"location\".`, 'NotSupportedError');\n    },\n    enumerable: true,\n  });\n}\n"]}