import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Modal,
  FlatList,
  Alert,
  Platform,
  ActivityIndicator,
  SafeAreaView,
  Dimensions,
  Linking
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Design tokens
import { Colors, Spacing, Typography, BorderRadius } from '../../constants/DesignTokens';

// Import types
import type { CurrencyCode } from '../../services/types';

// Import services
import { databaseService } from '../../services/databaseService';
import { getCurrentLocation } from '../../services/locationService';
import { DEFAULT_COUNTRY } from '../../constants/Config';

// Rate info interface
interface RateInfo {
  buy: number;
  sell: number;
}

// Currency interface
interface Currency {
  code: CurrencyCode;
  name: string;
  symbol: string;
  unit: number;
}

// Exchange service interface
interface ExchangeService {
  id: string;
  name: string;
  logo?: string;
  location?: string;
  rates: Partial<Record<CurrencyCode, RateInfo>>;
  distance?: number;
  distanceInKm?: number;
  coords?: {
    latitude: number;
    longitude: number;
  };
  address?: string;
  isOpen: boolean;
  openHours?: string;
  lastUpdated?: number;
  phone?: string;
  branchIdentifier?: string;
  parentCompany?: string;
  country?: string;
  dataSource?: string;
  lastVerified?: number;
  placeId?: string;
}

// Location info interface
interface LocationInfo {
  country: string;
  currencyCode?: CurrencyCode;
  coords?: {
    latitude: number;
    longitude: number;
    accuracy?: number;
  };
  address?: string;
}

// Conversion result interface
interface ConversionResult {
  amount: number;
  buyRate: number;
  sellRate: number;
  service: ExchangeService;
  savingsPercent?: number;
}

const { width } = Dimensions.get('window');

export default function ExchangeScreen() {
  // State management
  const [fromCurrency, setFromCurrency] = useState<CurrencyCode>('MYR');
  const [toCurrency, setToCurrency] = useState<CurrencyCode>('SGD');
  const [amount, setAmount] = useState<string>('');
  const [locationInfo, setLocationInfo] = useState<LocationInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [conversionResults, setConversionResults] = useState<ConversionResult[]>([]);
  const [isCurrencyModalVisible, setIsCurrencyModalVisible] = useState(false);
  const [isFromCurrencySelector, setIsFromCurrencySelector] = useState(true);
  const [bestExchangeRate, setBestExchangeRate] = useState<{ service: ExchangeService; rate: number } | null>(null);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [searchRadius, setSearchRadius] = useState<number>(25);
  const [sortOrder, setSortOrder] = useState<'distance' | 'rate'>('distance');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [resultLimit, setResultLimit] = useState<number>(20);

  // Define currencies
  const currencies: Currency[] = [
    { code: 'MYR', name: 'Malaysian Ringgit', symbol: 'RM', unit: 1 },
    { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$', unit: 1 },
    { code: 'USD', name: 'US Dollar', symbol: '$', unit: 1 },
    { code: 'EUR', name: 'Euro', symbol: '€', unit: 1 },
    { code: 'GBP', name: 'British Pound', symbol: '£', unit: 1 },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥', unit: 100 },
    { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', unit: 1 },
    { code: 'THB', name: 'Thai Baht', symbol: '฿', unit: 1 },
    { code: 'IDR', name: 'Indonesian Rupiah', symbol: 'Rp', unit: 1000 },
    { code: 'KRW', name: 'South Korean Won', symbol: '₩', unit: 1000 },
    { code: 'INR', name: 'Indian Rupee', symbol: '₹', unit: 1 },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', unit: 1 },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', unit: 1 },
    { code: 'CHF', name: 'Swiss Franc', symbol: 'Fr', unit: 1 },
    { code: 'HKD', name: 'Hong Kong Dollar', symbol: 'HK$', unit: 1 },
    { code: 'NZD', name: 'New Zealand Dollar', symbol: 'NZ$', unit: 1 },
  ];

  // Initialize location
  useEffect(() => {
    const getLocation = async () => {
      try {
        const location = await getCurrentLocation();
        setLocationInfo({
          country: DEFAULT_COUNTRY,
          currencyCode: 'MYR' as CurrencyCode,
          coords: location?.coords ? {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy || undefined,
          } : undefined,
        });
      } catch (error) {
        console.warn('Error getting location:', error);
        setLocationInfo({
          country: DEFAULT_COUNTRY,
          currencyCode: 'MYR' as CurrencyCode,
        });
      }
    };

    getLocation();
  }, []);

  // Main exchange button handler
  const handleExchangeButtonPress = async () => {
    const amountNum = parseFloat(amount);
    if (!amountNum || amountNum <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid amount');
      return;
    }

    setIsLoading(true);
    
    try {
      // Fetch services from database
      const services = await databaseService.getExchangeServicesByCountry(
        locationInfo?.country || DEFAULT_COUNTRY
      );
      
      if (services && services.length > 0) {
        const results: ConversionResult[] = services.map(service => {
          const rate = getExchangeRate(service, fromCurrency, toCurrency);
          return {
            amount: rate ? amountNum * rate : 0,
            buyRate: rate || 0,
            sellRate: rate || 0,
            service,
          };
        }).filter(result => result.buyRate > 0);

        setConversionResults(results);
        setShowResults(true);
        
        // Find best rate
        if (results.length > 0) {
          const best = results.reduce((prev, current) => 
            (prev.buyRate > current.buyRate) ? prev : current
          );
          setBestExchangeRate({ service: best.service, rate: best.buyRate });
        }
      } else {
        Alert.alert('No Results', 'No exchange services found in your area');
      }
    } catch (error) {
      console.error('Error fetching exchange services:', error);
      Alert.alert('Error', 'Error loading exchange services');
    } finally {
      setIsLoading(false);
    }
  };

  // Get exchange rate from service
  const getExchangeRate = (
    service: ExchangeService,
    fromCurrency: CurrencyCode,
    toCurrency: CurrencyCode
  ): number | null => {
    const rates = service.rates;
    if (!rates) return null;

    // Try direct conversion
    const directRate = rates[toCurrency];
    if (directRate) {
      return directRate.buy;
    }

    // Try inverse conversion
    const inverseRate = rates[fromCurrency];
    if (inverseRate && inverseRate.sell > 0) {
      return 1 / inverseRate.sell;
    }

    return null;
  };

  // Get currency symbol
  const getCurrencySymbol = (code: CurrencyCode): string => {
    const currency = currencies.find(c => c.code === code);
    return currency?.symbol || code;
  };

  // Format distance
  const formatDistance = (distance: number) => {
    if (distance < 1000) {
      return `${Math.round(distance)}m`;
    } else {
      return `${(distance / 1000).toFixed(1)}km`;
    }
  };

  // Navigate to service
  const navigateToService = (service: ExchangeService) => {
    if (service.address) {
      const url = Platform.select({
        ios: `maps:0,0?q=${encodeURIComponent(service.address)}`,
        android: `geo:0,0?q=${encodeURIComponent(service.address)}`,
        default: `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(service.address)}`
      });
      
      if (url) {
        Linking.openURL(url);
      }
    }
  };

  // Call service
  const handleCallService = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`;
    Linking.openURL(url);
  };

  // Render currency item
  const renderCurrencyItem = (currency: Currency, type: 'from' | 'to') => (
    <TouchableOpacity
      key={currency.code}
      style={styles.currencyItem}
      onPress={() => {
        if (type === 'from') {
          setFromCurrency(currency.code);
        } else {
          setToCurrency(currency.code);
        }
        setIsCurrencyModalVisible(false);
      }}
    >
      <View style={styles.currencyItemContent}>
        <Text style={styles.currencyCode}>{currency.code}</Text>
        <Text style={styles.currencyName}>{currency.name}</Text>
      </View>
      <Text style={styles.currencySymbol}>{currency.symbol}</Text>
    </TouchableOpacity>
  );

  // Render result item
  const renderResultItem = ({ item, index }: { item: ConversionResult; index: number }) => {
    const { amount: resultAmount, buyRate, service } = item;
    
    return (
      <View style={[styles.resultItem, index % 2 === 0 ? styles.resultItemEven : styles.resultItemOdd]}>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceName}>{service.name}</Text>
          {service.address && (
            <Text style={styles.serviceAddress} numberOfLines={1}>{service.address}</Text>
          )}
          {service.distance && (
            <Text style={styles.serviceDistance}>{formatDistance(service.distance)}</Text>
          )}
        </View>
        
        <View style={styles.rateInfo}>
          <Text style={styles.rateValue}>{buyRate.toFixed(4)}</Text>
          <Text style={styles.rateLabel}>per {fromCurrency}</Text>
        </View>
        
        <View style={styles.resultInfo}>
          <Text style={styles.resultAmount}>{resultAmount.toFixed(2)}</Text>
          <Text style={styles.resultCurrency}>{toCurrency}</Text>
        </View>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigateToService(service)}
          >
            <Ionicons name="navigate" size={16} color={Colors.primary || '#007AFF'} />
          </TouchableOpacity>
          
          {service.phone && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleCallService(service.phone!)}
            >
              <Ionicons name="call" size={16} color={Colors.primary || '#007AFF'} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  // Filter results
  const filterResults = () => {
    const searchFiltered = searchTerm.trim() 
      ? conversionResults.filter(result => 
          (result.service.name || '').toLowerCase().includes(searchTerm.toLowerCase()) || 
          (result.service.address && result.service.address.toLowerCase().includes(searchTerm.toLowerCase()))
        )
      : conversionResults;
      
    return [...searchFiltered].sort((a, b) => {
      if (sortOrder === 'rate') {
        return (a.buyRate || Infinity) - (b.buyRate || Infinity);
      } else {
        return (a.service.distance || 999) - (b.service.distance || 999);
      }
    });
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors.backgroundDefault || '#f8f9fa' }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Exchange Rates</Text>
        <Text style={styles.headerSubtitle}>Find the best currency exchange rates</Text>
        
        {locationInfo && (
          <View style={styles.locationInfo}>
            <Ionicons name="location" size={16} color={Colors.primary || '#007AFF'} />
            <Text style={styles.locationText}>{locationInfo.country}</Text>
          </View>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Currency Converter Card */}
        <View style={styles.converterCard}>
          <Text style={styles.cardTitle}>Currency Converter</Text>
          
          {/* From Currency */}
          <View style={styles.currencySection}>
            <Text style={styles.currencyLabel}>From</Text>
            <TouchableOpacity
              style={styles.currencySelector}
              onPress={() => {
                setIsFromCurrencySelector(true);
                setIsCurrencyModalVisible(true);
              }}
            >
              <Text style={styles.currencySelectorText}>{fromCurrency}</Text>
              <Ionicons name="chevron-down" size={16} color={Colors.textSecondary || '#666'} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.amountInput}>
            <Text style={styles.currencySymbol}>{getCurrencySymbol(fromCurrency)}</Text>
            <TextInput
              style={styles.textInput}
              value={amount}
              onChangeText={setAmount}
              keyboardType="decimal-pad"
              placeholder="0.00"
              placeholderTextColor={Colors.textSecondary || '#999'}
            />
          </View>

          {/* To Currency */}
          <View style={styles.currencySection}>
            <Text style={styles.currencyLabel}>To</Text>
            <TouchableOpacity
              style={styles.currencySelector}
              onPress={() => {
                setIsFromCurrencySelector(false);
                setIsCurrencyModalVisible(true);
              }}
            >
              <Text style={styles.currencySelectorText}>{toCurrency}</Text>
              <Ionicons name="chevron-down" size={16} color={Colors.textSecondary || '#666'} />
            </TouchableOpacity>
          </View>

          {/* Exchange Button */}
          <TouchableOpacity
            style={[styles.exchangeButton, isLoading && styles.exchangeButtonDisabled]}
            onPress={handleExchangeButtonPress}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.exchangeButtonText}>Find Exchange Services</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Best Rate Card */}
        {bestExchangeRate && (
          <View style={styles.bestRateCard}>
            <View style={styles.bestRateHeader}>
              <Ionicons name="star" size={20} color={Colors.warning || '#FFA500'} />
              <Text style={styles.bestRateTitle}>Best Exchange Rate</Text>
            </View>
            <Text style={styles.bestRateService}>{bestExchangeRate.service.name}</Text>
            <Text style={styles.bestRateValue}>{bestExchangeRate.rate.toFixed(4)}</Text>
          </View>
        )}

        {/* Results */}
        {showResults && conversionResults.length > 0 && (
          <View style={styles.resultsCard}>
            <View style={styles.resultsHeader}>
              <Text style={styles.resultsTitle}>
                Exchange Services ({conversionResults.length})
              </Text>
              
              {/* Sort Controls */}
              <View style={styles.sortControls}>
                <TouchableOpacity
                  style={[styles.sortButton, sortOrder === 'distance' && styles.sortButtonActive]}
                  onPress={() => setSortOrder('distance')}
                >
                  <Text style={[styles.sortButtonText, sortOrder === 'distance' && styles.sortButtonTextActive]}>
                    Distance
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.sortButton, sortOrder === 'rate' && styles.sortButtonActive]}
                  onPress={() => setSortOrder('rate')}
                >
                  <Text style={[styles.sortButtonText, sortOrder === 'rate' && styles.sortButtonTextActive]}>
                    Rate
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Search Input */}
            <TextInput
              style={styles.searchInput}
              placeholder="Search by name or location..."
              value={searchTerm}
              onChangeText={setSearchTerm}
              placeholderTextColor={Colors.textSecondary || '#999'}
            />

            {/* Results List */}
            <FlatList
              data={filterResults().slice(0, resultLimit)}
              renderItem={renderResultItem}
              keyExtractor={(item, index) => `${item.service.id}-${index}`}
              scrollEnabled={false}
            />
          </View>
        )}
      </ScrollView>

      {/* Currency Selection Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={isCurrencyModalVisible}
        onRequestClose={() => setIsCurrencyModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Currency</Text>
              <TouchableOpacity onPress={() => setIsCurrencyModalVisible(false)}>
                <Ionicons name="close" size={24} color={Colors.textPrimary || '#000'} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.currencyList}>
              {currencies.map(currency => 
                renderCurrencyItem(currency, isFromCurrencySelector ? 'from' : 'to')
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6c757d',
    marginBottom: 12,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    color: '#007AFF',
    marginLeft: 4,
  },
  content: {
    flex: 1,
  },
  converterCard: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 20,
  },
  currencySection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  currencyLabel: {
    fontSize: 16,
    color: '#495057',
    fontWeight: '500',
  },
  currencySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#dee2e6',
    borderRadius: 8,
    minWidth: 80,
  },
  currencySelectorText: {
    fontSize: 16,
    color: '#212529',
    marginRight: 4,
  },
  amountInput: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#dee2e6',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 20,
  },
  currencySymbol: {
    fontSize: 18,
    color: '#495057',
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    fontSize: 18,
    paddingVertical: 12,
    color: '#212529',
  },
  exchangeButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  exchangeButtonDisabled: {
    backgroundColor: '#adb5bd',
  },
  exchangeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  bestRateCard: {
    backgroundColor: '#fff',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#FFA500',
  },
  bestRateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  bestRateTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
    marginLeft: 8,
  },
  bestRateService: {
    fontSize: 14,
    color: '#495057',
    marginBottom: 4,
  },
  bestRateValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFA500',
  },
  resultsCard: {
    backgroundColor: '#fff',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
  },
  sortControls: {
    flexDirection: 'row',
  },
  sortButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginLeft: 8,
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  sortButtonActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  sortButtonText: {
    fontSize: 12,
    color: '#495057',
  },
  sortButtonTextActive: {
    color: '#fff',
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#dee2e6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    marginBottom: 16,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  resultItemEven: {
    backgroundColor: '#fff',
  },
  resultItemOdd: {
    backgroundColor: '#f8f9fa',
  },
  serviceInfo: {
    flex: 2,
  },
  serviceName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#212529',
  },
  serviceAddress: {
    fontSize: 12,
    color: '#6c757d',
    marginTop: 2,
  },
  serviceDistance: {
    fontSize: 12,
    color: '#007AFF',
    marginTop: 2,
  },
  rateInfo: {
    flex: 1,
    alignItems: 'center',
  },
  rateValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#212529',
  },
  rateLabel: {
    fontSize: 11,
    color: '#6c757d',
  },
  resultInfo: {
    flex: 1,
    alignItems: 'flex-end',
  },
  resultAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#28a745',
  },
  resultCurrency: {
    fontSize: 11,
    color: '#6c757d',
  },
  actionButtons: {
    flexDirection: 'row',
    marginLeft: 8,
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
  },
  currencyList: {
    flex: 1,
  },
  currencyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  currencyItemContent: {
    flex: 1,
  },
  currencyCode: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212529',
  },
  currencyName: {
    fontSize: 14,
    color: '#6c757d',
    marginTop: 2,
  },
  currencySymbol: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
}); 