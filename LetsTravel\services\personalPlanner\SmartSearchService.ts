// 🎨 Trekmate 4.0 - 智能搜索服务
// 基于MD计划文件的高性能搜索系统

import Fuse from 'fuse.js';
import { useQuery, useQueryClient } from 'react-query';

// 🎯 搜索结果类型定义
export interface SearchResult {
  id: string;
  name: string;
  type: 'attraction' | 'hotel' | 'restaurant' | 'transport' | 'custom';
  location: {
    name: string;
    address?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  rating?: number;
  priceLevel?: 1 | 2 | 3 | 4 | 5;
  description?: string;
  images?: string[];
  openingHours?: string;
  website?: string;
  phone?: string;
  tags?: string[];
  distance?: number; // 距离用户位置的公里数
}

// 🎯 搜索过滤器
export interface SearchFilter {
  type?: SearchResult['type'][];
  priceLevel?: number[];
  rating?: number;
  distance?: number;
  openNow?: boolean;
  tags?: string[];
}

// 🎯 搜索配置
interface SearchConfig {
  threshold: number; // 模糊匹配阈值
  keys: string[]; // 搜索字段
  includeScore: boolean;
  includeMatches: boolean;
  minMatchCharLength: number;
}

// 🎯 缓存配置
const CACHE_CONFIG = {
  SEARCH_RESULTS: 'search-results',
  POPULAR_SEARCHES: 'popular-searches',
  USER_HISTORY: 'user-search-history',
  TTL: 30 * 60 * 1000, // 30分钟缓存
};

export class SmartSearchService {
  private static instance: SmartSearchService;
  private fuseInstance: Fuse<SearchResult> | null = null;
  private searchData: SearchResult[] = [];
  private queryClient: any;

  // 🎯 单例模式
  static getInstance(): SmartSearchService {
    if (!SmartSearchService.instance) {
      SmartSearchService.instance = new SmartSearchService();
    }
    return SmartSearchService.instance;
  }

  // 🎯 初始化服务
  async initialize(queryClient: any): Promise<void> {
    this.queryClient = queryClient;
    await this.loadSearchData();
    this.initializeFuse();
  }

  // 🎯 加载搜索数据
  private async loadSearchData(): Promise<void> {
    try {
      // 从缓存获取数据
      const cachedData = this.queryClient?.getQueryData(CACHE_CONFIG.SEARCH_RESULTS);
      if (cachedData) {
        this.searchData = cachedData;
        return;
      }

      // 从API获取数据
      const data = await this.fetchSearchData();
      this.searchData = data;

      // 缓存数据
      this.queryClient?.setQueryData(CACHE_CONFIG.SEARCH_RESULTS, data, {
        staleTime: CACHE_CONFIG.TTL,
      });
    } catch (error) {
      console.error('加载搜索数据失败:', error);
      // 使用模拟数据作为降级
      this.searchData = this.getMockSearchData();
    }
  }

  // 🎯 初始化Fuse.js
  private initializeFuse(): void {
    const config: SearchConfig = {
      threshold: 0.3, // 模糊匹配阈值
      keys: [
        { name: 'name', weight: 0.4 },
        { name: 'location.name', weight: 0.3 },
        { name: 'location.address', weight: 0.2 },
        { name: 'tags', weight: 0.1 },
      ],
      includeScore: true,
      includeMatches: true,
      minMatchCharLength: 2,
    };

    this.fuseInstance = new Fuse(this.searchData, config);
  }

  // 🎯 智能搜索主方法
  async search(
    query: string,
    filters?: SearchFilter,
    userLocation?: { lat: number; lng: number }
  ): Promise<SearchResult[]> {
    if (!query.trim()) {
      return this.getPopularResults(filters);
    }

    try {
      // 执行模糊搜索
      const fuseResults = this.fuseInstance?.search(query) || [];
      
      // 转换结果格式
      let results = fuseResults.map(result => ({
        ...result.item,
        score: result.score,
        matches: result.matches,
      }));

      // 应用过滤器
      if (filters) {
        results = this.applyFilters(results, filters);
      }

      // 计算距离并排序
      if (userLocation) {
        results = this.calculateDistanceAndSort(results, userLocation);
      }

      // 记录搜索历史
      this.recordSearchHistory(query);

      return results.slice(0, 20); // 限制返回结果数量
    } catch (error) {
      console.error('搜索失败:', error);
      return [];
    }
  }

  // 🎯 获取搜索建议
  async getSuggestions(query: string, limit: number = 5): Promise<string[]> {
    if (!query.trim() || query.length < 2) {
      return this.getPopularSearches();
    }

    const results = await this.search(query);
    const suggestions = results
      .map(result => result.name)
      .filter((name, index, array) => array.indexOf(name) === index)
      .slice(0, limit);

    return suggestions;
  }

  // 🎯 应用过滤器
  private applyFilters(results: SearchResult[], filters: SearchFilter): SearchResult[] {
    return results.filter(result => {
      // 类型过滤
      if (filters.type && !filters.type.includes(result.type)) {
        return false;
      }

      // 价格等级过滤
      if (filters.priceLevel && result.priceLevel && 
          !filters.priceLevel.includes(result.priceLevel)) {
        return false;
      }

      // 评分过滤
      if (filters.rating && result.rating && result.rating < filters.rating) {
        return false;
      }

      // 距离过滤
      if (filters.distance && result.distance && result.distance > filters.distance) {
        return false;
      }

      // 标签过滤
      if (filters.tags && filters.tags.length > 0) {
        const resultTags = result.tags || [];
        const hasMatchingTag = filters.tags.some(tag => 
          resultTags.some(resultTag => 
            (resultTag || '').toLowerCase().includes((tag || '').toLowerCase())
          )
        );
        if (!hasMatchingTag) {
          return false;
        }
      }

      return true;
    });
  }

  // 🎯 计算距离并排序
  private calculateDistanceAndSort(
    results: SearchResult[], 
    userLocation: { lat: number; lng: number }
  ): SearchResult[] {
    return results
      .map(result => {
        if (result.location.coordinates) {
          const distance = this.calculateDistance(
            userLocation.lat,
            userLocation.lng,
            result.location.coordinates.latitude,
            result.location.coordinates.longitude
          );
          return { ...result, distance };
        }
        return result;
      })
      .sort((a, b) => {
        // 优先按评分排序，然后按距离
        if (a.rating && b.rating && Math.abs(a.rating - b.rating) > 0.5) {
          return b.rating - a.rating;
        }
        if (a.distance && b.distance) {
          return a.distance - b.distance;
        }
        return 0;
      });
  }

  // 🎯 计算两点间距离（哈弗辛公式）
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // 地球半径（公里）
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }

  // 🎯 获取热门搜索
  private getPopularSearches(): string[] {
    return [
      'Tokyo Tower',
      'Shibuya Crossing',
      'Senso-ji Temple',
      'Tsukiji Fish Market',
      'Mount Fuji',
    ];
  }

  // 🎯 获取热门结果
  private getPopularResults(filters?: SearchFilter): SearchResult[] {
    let popular = this.searchData
      .filter(item => item.rating && item.rating >= 4.0)
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, 10);

    if (filters) {
      popular = this.applyFilters(popular, filters);
    }

    return popular;
  }

  // 🎯 记录搜索历史
  private recordSearchHistory(query: string): void {
    try {
      const history = this.queryClient?.getQueryData(CACHE_CONFIG.USER_HISTORY) || [];
      const updatedHistory = [query, ...history.filter((h: string) => h !== query)].slice(0, 10);
      
      this.queryClient?.setQueryData(CACHE_CONFIG.USER_HISTORY, updatedHistory);
    } catch (error) {
      console.error('记录搜索历史失败:', error);
    }
  }

  // 🎯 从API获取搜索数据
  private async fetchSearchData(): Promise<SearchResult[]> {
    // 这里应该调用实际的API
    // 暂时返回模拟数据
    return this.getMockSearchData();
  }

  // 🎯 模拟搜索数据
  private getMockSearchData(): SearchResult[] {
    return [
      {
        id: '1',
        name: 'Tokyo Tower',
        type: 'attraction',
        location: {
          name: 'Minato City, Tokyo',
          address: '4 Chome-2-8 Shibakoen, Minato City, Tokyo 105-0011, Japan',
          coordinates: { latitude: 35.6586, longitude: 139.7454 },
        },
        rating: 4.2,
        priceLevel: 2,
        description: 'Iconic red and white tower offering city views',
        tags: ['landmark', 'observation deck', 'city views'],
      },
      // 更多模拟数据...
    ];
  }
}

// 🎯 React Hook for Smart Search
export const useSmartSearch = () => {
  const searchService = SmartSearchService.getInstance();
  
  return {
    search: searchService.search.bind(searchService),
    getSuggestions: searchService.getSuggestions.bind(searchService),
  };
};
