{"version": 3, "file": "EUCKRDecoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/euc-kr/EUCKRDecoder.ts"], "names": [], "mappings": ";;AACA,sDAAwD;AACxD,oDAAmD;AACnD,kDAAkE;AAClE,0DAAwE;AACxE,sDAAmD;AAEnD;;;;GAIG;AACH;IAME,sBAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAE3B,mEAAmE;QACnE,qBAAqB,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC/C,CAAC;IACD;;;;;;OAMG;IACH,8BAAO,GAAP,UAAQ,MAAc,EAAE,IAAY;QAClC,+DAA+D;QAC/D,wCAAwC;QACxC,IAAI,IAAI,KAAK,2BAAa,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;YACnD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,wBAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACjC;QAED,8DAA8D;QAC9D,YAAY;QACZ,IAAI,IAAI,KAAK,2BAAa,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC;YACjD,OAAO,mBAAQ,CAAC;QAElB,8DAA8D;QAC9D,+DAA+D;QAC/D,YAAY;QACZ,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;YAC7B,IAAI,OAAO,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YAEvB,0DAA0D;YAC1D,kDAAkD;YAClD,IAAI,mBAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;gBAC3B,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;YAEhD,yDAAyD;YACzD,0DAA0D;YAC1D,IAAM,UAAU,GAAG,CAAC,OAAO,KAAK,IAAI,CAAC;gBACnC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,2BAAiB,CAAC,OAAO,EAAE,eAAK,CAAC,QAAQ,CAAa,CAAC,CAAC;YAEnE,8DAA8D;YAC9D,kBAAkB;YAClB,IAAI,OAAO,KAAK,IAAI,IAAI,yBAAW,CAAC,IAAI,CAAC;gBACvC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEvB,0CAA0C;YAC1C,IAAI,UAAU,KAAK,IAAI;gBACrB,OAAO,wBAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAElC,oDAAoD;YACpD,OAAO,UAAU,CAAC;SACnB;QAED,+DAA+D;QAC/D,WAAW;QACX,IAAI,yBAAW,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI,CAAC;QAEd,0DAA0D;QAC1D,2CAA2C;QAC3C,IAAI,mBAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;YAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,IAAI,CAAC;SACb;QAED,mBAAmB;QACnB,OAAO,wBAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IACH,mBAAC;AAAD,CAAC,AA9ED,IA8EC;AA9EY,oCAAY"}