{"version": 3, "file": "AnsiHighlight.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/UI/AnsiHighlight.tsx"], "names": [], "mappings": ";;;;;;AAAA;;;;;;GAMG;AACH,kDAA0B;AAC1B,kDAA0B;AAC1B,+CAA4E;AAE5E,uDAAuD;AACvD,MAAM,MAAM,GAA2B;IACrC,YAAY,EAAE,iBAAiB;IAC/B,UAAU,EAAE,kBAAkB;IAC9B,YAAY,EAAE,mBAAmB;IACjC,aAAa,EAAE,oBAAoB;IACnC,WAAW,EAAE,oBAAoB;IACjC,cAAc,EAAE,oBAAoB;IACpC,WAAW,EAAE,oBAAoB;IACjC,oEAAoE;IACpE,sCAAsC;IACtC,mBAAmB,EAAE,iBAAiB;IACtC,iBAAiB,EAAE,kBAAkB;IACrC,mBAAmB,EAAE,mBAAmB;IACxC,oBAAoB,EAAE,oBAAoB;IAC1C,kBAAkB,EAAE,oBAAoB;IACxC,qBAAqB,EAAE,oBAAoB;IAC3C,kBAAkB,EAAE,oBAAoB;IACxC,mBAAmB,EAAE,oBAAoB;CAC1C,CAAC;AAEF,SAAgB,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAiD;IACjF,IAAI,sBAAsB,GAAG,QAAQ,CAAC;IACtC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAChD,eAAK,CAAC,UAAU,CAAC,IAAI,EAAE;QACrB,IAAI,EAAE,IAAI;QACV,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE,IAAI;KAClB,CAAC,CACH,CAAC;IAEF,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACxB,0EAA0E;QAC1E,8EAA8E;QAC9E,kDAAkD;QAClD,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1D,MAAM,gBAAgB,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,gBAAgB,GAAG,sBAAsB,EAAE;YAC7C,sBAAsB,GAAG,gBAAgB,CAAC;SAC3C;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,CAAC,OAAe,EAAE,GAAW,EAAE,EAAE;QAC/C,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,6CAA6C;YAC7C,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SACrC;aAAM,IAAI,GAAG,KAAK,CAAC,IAAI,sBAAsB,GAAG,QAAQ,EAAE;YACzD,wDAAwD;YACxD,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;SAC/C;aAAM;YACL,OAAO,OAAO,CAAC;SAChB;IACH,CAAC,CAAC;IAEF,OAAO,CACL,8BAAC,mBAAI,QACF,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAC7B,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,IAC7B,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;QACzB,MAAM,SAAS,GACb,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5B,CAAC,CAAC;gBACE,eAAe,EAAE,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/C,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;aACtC;YACH,CAAC,CAAC;gBACE,eAAe,EAAE,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;aAChD,CAAC;QACR,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,GAAG,IACtC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CACxB,CACR,CAAC;IACJ,CAAC,CAAC,CACG,CACR,CAAC,CACG,CACR,CAAC;AACJ,CAAC;AAzDD,oBAyDC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,EAAE;QACJ,aAAa,EAAE,KAAK;KACrB;CACF,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Anser from 'anser';\nimport React from 'react';\nimport { StyleProp, StyleSheet, Text, TextStyle, View } from 'react-native';\n\n// Afterglow theme from https://iterm2colorschemes.com/\nconst COLORS: Record<string, string> = {\n  'ansi-black': 'rgb(27, 27, 27)',\n  'ansi-red': 'rgb(187, 86, 83)',\n  'ansi-green': 'rgb(144, 157, 98)',\n  'ansi-yellow': 'rgb(234, 193, 121)',\n  'ansi-blue': 'rgb(125, 169, 199)',\n  'ansi-magenta': 'rgb(176, 101, 151)',\n  'ansi-cyan': 'rgb(140, 220, 216)',\n  // Instead of white, use the default color provided to the component\n  // 'ansi-white': 'rgb(216, 216, 216)',\n  'ansi-bright-black': 'rgb(98, 98, 98)',\n  'ansi-bright-red': 'rgb(187, 86, 83)',\n  'ansi-bright-green': 'rgb(144, 157, 98)',\n  'ansi-bright-yellow': 'rgb(234, 193, 121)',\n  'ansi-bright-blue': 'rgb(125, 169, 199)',\n  'ansi-bright-magenta': 'rgb(176, 101, 151)',\n  'ansi-bright-cyan': 'rgb(140, 220, 216)',\n  'ansi-bright-white': 'rgb(247, 247, 247)',\n};\n\nexport function Ansi({ text, style }: { text: string; style: StyleProp<TextStyle> }) {\n  let commonWhitespaceLength = Infinity;\n  const parsedLines = text.split(/\\n/).map((line) =>\n    Anser.ansiToJson(line, {\n      json: true,\n      remove_empty: true,\n      use_classes: true,\n    })\n  );\n\n  parsedLines.map((lines) => {\n    // The third item on each line includes the whitespace of the source code.\n    // We are looking for the least amount of common whitespace to trim all lines.\n    // Example: Array [\" \", \" 96 |\", \"     text\", ...]\n    const match = lines[2] && lines[2]?.content?.match(/^ +/);\n    const whitespaceLength = (match && match[0]?.length) || 0;\n    if (whitespaceLength < commonWhitespaceLength) {\n      commonWhitespaceLength = whitespaceLength;\n    }\n  });\n\n  const getText = (content: string, key: number) => {\n    if (key === 1) {\n      // Remove the vertical bar after line numbers\n      return content.replace(/\\| $/, ' ');\n    } else if (key === 2 && commonWhitespaceLength < Infinity) {\n      // Remove common whitespace at the beginning of the line\n      return content.substr(commonWhitespaceLength);\n    } else {\n      return content;\n    }\n  };\n\n  return (\n    <View>\n      {parsedLines.map((items, i) => (\n        <View style={styles.line} key={i}>\n          {items.map((bundle, key) => {\n            const textStyle =\n              bundle.fg && COLORS[bundle.fg]\n                ? {\n                    backgroundColor: bundle.bg && COLORS[bundle.bg],\n                    color: bundle.fg && COLORS[bundle.fg],\n                  }\n                : {\n                    backgroundColor: bundle.bg && COLORS[bundle.bg],\n                  };\n            return (\n              <Text style={[style, textStyle]} key={key}>\n                {getText(bundle.content, key)}\n              </Text>\n            );\n          })}\n        </View>\n      ))}\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  line: {\n    flexDirection: 'row',\n  },\n});\n"]}