{"version": 3, "file": "router-store.js", "sourceRoot": "", "sources": ["../../src/global-state/router-store.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAIkC;AAClC,oEAAuC;AACvC,iEAAmD;AACnD,iCAA+E;AAC/E,+CAAwC;AAExC,uCAWmB;AACnB,+CAAgD;AAChD,0DAAuE;AAEvE,+DAA2E;AAE3E,0DAAiG;AACjG,4CAAyC;AAEzC,8CAA2D;AAE3D;;;;GAIG;AACH,MAAa,WAAW;IACtB,SAAS,CAAoB;IAC7B,aAAa,CAAiB;IAC9B,OAAO,CAAsB;IACrB,wBAAwB,GAAY,KAAK,CAAC;IAElD,YAAY,CAAe;IAC3B,SAAS,CAAe;IACxB,SAAS,CAAe;IACxB,SAAS,CAAa;IACtB,0BAA0B,CAAU;IAEpC,aAAa,CAAoE;IACjF,yBAAyB,CAAc;IAEvC,oBAAoB,GAAG,IAAI,GAAG,EAAc,CAAC;IAC7C,gBAAgB,GAAG,IAAI,GAAG,EAAc,CAAC;IAEzC,MAAM,GAAG,gBAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,eAAe,GAAG,6BAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,GAAG,gBAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,SAAS,GAAG,mBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,IAAI,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvB,OAAO,GAAG,iBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,OAAO,GAAG,iBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,UAAU,GAAG,oBAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,UAAU,GAAG,oBAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,SAAS,GAAG,mBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,QAAQ,GAAG,kBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE/B,UAAU,CACR,OAAuB,EACvB,aAA+E,EAC/E,uBAA6C,EAAE;QAE/C,8BAA8B;QAC9B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;QACnC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAE9B,IAAI,CAAC,SAAS,GAAG,IAAA,qBAAS,EAAC,OAAO,EAAE;YAClC,GAAG,wBAAS,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM;YACtC,iBAAiB,EAAE,IAAI;YACvB,QAAQ,EAAE,uBAAQ,CAAC,EAAE;SACtB,CAAC,CAAC;QAEH,8EAA8E;QAC9E,gEAAgE;QAChE,8BAA8B;QAC9B,IAAI,CAAC,SAAS,GAAG;YACf,mBAAmB,EAAE,EAAE;YACvB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,mEAAmE;YACnE,IAAI,CAAC,OAAO,GAAG,IAAA,mCAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,oBAAoB,CAAC,CAAC;YACrF,IAAI,CAAC,aAAa,GAAG,IAAA,uCAA0B,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEhE,sHAAsH;YACtH,+EAA+E;YAC/E,qHAAqH;YACrH,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,EAAE,CAAC;YACnD,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;gBAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC;gBACnC,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBACpD;aACF;SACF;aAAM;YACL,8EAA8E;YAC9E,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;gBACzC,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;aACpC;YAED,qDAAqD;YACrD,IAAI,CAAC,aAAa,GAAG,gBAAQ,CAAC;SAC/B;QAED;;;;;;;;;;WAUG;QACH,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,yBAAyB,GAAG,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YAC3E,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAoB,CAAC;YAE7C,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAClC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;gBACrC,iGAAiG;gBACjG,IAAI,CAAC,0BAA0B,GAAG,qBAAqB,CAAC,GAAG,EAAE;oBAC3D,4EAA4E;oBAC5E,YAAY,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBAC5C,CAAC,CAAC,CAAC;aACJ;YAED,IAAI,uBAAuB,GAAG,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC;YACvD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAE3B,oFAAoF;YACpF,0FAA0F;YAC1F,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;gBACrC,aAAK,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBACpC,uBAAuB,GAAG,IAAI,CAAC;aAChC;YAED,2FAA2F;YAC3F,IAAI,uBAAuB,EAAE;gBAC3B,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,oBAAoB,EAAE;oBAClD,UAAU,EAAE,CAAC;iBACd;aACF;QACH,CAAC,CAAC,CAAC;QAEH,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC9C,UAAU,EAAE,CAAC;SACd;IACH,CAAC;IAED,WAAW,CAAC,KAAkB,EAAE,SAAS,GAAG,KAAK;QAC/C,aAAK,CAAC,SAAS,GAAG,KAAK,CAAC;QACxB,aAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAE5B,MAAM,aAAa,GAAG,aAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAA,4BAAS,EAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE;YAC7C,aAAK,CAAC,SAAS,GAAG,aAAa,CAAC;SACjC;IACH,CAAC;IAED,YAAY,CAAC,KAAkB;QAC7B,OAAO,IAAA,wCAAqB,EAC1B,CAAC,KAA6C,EAAE,MAAe,EAAE,EAAE;YACjE,OAAO,IAAA,uCAAoB,EAAC,KAAK,EAAE;gBACjC,OAAO,EAAE,EAAE;gBACX,GAAG,IAAI,CAAC,OAAO,EAAE,MAAM;gBACvB,qBAAqB,EAAE,MAAM;gBAC7B,cAAc,EAAE,MAAM;aACvB,CAAC,CAAC;QACL,CAAC,EACD,KAAK,CACN,CAAC;IACJ,CAAC;IAED,kEAAkE;IAClE,iEAAiE;IACjE,kBAAkB;QAChB,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;IACnE,CAAC;IAED,uEAAuE;IACvE,oBAAoB,GAAG,CAAC,UAAsB,EAAE,EAAE;QAChD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC1C,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAC5D,CAAC,CAAC;IACF,gBAAgB,GAAG,CAAC,UAAsB,EAAE,EAAE;QAC5C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtC,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACxD,CAAC,CAAC;IACF,QAAQ,GAAG,GAAG,EAAE;QACd,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IACF,iBAAiB,GAAG,GAAG,EAAE;QACvB,OAAO,IAAI,CAAC,SAAU,CAAC;IACzB,CAAC,CAAC;IACF,iBAAiB,GAAG,GAAG,EAAE;QACvB,OAAO,IAAI,CAAC,SAAU,CAAC;IACzB,CAAC,CAAC;IAEF,OAAO;QACL,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACnC,oBAAoB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;SACvD;IACH,CAAC;CACF;AA7LD,kCA6LC;AAEY,QAAA,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;AAEvC,SAAgB,aAAa;IAC3B,OAAO,IAAA,4BAAoB,EAAC,aAAK,CAAC,gBAAgB,EAAE,aAAK,CAAC,QAAQ,EAAE,aAAK,CAAC,QAAQ,CAAC,CAAC;AACtF,CAAC;AAFD,sCAEC;AAED,SAAS,kBAAkB;IACzB,IAAI,aAAK,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE;QACjC,MAAM,YAAY,GAAG,aAAK,CAAC,aAAa,CAAC,YAAY,EAA4B,CAAC;QAElF,IAAI,aAAK,CAAC,SAAS,KAAK,YAAY,EAAE;YACpC,aAAK,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;SACjC;KACF;AACH,CAAC;AAED,SAAgB,iBAAiB;IAC/B,kBAAkB,EAAE,CAAC;IACrB,OAAO,IAAA,4BAAoB,EACzB,aAAK,CAAC,oBAAoB,EAC1B,aAAK,CAAC,iBAAiB,EACvB,aAAK,CAAC,iBAAiB,CACxB,CAAC;AACJ,CAAC;AAPD,8CAOC;AAED,SAAgB,iBAAiB;IAC/B,kBAAkB,EAAE,CAAC;IACrB,OAAO,IAAA,4BAAoB,EACzB,aAAK,CAAC,oBAAoB,EAC1B,aAAK,CAAC,iBAAiB,EACvB,aAAK,CAAC,iBAAiB,CACxB,CAAC;AACJ,CAAC;AAPD,8CAOC;AAED,SAAgB,uBAAuB,CAAC,OAAuB,EAAE,OAA6B;IAC5F,MAAM,aAAa,GAAG,IAAA,kCAAyB,GAAE,CAAC;IAClD,IAAA,eAAO,EAAC,GAAG,EAAE,CAAC,aAAK,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5E,aAAa,EAAE,CAAC;IAChB,OAAO,aAAK,CAAC;AACf,CAAC;AALD,0DAKC", "sourcesContent": ["import {\n  NavigationContainerRefWithCurrent,\n  getPathFromState,\n  useNavigationContainerRef,\n} from '@react-navigation/native';\nimport Constants from 'expo-constants';\nimport * as SplashScreen from 'expo-splash-screen';\nimport { useSyncExternalStore, useMemo, ComponentType, Fragment } from 'react';\nimport { Platform } from 'react-native';\n\nimport {\n  canGoBack,\n  canDismiss,\n  goBack,\n  linkTo,\n  navigate,\n  dismiss,\n  dismissAll,\n  push,\n  replace,\n  setParams,\n} from './routing';\nimport { getSortedRoutes } from './sort-routes';\nimport { UrlObject, getRouteInfoFromState } from '../LocationProvider';\nimport { RouteNode } from '../Route';\nimport { deepEqual, getPathDataFromState } from '../fork/getPathFromState';\nimport { ResultState } from '../fork/getStateFromPath';\nimport { ExpoLinkingOptions, LinkingConfigOptions, getLinkingConfig } from '../getLinkingConfig';\nimport { getRoutes } from '../getRoutes';\nimport { RequireContext } from '../types';\nimport { getQualifiedRouteComponent } from '../useScreens';\n\n/**\n * This is the global state for the router. It is used to keep track of the current route, and to provide a way to navigate to other routes.\n *\n * There should only be one instance of this class and be initialized via `useInitializeExpoRouter`\n */\nexport class RouterStore {\n  routeNode!: RouteNode | null;\n  rootComponent!: ComponentType;\n  linking?: ExpoLinkingOptions;\n  private hasAttemptedToHideSplash: boolean = false;\n\n  initialState?: ResultState;\n  rootState?: ResultState;\n  nextState?: ResultState;\n  routeInfo?: UrlObject;\n  splashScreenAnimationFrame?: number;\n\n  navigationRef!: NavigationContainerRefWithCurrent<ReactNavigation.RootParamList>;\n  navigationRefSubscription!: () => void;\n\n  rootStateSubscribers = new Set<() => void>();\n  storeSubscribers = new Set<() => void>();\n\n  linkTo = linkTo.bind(this);\n  getSortedRoutes = getSortedRoutes.bind(this);\n  goBack = goBack.bind(this);\n  canGoBack = canGoBack.bind(this);\n  push = push.bind(this);\n  dismiss = dismiss.bind(this);\n  replace = replace.bind(this);\n  dismissAll = dismissAll.bind(this);\n  canDismiss = canDismiss.bind(this);\n  setParams = setParams.bind(this);\n  navigate = navigate.bind(this);\n\n  initialize(\n    context: RequireContext,\n    navigationRef: NavigationContainerRefWithCurrent<ReactNavigation.RootParamList>,\n    linkingConfigOptions: LinkingConfigOptions = {}\n  ) {\n    // Clean up any previous state\n    this.initialState = undefined;\n    this.rootState = undefined;\n    this.nextState = undefined;\n    this.linking = undefined;\n    this.navigationRefSubscription?.();\n    this.rootStateSubscribers.clear();\n    this.storeSubscribers.clear();\n\n    this.routeNode = getRoutes(context, {\n      ...Constants.expoConfig?.extra?.router,\n      ignoreEntryPoints: true,\n      platform: Platform.OS,\n    });\n\n    // We always needs routeInfo, even if there are no routes. This can happen if:\n    //  - there are no routes (we are showing the onboarding screen)\n    //  - getInitialURL() is async\n    this.routeInfo = {\n      unstable_globalHref: '',\n      pathname: '',\n      isIndex: false,\n      params: {},\n      segments: [],\n    };\n\n    if (this.routeNode) {\n      // We have routes, so get the linking config and the root component\n      this.linking = getLinkingConfig(this, this.routeNode, context, linkingConfigOptions);\n      this.rootComponent = getQualifiedRouteComponent(this.routeNode);\n\n      // By default React Navigation is async and does not render anything in the first pass as it waits for `getInitialURL`\n      // This will cause static rendering to fail, which once performs a single pass.\n      // If the initialURL is a string, we can preload the state and routeInfo, skipping React Navigation's async behavior.\n      const initialURL = this.linking?.getInitialURL?.();\n      if (typeof initialURL === 'string') {\n        this.rootState = this.linking.getStateFromPath?.(initialURL, this.linking.config);\n        this.initialState = this.rootState;\n        if (this.rootState) {\n          this.routeInfo = this.getRouteInfo(this.rootState);\n        }\n      }\n    } else {\n      // Only error in production, in development we will show the onboarding screen\n      if (process.env.NODE_ENV === 'production') {\n        throw new Error('No routes found');\n      }\n\n      // In development, we will show the onboarding screen\n      this.rootComponent = Fragment;\n    }\n\n    /**\n     * Counter intuitively - this fires AFTER both React Navigation's state changes and the subsequent paint.\n     * This poses a couple of issues for Expo Router,\n     *   - Ensuring hooks (e.g. useSearchParams()) have data in the initial render\n     *   - Reacting to state changes after a navigation event\n     *\n     * This is why the initial render renders a Fragment and we wait until `onReady()` is called\n     * Additionally, some hooks compare the state from both the store and the navigationRef. If the store it stale,\n     * that hooks will manually update the store.\n     *\n     */\n    this.navigationRef = navigationRef;\n    this.navigationRefSubscription = navigationRef.addListener('state', (data) => {\n      const state = data.data.state as ResultState;\n\n      if (!this.hasAttemptedToHideSplash) {\n        this.hasAttemptedToHideSplash = true;\n        // NOTE(EvanBacon): `navigationRef.isReady` is sometimes not true when state is called initially.\n        this.splashScreenAnimationFrame = requestAnimationFrame(() => {\n          // @ts-expect-error: This function is native-only and for internal-use only.\n          SplashScreen._internal_maybeHideAsync?.();\n        });\n      }\n\n      let shouldUpdateSubscribers = this.nextState === state;\n      this.nextState = undefined;\n\n      // This can sometimes be undefined when an error is thrown in the Root Layout Route.\n      // Additionally that state may already equal the rootState if it was updated within a hook\n      if (state && state !== this.rootState) {\n        store.updateState(state, undefined);\n        shouldUpdateSubscribers = true;\n      }\n\n      // If the state has changed, or was changed inside a hook we need to update the subscribers\n      if (shouldUpdateSubscribers) {\n        for (const subscriber of this.rootStateSubscribers) {\n          subscriber();\n        }\n      }\n    });\n\n    for (const subscriber of this.storeSubscribers) {\n      subscriber();\n    }\n  }\n\n  updateState(state: ResultState, nextState = state) {\n    store.rootState = state;\n    store.nextState = nextState;\n\n    const nextRouteInfo = store.getRouteInfo(state);\n\n    if (!deepEqual(this.routeInfo, nextRouteInfo)) {\n      store.routeInfo = nextRouteInfo;\n    }\n  }\n\n  getRouteInfo(state: ResultState) {\n    return getRouteInfoFromState(\n      (state: Parameters<typeof getPathFromState>[0], asPath: boolean) => {\n        return getPathDataFromState(state, {\n          screens: {},\n          ...this.linking?.config,\n          preserveDynamicRoutes: asPath,\n          preserveGroups: asPath,\n        });\n      },\n      state\n    );\n  }\n\n  // This is only used in development, to show the onboarding screen\n  // In production we should have errored during the initialization\n  shouldShowTutorial() {\n    return !this.routeNode && process.env.NODE_ENV === 'development';\n  }\n\n  /** Make sure these are arrow functions so `this` is correctly bound */\n  subscribeToRootState = (subscriber: () => void) => {\n    this.rootStateSubscribers.add(subscriber);\n    return () => this.rootStateSubscribers.delete(subscriber);\n  };\n  subscribeToStore = (subscriber: () => void) => {\n    this.storeSubscribers.add(subscriber);\n    return () => this.storeSubscribers.delete(subscriber);\n  };\n  snapshot = () => {\n    return this;\n  };\n  rootStateSnapshot = () => {\n    return this.rootState!;\n  };\n  routeInfoSnapshot = () => {\n    return this.routeInfo!;\n  };\n\n  cleanup() {\n    if (this.splashScreenAnimationFrame) {\n      cancelAnimationFrame(this.splashScreenAnimationFrame);\n    }\n  }\n}\n\nexport const store = new RouterStore();\n\nexport function useExpoRouter() {\n  return useSyncExternalStore(store.subscribeToStore, store.snapshot, store.snapshot);\n}\n\nfunction syncStoreRootState() {\n  if (store.navigationRef.isReady()) {\n    const currentState = store.navigationRef.getRootState() as unknown as ResultState;\n\n    if (store.rootState !== currentState) {\n      store.updateState(currentState);\n    }\n  }\n}\n\nexport function useStoreRootState() {\n  syncStoreRootState();\n  return useSyncExternalStore(\n    store.subscribeToRootState,\n    store.rootStateSnapshot,\n    store.rootStateSnapshot\n  );\n}\n\nexport function useStoreRouteInfo() {\n  syncStoreRootState();\n  return useSyncExternalStore(\n    store.subscribeToRootState,\n    store.routeInfoSnapshot,\n    store.routeInfoSnapshot\n  );\n}\n\nexport function useInitializeExpoRouter(context: RequireContext, options: LinkingConfigOptions) {\n  const navigationRef = useNavigationContainerRef();\n  useMemo(() => store.initialize(context, navigationRef, options), [context]);\n  useExpoRouter();\n  return store;\n}\n"]}