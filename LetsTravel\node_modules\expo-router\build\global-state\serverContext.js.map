{"version": 3, "file": "serverContext.js", "sourceRoot": "", "sources": ["../../src/global-state/serverContext.ts"], "names": [], "mappings": ";;AAAA,iIAAiI;AACjI,iCAAsC;AAStC,MAAM,aAAa,GAAG,IAAA,qBAAa,EAAgC,SAAS,CAAC,CAAC;AAE9E,kBAAe,aAAa,CAAC", "sourcesContent": ["// This is file should mirror https://github.com/react-navigation/react-navigation/blob/6.x/packages/native/src/ServerContext.tsx\nimport { createContext } from 'react';\n\nexport type ServerContextType = {\n  location?: {\n    pathname: string;\n    search: string;\n  };\n};\n\nconst ServerContext = createContext<ServerContextType | undefined>(undefined);\n\nexport default ServerContext;\n"]}