{"version": 3, "file": "resolvePackageJson.js", "names": ["_fs", "data", "require", "_path", "_Errors", "getRootPackageJsonPath", "projectRoot", "packageJsonPath", "join", "existsSync", "ConfigError"], "sources": ["../src/resolvePackageJson.ts"], "sourcesContent": ["import { existsSync } from 'fs';\nimport { join } from 'path';\n\nimport { ConfigError } from './Errors';\n\nexport function getRootPackageJsonPath(projectRoot: string): string {\n  const packageJsonPath = join(projectRoot, 'package.json');\n  if (!existsSync(packageJsonPath)) {\n    throw new ConfigError(\n      `The expected package.json path: ${packageJsonPath} does not exist`,\n      'MODULE_NOT_FOUND'\n    );\n  }\n  return packageJsonPath;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,SAASI,sBAAsBA,CAACC,WAAmB,EAAU;EAClE,MAAMC,eAAe,GAAG,IAAAC,YAAI,EAACF,WAAW,EAAE,cAAc,CAAC;EACzD,IAAI,CAAC,IAAAG,gBAAU,EAACF,eAAe,CAAC,EAAE;IAChC,MAAM,KAAIG,qBAAW,EACnB,mCAAmCH,eAAe,iBAAiB,EACnE,kBACF,CAAC;EACH;EACA,OAAOA,eAAe;AACxB", "ignoreList": []}