{"version": 3, "file": "LogBoxButton.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/UI/LogBoxButton.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,+CAAwC;AACxC,+CAAmG;AAEnG,2DAA6C;AAa7C,SAAgB,YAAY,CAAC,KAAY;IACvC,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAE9C,IAAI,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC;IAC5C,IAAI,CAAC,eAAe,EAAE;QACpB,eAAe,GAAG;YAChB,OAAO,EAAE,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAC7C,OAAO,EAAE,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC;SAC7C,CAAC;KACH;IAED,MAAM,OAAO,GAAG,CACd,8BAAC,mBAAI,IACH,KAAK,EAAE;YACL;gBACE,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO;gBAC5E,GAAG,uBAAQ,CAAC,MAAM,CAAC;oBACjB,GAAG,EAAE;wBACH,MAAM,EAAE,SAAS;qBAClB;iBACF,CAAC;aACH;YACD,KAAK,CAAC,KAAK;SACZ,IACA,KAAK,CAAC,QAAQ,CACV,CACR,CAAC;IAEF,OAAO,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAC7B,OAAO,CACR,CAAC,CAAC,CAAC,CACF,8BAAC,wBAAS,IACR,OAAO,EAAE,KAAK,CAAC,OAAO,EACtB,OAAO,EAAE,KAAK,CAAC,OAAO,EACtB,SAAS,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EACjC,UAAU,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,IAClC,OAAO,CACE,CACb,CAAC;AACJ,CAAC;AAvCD,oCAuCC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React, { useState } from 'react';\nimport { GestureResponderEvent, Insets, Platform, Pressable, View, ViewStyle } from 'react-native';\n\nimport * as LogBoxStyle from './LogBoxStyle';\n\ntype Props = {\n  backgroundColor: {\n    default: string;\n    pressed: string;\n  };\n  children?: any;\n  hitSlop?: Insets;\n  onPress?: ((event: GestureResponderEvent) => void) | null;\n  style?: ViewStyle;\n};\n\nexport function LogBoxButton(props: Props) {\n  const [pressed, setPressed] = useState(false);\n\n  let backgroundColor = props.backgroundColor;\n  if (!backgroundColor) {\n    backgroundColor = {\n      default: LogBoxStyle.getBackgroundColor(0.95),\n      pressed: LogBoxStyle.getBackgroundColor(0.6),\n    };\n  }\n\n  const content = (\n    <View\n      style={[\n        {\n          backgroundColor: pressed ? backgroundColor.pressed : backgroundColor.default,\n          ...Platform.select({\n            web: {\n              cursor: 'pointer',\n            },\n          }),\n        },\n        props.style,\n      ]}>\n      {props.children}\n    </View>\n  );\n\n  return props.onPress == null ? (\n    content\n  ) : (\n    <Pressable\n      hitSlop={props.hitSlop}\n      onPress={props.onPress}\n      onPressIn={() => setPressed(true)}\n      onPressOut={() => setPressed(false)}>\n      {content}\n    </Pressable>\n  );\n}\n"]}