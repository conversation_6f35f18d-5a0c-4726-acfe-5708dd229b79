{"version": 3, "file": "modules.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "obj", "__esModule", "default", "statAsync", "file", "fs", "promises", "stat", "fileExistsAsync", "isFile", "directoryExistsAsync", "isDirectory", "fileExists", "statSync"], "sources": ["../../src/utils/modules.ts"], "sourcesContent": ["import fs from 'fs';\n\n/**\n * A non-failing version of async FS stat.\n *\n * @param file\n */\nasync function statAsync(file: string): Promise<fs.Stats | null> {\n  try {\n    return await fs.promises.stat(file);\n  } catch {\n    return null;\n  }\n}\n\nexport async function fileExistsAsync(file: string): Promise<boolean> {\n  return (await statAsync(file))?.isFile() ?? false;\n}\n\nexport async function directoryExistsAsync(file: string): Promise<boolean> {\n  return (await statAsync(file))?.isDirectory() ?? false;\n}\n\nexport function fileExists(file: string): boolean {\n  try {\n    return fs.statSync(file).isFile();\n  } catch {\n    return false;\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoB,SAAAC,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEpB;AACA;AACA;AACA;AACA;AACA,eAAeG,SAASA,CAACC,IAAY,EAA4B;EAC/D,IAAI;IACF,OAAO,MAAMC,aAAE,CAACC,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC;EACrC,CAAC,CAAC,MAAM;IACN,OAAO,IAAI;EACb;AACF;AAEO,eAAeI,eAAeA,CAACJ,IAAY,EAAoB;EACpE,OAAO,CAAC,MAAMD,SAAS,CAACC,IAAI,CAAC,GAAGK,MAAM,CAAC,CAAC,IAAI,KAAK;AACnD;AAEO,eAAeC,oBAAoBA,CAACN,IAAY,EAAoB;EACzE,OAAO,CAAC,MAAMD,SAAS,CAACC,IAAI,CAAC,GAAGO,WAAW,CAAC,CAAC,IAAI,KAAK;AACxD;AAEO,SAASC,UAAUA,CAACR,IAAY,EAAW;EAChD,IAAI;IACF,OAAOC,aAAE,CAACQ,QAAQ,CAACT,IAAI,CAAC,CAACK,MAAM,CAAC,CAAC;EACnC,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF", "ignoreList": []}