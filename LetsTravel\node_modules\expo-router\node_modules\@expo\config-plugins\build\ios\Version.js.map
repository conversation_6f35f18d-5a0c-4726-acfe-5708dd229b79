{"version": 3, "file": "Version.js", "names": ["_iosPlugins", "data", "require", "withVersion", "exports", "createInfoPlistPluginWithPropertyGuard", "setVersion", "infoPlistProperty", "expoConfigProperty", "withBuildNumber", "setBuildNumber", "getVersion", "config", "version", "infoPlist", "CFBundleShortVersionString", "getBuildNumber", "ios", "buildNumber", "CFBundleVersion"], "sources": ["../../src/ios/Version.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { InfoPlist } from './IosConfig.types';\nimport { createInfoPlistPluginWithPropertyGuard } from '../plugins/ios-plugins';\n\nexport const withVersion = createInfoPlistPluginWithPropertyGuard(\n  setVersion,\n  {\n    infoPlistProperty: 'CFBundleShortVersionString',\n    expoConfigProperty: 'version',\n  },\n  'withVersion'\n);\n\nexport const withBuildNumber = createInfoPlistPluginWithPropertyGuard(\n  setBuildNumber,\n  {\n    infoPlistProperty: 'CFBundleVersion',\n    expoConfigProperty: 'ios.buildNumber',\n  },\n  'withBuildNumber'\n);\n\nexport function getVersion(config: Pick<ExpoConfig, 'version'>) {\n  return config.version || '1.0.0';\n}\n\nexport function setVersion(config: Pick<ExpoConfig, 'version'>, infoPlist: InfoPlist): InfoPlist {\n  return {\n    ...infoPlist,\n    CFBundleShortVersionString: getVersion(config),\n  };\n}\n\nexport function getBuildNumber(config: Pick<ExpoConfig, 'ios'>) {\n  return config.ios?.buildNumber ? config.ios.buildNumber : '1';\n}\n\nexport function setBuildNumber(config: Pick<ExpoConfig, 'ios'>, infoPlist: InfoPlist): InfoPlist {\n  return {\n    ...infoPlist,\n    CFBundleVersion: getBuildNumber(config),\n  };\n}\n"], "mappings": ";;;;;;;;;;AAGA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAME,WAAW,GAAAC,OAAA,CAAAD,WAAA,GAAG,IAAAE,oDAAsC,EAC/DC,UAAU,EACV;EACEC,iBAAiB,EAAE,4BAA4B;EAC/CC,kBAAkB,EAAE;AACtB,CAAC,EACD,aACF,CAAC;AAEM,MAAMC,eAAe,GAAAL,OAAA,CAAAK,eAAA,GAAG,IAAAJ,oDAAsC,EACnEK,cAAc,EACd;EACEH,iBAAiB,EAAE,iBAAiB;EACpCC,kBAAkB,EAAE;AACtB,CAAC,EACD,iBACF,CAAC;AAEM,SAASG,UAAUA,CAACC,MAAmC,EAAE;EAC9D,OAAOA,MAAM,CAACC,OAAO,IAAI,OAAO;AAClC;AAEO,SAASP,UAAUA,CAACM,MAAmC,EAAEE,SAAoB,EAAa;EAC/F,OAAO;IACL,GAAGA,SAAS;IACZC,0BAA0B,EAAEJ,UAAU,CAACC,MAAM;EAC/C,CAAC;AACH;AAEO,SAASI,cAAcA,CAACJ,MAA+B,EAAE;EAC9D,OAAOA,MAAM,CAACK,GAAG,EAAEC,WAAW,GAAGN,MAAM,CAACK,GAAG,CAACC,WAAW,GAAG,GAAG;AAC/D;AAEO,SAASR,cAAcA,CAACE,MAA+B,EAAEE,SAAoB,EAAa;EAC/F,OAAO;IACL,GAAGA,SAAS;IACZK,eAAe,EAAEH,cAAc,CAACJ,MAAM;EACxC,CAAC;AACH", "ignoreList": []}