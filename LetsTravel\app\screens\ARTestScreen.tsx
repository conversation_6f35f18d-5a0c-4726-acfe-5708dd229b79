import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView } from 'react-native';
import { router } from 'expo-router';
import ARCoreTextTranslation from '../components/ARCoreTextTranslation';

const ARTestScreen = () => {
  const handleClose = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ARCoreTextTranslation
        isVisible={true}
        onClose={handleClose}
        enableTranslation={true}
        translationTarget="zh-CN"
        enableCurrency={true}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
});

export default ARTestScreen; 