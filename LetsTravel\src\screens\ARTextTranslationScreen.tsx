import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { 
  takePhoto, 
  detectText, 
  hitTest, 
  addTextNode, 
  clearAllNodes, 
  initARScene,
  isARAvailable,
  TextBlock
} from 'ar-bridge';

// 模拟翻译服务
const translateText = async (text: string, targetLang: string = 'zh-CN'): Promise<string> => {
  // 实际项目中应调用真实的翻译API
  // 例如: Google Translate API, Microsoft Translator, 或自建服务
  // 这里我们模拟延迟并返回"翻译"结果
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 简单的映射表，用于演示
  const simpleTranslate: {[key: string]: string} = {
    'hello': '你好',
    'coffee': '咖啡',
    'menu': '菜单',
    'price': '价格',
    'restaurant': '餐厅',
    'food': '食物',
    'drink': '饮料',
    'water': '水',
    'tea': '茶',
  };
  
  return text.split(' ').map(word => {
    const lowerWord = (word || '').toLowerCase();
    return simpleTranslate[lowerWord] || word;
  }).join(' ');
};

// 模拟汇率转换
const convertCurrency = async (amount: string, fromCurrency: string, toCurrency: string = 'CNY'): Promise<string> => {
  // 实际项目中应调用真实的汇率API
  // 例如: Open Exchange Rates, Fixer.io, 或其他汇率服务
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // 简单的模拟汇率，仅用于演示
  const rates: {[key: string]: number} = {
    'USD': 7.15, // 1美元 = 7.15人民币
    'EUR': 7.78, // 1欧元 = 7.78人民币
    'GBP': 9.06, // 1英镑 = 9.06人民币
    'JPY': 0.048, // 1日元 = 0.048人民币
    'HKD': 0.92, // 1港币 = 0.92人民币
  };
  
  // 尝试从文本中提取数字
  const numberMatch = amount.match(/(\d+(\.\d+)?)/);
  if (!numberMatch) return amount;
  
  const number = parseFloat(numberMatch[0]);
  if (isNaN(number)) return amount;
  
  // 尝试确定货币类型
  let detectedCurrency = '';
  if (amount.includes('$') || amount.toLowerCase().includes('usd')) {
    detectedCurrency = 'USD';
  } else if (amount.includes('€') || amount.toLowerCase().includes('eur')) {
    detectedCurrency = 'EUR';
  } else if (amount.includes('£') || amount.toLowerCase().includes('gbp')) {
    detectedCurrency = 'GBP';
  } else if (amount.includes('¥') || amount.toLowerCase().includes('jpy')) {
    detectedCurrency = 'JPY';
  } else if (amount.includes('HK$') || amount.toLowerCase().includes('hkd')) {
    detectedCurrency = 'HKD';
  } else {
    // 如果无法检测货币类型，则使用提供的fromCurrency
    detectedCurrency = fromCurrency;
  }
  
  if (!rates[detectedCurrency]) return amount;
  
  // 转换为目标货币
  const convertedAmount = number * rates[detectedCurrency];
  
  // 格式化结果
  return `${number} ${detectedCurrency} ≈ ${convertedAmount.toFixed(2)} ${toCurrency}`;
};

// 检测文本是否可能包含价格
const isPriceText = (text: string): boolean => {
  // 简单检测是否包含价格符号和数字
  const priceRegex = /(\$|€|£|¥|HK\$|USD|EUR|GBP|JPY|HKD)?\s*\d+(\.\d+)?|\d+(\.\d+)?\s*(\$|€|£|¥|HK\$|USD|EUR|GBP|JPY|HKD)/i;
  return priceRegex.test(text);
};

const ARTextTranslationScreen = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [processingStep, setProcessingStep] = useState('');
  const [detectedBlocks, setDetectedBlocks] = useState([]);
  const [isARSupported, setIsARSupported] = useState(false);
  
  // 初始化AR场景和检查设备兼容性
  useEffect(() => {
    const checkARAvailability = async () => {
      try {
        const available = await isARAvailable();
        setIsARSupported(available);
        
        if (!available) {
          Alert.alert(
            "不支持AR",
            "您的设备不支持ARCore，无法使用AR功能。",
            [{ text: "返回", onPress: () => router.back() }]
          );
          return;
        }
        
        // 初始化AR场景
        await initARScene();
        setIsInitializing(false);
      } catch (error) {
        console.error("AR初始化错误:", error);
        Alert.alert(
          "AR初始化失败",
          "无法初始化AR功能，请确保您的设备支持AR并已授予相应权限。",
          [{ text: "返回", onPress: () => router.back() }]
        );
      }
    };
    
    checkARAvailability();
    
    // 组件卸载时清理
    return () => {
      clearAllNodes().catch(console.error);
    };
  }, []);
  
  // 处理拍照并识别文字
  const handleCaptureAndRecognize = async () => {
    if (isLoading) return;
    
    try {
      setIsLoading(true);
      setProcessingStep('拍照中...');
      
      // 1. 拍照
      const imagePath = await takePhoto();
      
      // 2. 文字识别
      setProcessingStep('识别文字中...');
      const blocks = await detectText(imagePath);
      setDetectedBlocks(blocks);
      
      if (blocks.length === 0) {
        Alert.alert("未检测到文字", "请尝试对准含有清晰文字的区域。");
        setIsLoading(false);
        return;
      }
      
      // 3. 处理每个文字块
      setProcessingStep('处理文字中...');
      for (const block of blocks) {
        // 计算文字块中心点
        const centerX = (block.boundingBox.left + block.boundingBox.right) / 2;
        const centerY = (block.boundingBox.top + block.boundingBox.bottom) / 2;
        
        // 执行hit-test获取3D空间坐标
        try {
          const hitResult = await hitTest(centerX, centerY);
          
          // 根据文字内容决定是翻译还是汇率转换
          let processedText = block.text;
          if (isPriceText(block.text)) {
            // 价格文本 - 执行汇率转换
            processedText = await convertCurrency(block.text, 'USD');
          } else {
            // 普通文本 - 执行翻译
            processedText = await translateText(block.text);
          }
          
          // 当翻译结果与原文不同时才添加节点
          if (processedText !== block.text) {
            // 添加3D文本节点
            await addTextNode(
              hitResult,
              processedText,
              {
                textColor: "#FFFFFF",
                backgroundColor: isPriceText(block.text) ? "#4CAF50" : "#2196F3",
                fontSize: 16,
                padding: 8,
                elevation: 0.05
              }
            );
          }
        } catch (hitError) {
          console.log(`无法在(${centerX}, ${centerY})处创建AR标记: ${hitError}`);
          // 继续处理其他文字块
        }
      }
      
      setProcessingStep('完成！');
    } catch (error) {
      console.error("AR处理错误:", error);
      Alert.alert(
        "处理失败",
        "无法完成AR文字翻译，请重试。",
        [{ text: "确定", style: "cancel" }]
      );
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理清除所有节点
  const handleClearAll = async () => {
    try {
      await clearAllNodes();
      setDetectedBlocks([]);
    } catch (error) {
      console.error("清除节点错误:", error);
    }
  };
  
  // 渲染加载指示器
  const renderLoadingOverlay = () => {
    if (!isLoading && !isInitializing) return null;
    
    return (
      <View style={styles.loadingOverlay}>
        <ActivityIndicator size="large" color="#FFFFFF" />
        <Text style={styles.loadingText}>
          {isInitializing ? "正在初始化AR..." : processingStep}
        </Text>
      </View>
    );
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      
      {/* 这里将放置AR视图，由原生ARActivity提供 */}
      <View style={styles.arViewContainer}>
        {/* AR视图在原生层实现 */}
        <Text style={styles.placeholderText}>
          {isARSupported ? "AR视图加载中..." : "设备不支持AR"}
        </Text>
      </View>
      
      {/* 控制栏 */}
      <View style={styles.controlBar}>
        <TouchableOpacity 
          style={styles.button} 
          onPress={() => router.back()}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>返回</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton]} 
          onPress={handleCaptureAndRecognize}
          disabled={isLoading || isInitializing}
        >
          <Text style={styles.buttonText}>
            {isLoading ? "处理中..." : "拍照识别"}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={handleClearAll}
          disabled={isLoading || detectedBlocks.length === 0}
        >
          <Text style={styles.buttonText}>清除</Text>
        </TouchableOpacity>
      </View>
      
      {renderLoadingOverlay()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  arViewContainer: {
    flex: 1,
    backgroundColor: '#222',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    color: '#FFF',
    fontSize: 16,
  },
  controlBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 10,
    backgroundColor: 'rgba(0,0,0,0.75)',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: '#555',
    minWidth: 100,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#2196F3',
  },
  buttonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFF',
    fontSize: 18,
    marginTop: 16,
  },
});

export default ARTextTranslationScreen; 