{"version": 3, "file": "buildAsyncRequire.js", "sourceRoot": "", "sources": ["../../src/async-require/buildAsyncRequire.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,6CAA+C;AAQ/C,oFAAoF;AACpF,SAAgB,iBAAiB;IAC/B,MAAM,KAAK,GAAG,IAAI,GAAG,EAAyB,CAAC;IAE/C,OAAO,KAAK,UAAU,yBAAyB,CAAC,IAAY;QAC1D,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACnB,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;SACzB;QAED,MAAM,OAAO,GAAG,IAAA,4BAAe,EAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACpD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACnB,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEzB,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;AACJ,CAAC;AAjBD,8CAiBC", "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport { loadBundleAsync } from './loadBundle';\n\n/**\n * Must satisfy the requirements of the Metro bundler.\n * https://github.com/react-native-community/discussions-and-proposals/blob/main/proposals/0605-lazy-bundling.md#__loadbundleasync-in-metro\n */\ntype AsyncRequire = (path: string) => Promise<void>;\n\n/** Create an `loadBundleAsync` function in the expected shape for Metro bundler. */\nexport function buildAsyncRequire(): AsyncRequire {\n  const cache = new Map<string, Promise<void>>();\n\n  return async function universal_loadBundleAsync(path: string): Promise<void> {\n    if (cache.has(path)) {\n      return cache.get(path)!;\n    }\n\n    const promise = loadBundleAsync(path).catch((error) => {\n      cache.delete(path);\n      throw error;\n    });\n\n    cache.set(path, promise);\n\n    return promise;\n  };\n}\n"]}