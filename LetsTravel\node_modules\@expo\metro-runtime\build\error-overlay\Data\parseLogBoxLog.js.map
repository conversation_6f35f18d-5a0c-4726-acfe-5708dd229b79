{"version": 3, "file": "parseLogBoxLog.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/Data/parseLogBoxLog.tsx"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;AAGH,iFAAyD;AACzD,6EAAqD;AAGrD,MAAM,4BAA4B,GAChC,gGAAgG,CAAC;AACnG,MAAM,6BAA6B,GACjC,sGAAsG,CAAC;AACzG,MAAM,kBAAkB,GACtB,6FAA6F,CAAC;AAgChG,MAAM,YAAY,GAAG,UAAU,CAAC;AAEhC,SAAgB,kBAAkB,CAAC,IAAoB;IAIrD,MAAM,aAAa,GAAa,EAAE,CAAC;IACnC,MAAM,YAAY,GAAa,EAAE,CAAC;IAClC,MAAM,mBAAmB,GAAyC,EAAE,CAAC;IAErE,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpC,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;QAC/C,MAAM,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACnD,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvD,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;QAE7D,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,IAAI,aAAa,GAAG,EAAE,CAAC;QAEvB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE;YAChD,cAAc,IAAI,gBAAgB,CAAC;YACnC,aAAa,IAAI,gBAAgB,CAAC;YAElC,IAAI,iBAAiB,GAAG,iBAAiB,EAAE;gBACzC,IAAI,iBAAiB,GAAG,aAAa,CAAC,MAAM,EAAE;oBAC5C,iCAAiC;oBACjC,qDAAqD;oBACrD,uCAAuC;oBACvC,MAAM,YAAY,GAChB,OAAO,aAAa,CAAC,iBAAiB,CAAC,KAAK,QAAQ;wBAClD,CAAC,CAAC,aAAa,CAAC,iBAAiB,CAAC;wBAClC,CAAC,CAAC,IAAA,uBAAa,EAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC;oBACtD,mBAAmB,CAAC,IAAI,CAAC;wBACvB,MAAM,EAAE,YAAY,CAAC,MAAM;wBAC3B,MAAM,EAAE,aAAa,CAAC,MAAM;qBAC7B,CAAC,CAAC;oBAEH,cAAc,IAAI,YAAY,CAAC;oBAC/B,aAAa,IAAI,YAAY,CAAC;iBAC/B;qBAAM;oBACL,mBAAmB,CAAC,IAAI,CAAC;wBACvB,MAAM,EAAE,CAAC;wBACT,MAAM,EAAE,aAAa,CAAC,MAAM;qBAC7B,CAAC,CAAC;oBAEH,cAAc,IAAI,IAAI,CAAC;oBACvB,aAAa,IAAI,IAAI,CAAC;iBACvB;gBAED,iBAAiB,EAAE,CAAC;aACrB;SACF;QAED,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KAClC;IAED,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QAC1C,iCAAiC;QACjC,qDAAqD;QACrD,uCAAuC;QACvC,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAA,uBAAa,EAAC,GAAG,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IACH,aAAa,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;IACrC,YAAY,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;IAEpC,OAAO;QACL,QAAQ,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC;QACjC,OAAO,EAAE;YACP,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC;YAC/B,aAAa,EAAE,mBAAmB;SACnC;KACF,CAAC;AACJ,CAAC;AAzED,gDAyEC;AAED,SAAS,gBAAgB,CAAC,eAAuB;IAC/C,MAAM,yBAAyB,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACjE,MAAM,yBAAyB,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACjE,MAAM,4BAA4B,GAAG,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAEnE,OAAO,yBAAyB,IAAI,yBAAyB,IAAI,4BAA4B,CAAC;AAChG,CAAC;AAED,SAAgB,mBAAmB,CAAC,OAAe;IACjD,sFAAsF;IACtF,oFAAoF;IACpF,6EAA6E;IAC7E,MAAM,KAAK,GAAG,IAAA,yBAAe,EAAC,OAAO,CAAC,CAAC;IACvC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QAC7B,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC3B,OAAO,EAAE,KAAK,CAAC,UAAU;YACzB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK;YACjC,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI;YACrD,QAAQ,EAAE;gBACR,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM;gBAChD,GAAG,EAAE,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU;aACtD;SACF,CAAC,CAAC,CAAC;KACL;IAED,OAAO,OAAO;SACX,KAAK,CAAC,YAAY,CAAC;SACnB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;QACT,IAAI,CAAC,CAAC,EAAE;YACN,OAAO,IAAI,CAAC;SACb;QACD,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACtD,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,IAAI,CAAC;SACb;QAED,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAChD,OAAO;YACL,OAAO;YACP,QAAQ;YACR,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;SACjD,CAAC;IACJ,CAAC,CAAC;SACD,MAAM,CAAC,OAAO,CAAmB,CAAC;AACvC,CAAC;AApCD,kDAoCC;AAED,SAAgB,oBAAoB,CAAC,KAA4B;IAC/D,MAAM,OAAO,GAAG,KAAK,CAAC,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC;IAElF,MAAM,kBAAkB,GAAG,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IAC7D,IAAI,kBAAkB,EAAE;QACtB,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEhF,OAAO;YACL,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,EAAE;YACT,gBAAgB,EAAE,KAAK;YACvB,cAAc,EAAE,EAAE;YAClB,SAAS,EAAE;gBACT,QAAQ;gBACR,QAAQ,EAAE;oBACR,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC;oBACtB,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;iBAC7B;gBACD,OAAO,EAAE,SAAS;aACnB;YACD,OAAO,EAAE;gBACP,OAAO;gBACP,aAAa,EAAE,EAAE;aAClB;YACD,QAAQ,EAAE,GAAG,QAAQ,IAAI,GAAG,IAAI,MAAM,EAAE;SACzC,CAAC;KACH;IAED,MAAM,mBAAmB,GAAG,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IACxE,IAAI,mBAAmB,EAAE;QACvB,iEAAiE;QACjE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEjF,OAAO;YACL,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,EAAE;YACT,gBAAgB,EAAE,KAAK;YACvB,cAAc,EAAE,EAAE;YAClB,SAAS,EAAE;gBACT,QAAQ;gBACR,QAAQ,EAAE;oBACR,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC;oBACtB,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;iBAC7B;gBACD,OAAO,EAAE,SAAS;aACnB;YACD,OAAO,EAAE;gBACP,OAAO;gBACP,aAAa,EAAE,EAAE;aAClB;YACD,QAAQ,EAAE,GAAG,QAAQ,IAAI,GAAG,IAAI,MAAM,EAAE;SACzC,CAAC;KACH;IAED,MAAM,mBAAmB,GAAG,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;IAEzE,IAAI,mBAAmB,EAAE;QACvB,mEAAmE;QACnE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpE,OAAO;YACL,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,EAAE;YACT,gBAAgB,EAAE,KAAK;YACvB,cAAc,EAAE,EAAE;YAClB,SAAS,EAAE;gBACT,QAAQ;gBACR,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,SAAS;aACnB;YACD,OAAO,EAAE;gBACP,OAAO;gBACP,aAAa,EAAE,EAAE;aAClB;YACD,QAAQ,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE;SAClC,CAAC;KACH;IAED,IAAI,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE;QACrC,OAAO;YACL,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,cAAc,EAAE,EAAE;YAClB,OAAO,EAAE;gBACP,OAAO,EAAE,OAAO;gBAChB,aAAa,EAAE,EAAE;aAClB;YACD,QAAQ,EAAE,OAAO;SAClB,CAAC;KACH;IAED,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;IAC5C,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,gBAAgB,EAAE;QAC3C,OAAO;YACL,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,cAAc,EAAE,cAAc,IAAI,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;YACjF,GAAG,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC;SACjC,CAAC;KACH;IAED,IAAI,cAAc,IAAI,IAAI,EAAE;QAC1B,4DAA4D;QAC5D,OAAO;YACL,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,cAAc,EAAE,mBAAmB,CAAC,cAAc,CAAC;YACnD,GAAG,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC;SACjC,CAAC;KACH;IAED,6EAA6E;IAC7E,sEAAsE;IACtE,OAAO;QACL,KAAK,EAAE,OAAO;QACd,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;QACxC,GAAG,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC;KAC7B,CAAC;AACJ,CAAC;AA1HD,oDA0HC;AAED,SAAgB,cAAc,CAAC,IAAoB;IAKjD,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,IAAI,yBAAyB,GAAU,EAAE,CAAC;IAC1C,IAAI,cAAc,GAAmB,EAAE,CAAC;IAExC,+DAA+D;IAC/D,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QAChF,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAAE;YAC5D,yBAAyB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9C,yBAAyB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACpD,cAAc,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;SAC/C;KACF;IAED,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;QAC/B,6CAA6C;QAC7C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE;gBACpD,qDAAqD;gBACrD,IAAI,eAAe,GAAG,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;gBACnD,IAAI,eAAe,GAAG,CAAC,EAAE;oBACvB,+BAA+B;oBAC/B,eAAe,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACpC;gBACD,IAAI,eAAe,GAAG,CAAC,EAAE;oBACvB,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC;iBAC/D;gBAED,cAAc,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;aAC3C;iBAAM;gBACL,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACrC;SACF;KACF;IAED,OAAO;QACL,GAAG,kBAAkB,CAAC,yBAAyB,CAAC;QAChD,cAAc;KACf,CAAC;AACJ,CAAC;AA5CD,wCA4CC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport type { LogBoxLogData } from './LogBoxLog';\nimport parseErrorStack from '../modules/parseErrorStack';\nimport stringifySafe from '../modules/stringifySafe';\ntype ExceptionData = any;\n\nconst BABEL_TRANSFORM_ERROR_FORMAT =\n  /^(?:TransformError )?(?:SyntaxError: |ReferenceError: )(.*): (.*) \\((\\d+):(\\d+)\\)\\n\\n([\\s\\S]+)/;\nconst BABEL_CODE_FRAME_ERROR_FORMAT =\n  /^(?:TransformError )?(?:.*):? (?:.*?)(\\/.*): ([\\s\\S]+?)\\n([ >]{2}[\\d\\s]+ \\|[\\s\\S]+|\\u{001b}[\\s\\S]+)/u;\nconst METRO_ERROR_FORMAT =\n  /^(?:InternalError Metro has encountered an error:) (.*): (.*) \\((\\d+):(\\d+)\\)\\n\\n([\\s\\S]+)/u;\n\nexport type ExtendedExceptionData = ExceptionData & {\n  isComponentError: boolean;\n  [key: string]: any;\n};\nexport type Category = string;\nexport type CodeFrame = {\n  content: string;\n  location?: {\n    row: number;\n    column: number;\n    [key: string]: any;\n  } | null;\n  fileName: string;\n\n  // TODO: When React switched to using call stack frames,\n  // we gained the ability to use the collapse flag, but\n  // it is not integrated into the LogBox UI.\n  collapse?: boolean;\n};\n\nexport type Message = {\n  content: string;\n  substitutions: {\n    length: number;\n    offset: number;\n  }[];\n};\n\nexport type ComponentStack = CodeFrame[];\n\nconst SUBSTITUTION = '\\ufeff%s';\n\nexport function parseInterpolation(args: readonly any[]): {\n  category: Category;\n  message: Message;\n} {\n  const categoryParts: string[] = [];\n  const contentParts: string[] = [];\n  const substitutionOffsets: { length: number; offset: number }[] = [];\n\n  const remaining = [...args];\n  if (typeof remaining[0] === 'string') {\n    const formatString = String(remaining.shift());\n    const formatStringParts = formatString.split('%s');\n    const substitutionCount = formatStringParts.length - 1;\n    const substitutions = remaining.splice(0, substitutionCount);\n\n    let categoryString = '';\n    let contentString = '';\n\n    let substitutionIndex = 0;\n    for (const formatStringPart of formatStringParts) {\n      categoryString += formatStringPart;\n      contentString += formatStringPart;\n\n      if (substitutionIndex < substitutionCount) {\n        if (substitutionIndex < substitutions.length) {\n          // Don't stringify a string type.\n          // It adds quotation mark wrappers around the string,\n          // which causes the LogBox to look odd.\n          const substitution =\n            typeof substitutions[substitutionIndex] === 'string'\n              ? substitutions[substitutionIndex]\n              : stringifySafe(substitutions[substitutionIndex]);\n          substitutionOffsets.push({\n            length: substitution.length,\n            offset: contentString.length,\n          });\n\n          categoryString += SUBSTITUTION;\n          contentString += substitution;\n        } else {\n          substitutionOffsets.push({\n            length: 2,\n            offset: contentString.length,\n          });\n\n          categoryString += '%s';\n          contentString += '%s';\n        }\n\n        substitutionIndex++;\n      }\n    }\n\n    categoryParts.push(categoryString);\n    contentParts.push(contentString);\n  }\n\n  const remainingArgs = remaining.map((arg) => {\n    // Don't stringify a string type.\n    // It adds quotation mark wrappers around the string,\n    // which causes the LogBox to look odd.\n    return typeof arg === 'string' ? arg : stringifySafe(arg);\n  });\n  categoryParts.push(...remainingArgs);\n  contentParts.push(...remainingArgs);\n\n  return {\n    category: categoryParts.join(' '),\n    message: {\n      content: contentParts.join(' '),\n      substitutions: substitutionOffsets,\n    },\n  };\n}\n\nfunction isComponentStack(consoleArgument: string) {\n  const isOldComponentStackFormat = / {4}in/.test(consoleArgument);\n  const isNewComponentStackFormat = / {4}at/.test(consoleArgument);\n  const isNewJSCComponentStackFormat = /@.*\\n/.test(consoleArgument);\n\n  return isOldComponentStackFormat || isNewComponentStackFormat || isNewJSCComponentStackFormat;\n}\n\nexport function parseComponentStack(message: string): ComponentStack {\n  // In newer versions of React, the component stack is formatted as a call stack frame.\n  // First try to parse the component stack as a call stack frame, and if that doesn't\n  // work then we'll fallback to the old custom component stack format parsing.\n  const stack = parseErrorStack(message);\n  if (stack && stack.length > 0) {\n    return stack.map((frame) => ({\n      content: frame.methodName,\n      collapse: frame.collapse || false,\n      fileName: frame.file == null ? 'unknown' : frame.file,\n      location: {\n        column: frame.column == null ? -1 : frame.column,\n        row: frame.lineNumber == null ? -1 : frame.lineNumber,\n      },\n    }));\n  }\n\n  return message\n    .split(/\\n {4}in /g)\n    .map((s) => {\n      if (!s) {\n        return null;\n      }\n      const match = s.match(/(.*) \\(at (.*\\.js):([\\d]+)\\)/);\n      if (!match) {\n        return null;\n      }\n\n      const [content, fileName, row] = match.slice(1);\n      return {\n        content,\n        fileName,\n        location: { column: -1, row: parseInt(row, 10) },\n      };\n    })\n    .filter(Boolean) as ComponentStack;\n}\n\nexport function parseLogBoxException(error: ExtendedExceptionData): LogBoxLogData {\n  const message = error.originalMessage != null ? error.originalMessage : 'Unknown';\n\n  const metroInternalError = message.match(METRO_ERROR_FORMAT);\n  if (metroInternalError) {\n    const [content, fileName, row, column, codeFrame] = metroInternalError.slice(1);\n\n    return {\n      level: 'fatal',\n      type: 'Metro Error',\n      stack: [],\n      isComponentError: false,\n      componentStack: [],\n      codeFrame: {\n        fileName,\n        location: {\n          row: parseInt(row, 10),\n          column: parseInt(column, 10),\n        },\n        content: codeFrame,\n      },\n      message: {\n        content,\n        substitutions: [],\n      },\n      category: `${fileName}-${row}-${column}`,\n    };\n  }\n\n  const babelTransformError = message.match(BABEL_TRANSFORM_ERROR_FORMAT);\n  if (babelTransformError) {\n    // Transform errors are thrown from inside the Babel transformer.\n    const [fileName, content, row, column, codeFrame] = babelTransformError.slice(1);\n\n    return {\n      level: 'syntax',\n      stack: [],\n      isComponentError: false,\n      componentStack: [],\n      codeFrame: {\n        fileName,\n        location: {\n          row: parseInt(row, 10),\n          column: parseInt(column, 10),\n        },\n        content: codeFrame,\n      },\n      message: {\n        content,\n        substitutions: [],\n      },\n      category: `${fileName}-${row}-${column}`,\n    };\n  }\n\n  const babelCodeFrameError = message.match(BABEL_CODE_FRAME_ERROR_FORMAT);\n\n  if (babelCodeFrameError) {\n    // Codeframe errors are thrown from any use of buildCodeFrameError.\n    const [fileName, content, codeFrame] = babelCodeFrameError.slice(1);\n    return {\n      level: 'syntax',\n      stack: [],\n      isComponentError: false,\n      componentStack: [],\n      codeFrame: {\n        fileName,\n        location: null, // We are not given the location.\n        content: codeFrame,\n      },\n      message: {\n        content,\n        substitutions: [],\n      },\n      category: `${fileName}-${1}-${1}`,\n    };\n  }\n\n  if (message.match(/^TransformError /)) {\n    return {\n      level: 'syntax',\n      stack: error.stack,\n      isComponentError: error.isComponentError,\n      componentStack: [],\n      message: {\n        content: message,\n        substitutions: [],\n      },\n      category: message,\n    };\n  }\n\n  const componentStack = error.componentStack;\n  if (error.isFatal || error.isComponentError) {\n    return {\n      level: 'fatal',\n      stack: error.stack,\n      isComponentError: error.isComponentError,\n      componentStack: componentStack != null ? parseComponentStack(componentStack) : [],\n      ...parseInterpolation([message]),\n    };\n  }\n\n  if (componentStack != null) {\n    // It is possible that console errors have a componentStack.\n    return {\n      level: 'error',\n      stack: error.stack,\n      isComponentError: error.isComponentError,\n      componentStack: parseComponentStack(componentStack),\n      ...parseInterpolation([message]),\n    };\n  }\n\n  // Most `console.error` calls won't have a componentStack. We parse them like\n  // regular logs which have the component stack burried in the message.\n  return {\n    level: 'error',\n    stack: error.stack,\n    isComponentError: error.isComponentError,\n    ...parseLogBoxLog([message]),\n  };\n}\n\nexport function parseLogBoxLog(args: readonly any[]): {\n  componentStack: ComponentStack;\n  category: Category;\n  message: Message;\n} {\n  const message = args[0];\n  let argsWithoutComponentStack: any[] = [];\n  let componentStack: ComponentStack = [];\n\n  // Extract component stack from warnings like \"Some warning%s\".\n  if (typeof message === 'string' && message.slice(-2) === '%s' && args.length > 0) {\n    const lastArg = args[args.length - 1];\n    if (typeof lastArg === 'string' && isComponentStack(lastArg)) {\n      argsWithoutComponentStack = args.slice(0, -1);\n      argsWithoutComponentStack[0] = message.slice(0, -2);\n      componentStack = parseComponentStack(lastArg);\n    }\n  }\n\n  if (componentStack.length === 0) {\n    // Try finding the component stack elsewhere.\n    for (const arg of args) {\n      if (typeof arg === 'string' && isComponentStack(arg)) {\n        // Strip out any messages before the component stack.\n        let messageEndIndex = arg.search(/\\n {4}(in|at) /);\n        if (messageEndIndex < 0) {\n          // Handle JSC component stacks.\n          messageEndIndex = arg.search(/\\n/);\n        }\n        if (messageEndIndex > 0) {\n          argsWithoutComponentStack.push(arg.slice(0, messageEndIndex));\n        }\n\n        componentStack = parseComponentStack(arg);\n      } else {\n        argsWithoutComponentStack.push(arg);\n      }\n    }\n  }\n\n  return {\n    ...parseInterpolation(argsWithoutComponentStack),\n    componentStack,\n  };\n}\n"]}