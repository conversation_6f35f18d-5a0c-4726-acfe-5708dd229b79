{"version": 3, "file": "LogBoxInspectorFooter.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/overlay/LogBoxInspectorFooter.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,kDAA0B;AAC1B,+CAA2E;AAE3E,mDAAoD;AACpD,+DAAiD;AAOjD,SAAgB,qBAAqB,CAAC,KAAY;IAChD,MAAM,GAAG,GAAG,IAAA,2BAAc,GAAE,CAAC;IAE7B,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QAC5C,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI;YACtB,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,MAAM;gBACxB,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,eAAe,sCAAwC,CACtE,CACF,CACR,CAAC;KACH;IAED,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI;QACtB,8BAAC,YAAY,IAAC,IAAI,EAAC,SAAS,EAAC,OAAO,EAAE,KAAK,CAAC,SAAS,GAAI;QACzD,8BAAC,YAAY,IAAC,IAAI,EAAC,UAAU,EAAC,OAAO,EAAE,KAAK,CAAC,UAAU,GAAI,CACtD,CACR,CAAC;AACJ,CAAC;AAnBD,sDAmBC;AAED,SAAS,YAAY,CAAC,EAAE,IAAI,EAAE,OAAO,EAAyC;IAC5E,OAAO,CACL,8BAAC,wBAAS,IAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAC5C,CAAC;IACA,uDAAuD;IACvD,OAAO,EACP,OAAO,GACR,EAAE,EAAE,CAAC,CACJ,8BAAC,mBAAI,IACH,KAAK,EAAE;YACL,YAAY,CAAC,QAAQ;YACrB;gBACE,kCAAkC;gBAClC,kBAAkB,EAAE,OAAO;gBAC3B,eAAe,EAAE,OAAO;oBACtB,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,OAAO;wBACP,CAAC,CAAC,SAAS;wBACX,CAAC,CAAC,WAAW,CAAC,kBAAkB,EAAE;aACvC;SACF;QACD,8BAAC,mBAAI,IAAC,KAAK,EAAE,YAAY,CAAC,OAAO;YAC/B,8BAAC,mBAAI,IAAC,KAAK,EAAE,YAAY,CAAC,KAAK,IAAG,IAAI,CAAQ,CACzC,CACF,CACR,CACS,CACb,CAAC;AACJ,CAAC;AAED,MAAM,YAAY,GAAG,yBAAU,CAAC,MAAM,CAAC;IACrC,QAAQ,EAAE;QACR,IAAI,EAAE,CAAC;QACP,cAAc,EAAE,CAAC;QACjB,WAAW,EAAE,SAAS;QACtB,0EAA0E;KAC3E;IACD,OAAO,EAAE;QACP,UAAU,EAAE,QAAQ;QACpB,MAAM,EAAE,EAAE;QACV,cAAc,EAAE,QAAQ;KACzB;IACD,KAAK,EAAE;QACL,UAAU,EAAE,MAAM;QAClB,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;QAClC,QAAQ,EAAE,EAAE;QACZ,kBAAkB,EAAE,KAAK;QACzB,UAAU,EAAE,EAAE;KACf;CACF,CAAC,CAAC;AAEH,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,EAAE;QACJ,eAAe,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAClD,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,SAAS,EAAE,mBAAmB;aAC/B;SACF,CAAC;QACF,aAAa,EAAE,KAAK;KACrB;IACD,MAAM,EAAE;QACN,IAAI,EAAE,CAAC;KACR;IACD,eAAe,EAAE;QACf,SAAS,EAAE,QAAQ;QACnB,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,EAAE;QACd,UAAU,EAAE,EAAE;QACd,aAAa,EAAE,EAAE;QACjB,SAAS,EAAE,QAAQ;QACnB,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC;KACrC;CACF,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React from 'react';\nimport { Platform, Pressable, StyleSheet, Text, View } from 'react-native';\n\nimport { useSelectedLog } from '../Data/LogContext';\nimport * as LogBoxStyle from '../UI/LogBoxStyle';\n\ntype Props = {\n  onDismiss: () => void;\n  onMinimize: () => void;\n};\n\nexport function LogBoxInspectorFooter(props: Props) {\n  const log = useSelectedLog();\n\n  if (['static', 'syntax'].includes(log.level)) {\n    return (\n      <View style={styles.root}>\n        <View style={styles.button}>\n          <Text style={styles.syntaxErrorText}>This error cannot be dismissed.</Text>\n        </View>\n      </View>\n    );\n  }\n\n  return (\n    <View style={styles.root}>\n      <FooterButton text=\"Dismiss\" onPress={props.onDismiss} />\n      <FooterButton text=\"Minimize\" onPress={props.onMinimize} />\n    </View>\n  );\n}\n\nfunction FooterButton({ text, onPress }: { onPress: () => void; text: string }) {\n  return (\n    <Pressable onPress={onPress} style={{ flex: 1 }}>\n      {({\n        /** @ts-expect-error: react-native types are broken. */\n        hovered,\n        pressed,\n      }) => (\n        <View\n          style={[\n            buttonStyles.safeArea,\n            {\n              // @ts-expect-error: web-only type\n              transitionDuration: '150ms',\n              backgroundColor: pressed\n                ? '#323232'\n                : hovered\n                  ? '#111111'\n                  : LogBoxStyle.getBackgroundColor(),\n            },\n          ]}>\n          <View style={buttonStyles.content}>\n            <Text style={buttonStyles.label}>{text}</Text>\n          </View>\n        </View>\n      )}\n    </Pressable>\n  );\n}\n\nconst buttonStyles = StyleSheet.create({\n  safeArea: {\n    flex: 1,\n    borderTopWidth: 1,\n    borderColor: '#323232',\n    // paddingBottom: DeviceInfo.getConstants().isIPhoneX_deprecated ? 30 : 0,\n  },\n  content: {\n    alignItems: 'center',\n    height: 48,\n    justifyContent: 'center',\n  },\n  label: {\n    userSelect: 'none',\n    color: LogBoxStyle.getTextColor(1),\n    fontSize: 14,\n    includeFontPadding: false,\n    lineHeight: 20,\n  },\n});\n\nconst styles = StyleSheet.create({\n  root: {\n    backgroundColor: LogBoxStyle.getBackgroundColor(1),\n    ...Platform.select({\n      web: {\n        boxShadow: `0 -2px 0 2px #000`,\n      },\n    }),\n    flexDirection: 'row',\n  },\n  button: {\n    flex: 1,\n  },\n  syntaxErrorText: {\n    textAlign: 'center',\n    width: '100%',\n    height: 48,\n    fontSize: 14,\n    lineHeight: 20,\n    paddingTop: 20,\n    paddingBottom: 50,\n    fontStyle: 'italic',\n    color: LogBoxStyle.getTextColor(0.6),\n  },\n});\n"]}