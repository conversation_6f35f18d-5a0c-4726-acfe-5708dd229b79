{"version": 3, "file": "UTF8Decoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/utf-8/UTF8Decoder.ts"], "names": [], "mappings": ";;AACA,sDAAwD;AACxD,oDAAmD;AACnD,0DAA2D;AAC3D,sDAAmD;AAEnD;;;;GAIG;AACH;IAUE,qBAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAE3B,8DAA8D;QAC9D,gEAAgE;QAChE,8DAA8D;QAC9D,oBAAoB;QACpB,qBAAqB,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC;YAC9C,qBAAqB,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC;YAC9C,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC;YAChD,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,GAAG,IAAI;YACrD,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACxD,CAAC;IAED;;;;;;OAMG;IACH,6BAAO,GAAP,UAAQ,MAAc,EAAE,IAAY;QAClC,+DAA+D;QAC/D,gDAAgD;QAChD,IAAI,IAAI,KAAK,2BAAa,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;YAC1D,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,OAAO,wBAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACjC;QAED,gDAAgD;QAChD,IAAI,IAAI,KAAK,2BAAa;YACxB,OAAO,mBAAQ,CAAC;QAElB,gDAAgD;QAChD,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;YAEhC,eAAe;YACf,IAAI,mBAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;gBAC7B,2CAA2C;gBAC3C,OAAO,IAAI,CAAC;aACb;YAED,eAAe;iBACV,IAAI,mBAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;gBAClC,kCAAkC;gBAClC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;gBAE3B,0CAA0C;gBAC1C,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC;aACpC;YAED,eAAe;iBACV,IAAI,mBAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;gBAClC,wDAAwD;gBACxD,IAAI,IAAI,KAAK,IAAI;oBACf,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAClC,wDAAwD;gBACxD,IAAI,IAAI,KAAK,IAAI;oBACf,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAClC,kCAAkC;gBAClC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;gBAC3B,yCAAyC;gBACzC,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,GAAG,CAAC;aACnC;YAED,eAAe;iBACV,IAAI,mBAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;gBAClC,wDAAwD;gBACxD,IAAI,IAAI,KAAK,IAAI;oBACf,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAClC,wDAAwD;gBACxD,IAAI,IAAI,KAAK,IAAI;oBACf,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAClC,kCAAkC;gBAClC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;gBAC3B,yCAAyC;gBACzC,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,GAAG,CAAC;aACnC;YAED,YAAY;iBACP;gBACH,gBAAgB;gBAChB,OAAO,wBAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACjC;YAED,mBAAmB;YACnB,OAAO,IAAI,CAAC;SACb;QAED,+DAA+D;QAC/D,iDAAiD;QACjD,IAAI,CAAC,mBAAO,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,EAAE;YAEtE,yDAAyD;YACzD,6DAA6D;YAC7D,gCAAgC;YAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YACzE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAEhC,6BAA6B;YAC7B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAErB,mBAAmB;YACnB,OAAO,wBAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACjC;QAED,+DAA+D;QAC/D,WAAW;QACX,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,+DAA+D;QAC/D,QAAQ;QACR,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAEnE,uCAAuC;QACvC,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;QAE1B,6DAA6D;QAC7D,YAAY;QACZ,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,iBAAiB;YACjD,OAAO,IAAI,CAAC;QAEd,yCAAyC;QACzC,IAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;QAExC,gEAAgE;QAChE,aAAa;QACb,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzE,qDAAqD;QACrD,OAAO,UAAU,CAAC;IACpB,CAAC;IACH,kBAAC;AAAD,CAAC,AAhJD,IAgJC;AAhJY,kCAAW"}