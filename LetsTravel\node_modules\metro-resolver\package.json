{"name": "metro-resolver", "version": "0.81.5", "description": "🚇 Implementation of Metro's resolution logic.", "main": "src/index.js", "exports": {".": "./src/index.js", "./package.json": "./package.json", "./private/*": "./src/*.js", "./src": "./src/index.js", "./src/*.js": "./src/*.js", "./src/*": "./src/*.js"}, "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "license": "MIT", "engines": {"node": ">=18.18"}, "devDependencies": {"metro": "0.81.5"}, "dependencies": {"flow-enums-runtime": "^0.0.6"}}