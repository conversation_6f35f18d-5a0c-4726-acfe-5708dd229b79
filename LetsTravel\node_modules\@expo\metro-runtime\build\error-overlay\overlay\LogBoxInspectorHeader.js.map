{"version": 3, "file": "LogBoxInspectorHeader.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/overlay/LogBoxInspectorHeader.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,kDAA0B;AAC1B,+CAAkF;AAGlF,mDAA6C;AAC7C,qDAAkD;AAClD,+DAAiD;AAOjD,SAAgB,qBAAqB,CAAC,KAAY;IAChD,MAAM,EAAE,gBAAgB,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,IAAA,oBAAO,GAAE,CAAC;IAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IAE1B,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,EAAE;QAC5B,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACjD,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,MAAM;gBACxB,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,KAAK;oBACvB,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,wBAA0B,CAClD,CACF,CACF,CACR,CAAC;KACH;IAED,MAAM,SAAS,GAAG,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC;IACxE,MAAM,SAAS,GAAG,aAAa,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC;IAExE,MAAM,SAAS,GAAG,OAAO,aAAa,GAAG,CAAC,OAAO,KAAK,EAAE,CAAC;IAEzD,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjD,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,MAAM;YACxB,8BAAC,2BAA2B,IAC1B,QAAQ,EAAE,KAAK,IAAI,CAAC,EACpB,KAAK,EAAE,KAAK,CAAC,KAAK,EAClB,KAAK,EAAE,OAAO,CAAC,6CAA6C,CAAC,EAC7D,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,GAC7C;YACF,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACvB,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,IAAG,SAAS,CAAQ,CAC5C;YACP,8BAAC,2BAA2B,IAC1B,QAAQ,EAAE,KAAK,IAAI,CAAC,EACpB,KAAK,EAAE,KAAK,CAAC,KAAK,EAClB,KAAK,EAAE,OAAO,CAAC,8CAA8C,CAAC,EAC9D,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,GAC7C,CACG,CACF,CACR,CAAC;AACJ,CAAC;AA1CD,sDA0CC;AAED,MAAM,kBAAkB,GAAG,CAAC,KAAe,EAAE,EAAE,CAC7C,CAAC;IACC,IAAI,EAAE;QACJ,OAAO,EAAE,aAAa;QACtB,OAAO,EAAE,WAAW,CAAC,mBAAmB,EAAE;KAC3C;IACD,KAAK,EAAE;QACL,OAAO,EAAE,aAAa;QACtB,OAAO,EAAE,WAAW,CAAC,iBAAiB,EAAE;KACzC;IACD,KAAK,EAAE;QACL,OAAO,EAAE,aAAa;QACtB,OAAO,EAAE,WAAW,CAAC,iBAAiB,EAAE;KACzC;IACD,MAAM,EAAE;QACN,OAAO,EAAE,aAAa;QACtB,OAAO,EAAE,WAAW,CAAC,iBAAiB,EAAE;KACzC;IACD,MAAM,EAAE;QACN,OAAO,EAAE,aAAa;QACtB,OAAO,EAAE,WAAW,CAAC,iBAAiB,EAAE;KACzC;CACF,CAAC,CAAC,KAAK,CAAC,CAAC;AAEZ,SAAS,2BAA2B,CAAC,KAKpC;IACC,OAAO,CACL,8BAAC,2BAAY,IACX,eAAe,EAAE,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC,EAChD,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EACnD,KAAK,EAAE,YAAY,CAAC,MAAM,IACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CACvB,8BAAC,oBAAK,IACJ,MAAM,EAAE,KAAK,CAAC,KAAK,EACnB,SAAS,EAAE,WAAW,CAAC,YAAY,EAAE,EACrC,KAAK,EAAE,YAAY,CAAC,WAAW,GAC/B,CACH,CACY,CAChB,CAAC;AACJ,CAAC;AAED,MAAM,YAAY,GAAG,yBAAU,CAAC,MAAM,CAAC;IACrC,MAAM,EAAE;QACN,UAAU,EAAE,QAAQ;QACpB,cAAc,EAAE,QAAQ;QACxB,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,CAAC;QACd,UAAU,EAAE,CAAC;QACb,YAAY,EAAE,CAAC;KAChB;IACD,WAAW,EAAE;QACX,MAAM,EAAE,EAAE;QACV,KAAK,EAAE,CAAC;KACT;CACF,CAAC,CAAC;AAEH,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,MAAM,EAAE;QACN,eAAe,EAAE,WAAW,CAAC,aAAa,EAAE;KAC7C;IACD,MAAM,EAAE;QACN,eAAe,EAAE,WAAW,CAAC,aAAa,EAAE;KAC7C;IACD,KAAK,EAAE;QACL,eAAe,EAAE,WAAW,CAAC,aAAa,EAAE;KAC7C;IACD,IAAI,EAAE;QACJ,eAAe,EAAE,WAAW,CAAC,eAAe,EAAE;KAC/C;IACD,KAAK,EAAE;QACL,eAAe,EAAE,WAAW,CAAC,aAAa,EAAE;KAC7C;IACD,MAAM,EAAE;QACN,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,QAAQ;QAEpB,iBAAiB,EAAE,CAAC;QACpB,MAAM,EAAE,uBAAQ,CAAC,MAAM,CAAC;YACtB,OAAO,EAAE,EAAE;YACX,GAAG,EAAE,EAAE;SACR,CAAC;KACH;IACD,KAAK,EAAE;QACL,UAAU,EAAE,QAAQ;QACpB,IAAI,EAAE,CAAC;QACP,cAAc,EAAE,QAAQ;KACzB;IACD,SAAS,EAAE;QACT,KAAK,EAAE,WAAW,CAAC,YAAY,EAAE;QACjC,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,KAAK;QACjB,kBAAkB,EAAE,KAAK;QACzB,UAAU,EAAE,EAAE;KACf;IACD,QAAQ,EAAE;QACR,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,wBAAS,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;KACzE;CACF,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React from 'react';\nimport { Image, Platform, StatusBar, StyleSheet, Text, View } from 'react-native';\n\nimport type { LogLevel } from '../Data/LogBoxLog';\nimport { useLogs } from '../Data/LogContext';\nimport { LogBoxButton } from '../UI/LogBoxButton';\nimport * as LogBoxStyle from '../UI/LogBoxStyle';\n\ntype Props = {\n  onSelectIndex: (selectedIndex: number) => void;\n  level: LogLevel;\n};\n\nexport function LogBoxInspectorHeader(props: Props) {\n  const { selectedLogIndex: selectedIndex, logs } = useLogs();\n  const total = logs.length;\n\n  if (props.level === 'syntax') {\n    return (\n      <View style={[styles.safeArea, styles[props.level]]}>\n        <View style={styles.header}>\n          <View style={styles.title}>\n            <Text style={styles.titleText}>Failed to compile</Text>\n          </View>\n        </View>\n      </View>\n    );\n  }\n\n  const prevIndex = selectedIndex - 1 < 0 ? total - 1 : selectedIndex - 1;\n  const nextIndex = selectedIndex + 1 > total - 1 ? 0 : selectedIndex + 1;\n\n  const titleText = `Log ${selectedIndex + 1} of ${total}`;\n\n  return (\n    <View style={[styles.safeArea, styles[props.level]]}>\n      <View style={styles.header}>\n        <LogBoxInspectorHeaderButton\n          disabled={total <= 1}\n          level={props.level}\n          image={require('@expo/metro-runtime/assets/chevron-left.png')}\n          onPress={() => props.onSelectIndex(prevIndex)}\n        />\n        <View style={styles.title}>\n          <Text style={styles.titleText}>{titleText}</Text>\n        </View>\n        <LogBoxInspectorHeaderButton\n          disabled={total <= 1}\n          level={props.level}\n          image={require('@expo/metro-runtime/assets/chevron-right.png')}\n          onPress={() => props.onSelectIndex(nextIndex)}\n        />\n      </View>\n    </View>\n  );\n}\n\nconst backgroundForLevel = (level: LogLevel) =>\n  ({\n    warn: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getWarningDarkColor(),\n    },\n    error: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getErrorDarkColor(),\n    },\n    fatal: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getFatalDarkColor(),\n    },\n    syntax: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getFatalDarkColor(),\n    },\n    static: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getFatalDarkColor(),\n    },\n  })[level];\n\nfunction LogBoxInspectorHeaderButton(props: {\n  disabled: boolean;\n  image: number;\n  level: LogLevel;\n  onPress?: () => void;\n}) {\n  return (\n    <LogBoxButton\n      backgroundColor={backgroundForLevel(props.level)}\n      onPress={props.disabled ? undefined : props.onPress}\n      style={headerStyles.button}>\n      {props.disabled ? null : (\n        <Image\n          source={props.image}\n          tintColor={LogBoxStyle.getTextColor()}\n          style={headerStyles.buttonImage}\n        />\n      )}\n    </LogBoxButton>\n  );\n}\n\nconst headerStyles = StyleSheet.create({\n  button: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    aspectRatio: 1,\n    marginRight: 6,\n    marginLeft: 6,\n    borderRadius: 3,\n  },\n  buttonImage: {\n    height: 14,\n    width: 8,\n  },\n});\n\nconst styles = StyleSheet.create({\n  syntax: {\n    backgroundColor: LogBoxStyle.getFatalColor(),\n  },\n  static: {\n    backgroundColor: LogBoxStyle.getFatalColor(),\n  },\n  fatal: {\n    backgroundColor: LogBoxStyle.getFatalColor(),\n  },\n  warn: {\n    backgroundColor: LogBoxStyle.getWarningColor(),\n  },\n  error: {\n    backgroundColor: LogBoxStyle.getErrorColor(),\n  },\n  header: {\n    flexDirection: 'row',\n    alignItems: 'center',\n\n    paddingHorizontal: 8,\n    height: Platform.select({\n      default: 48,\n      ios: 44,\n    }),\n  },\n  title: {\n    alignItems: 'center',\n    flex: 1,\n    justifyContent: 'center',\n  },\n  titleText: {\n    color: LogBoxStyle.getTextColor(),\n    fontSize: 16,\n    fontWeight: '600',\n    includeFontPadding: false,\n    lineHeight: 20,\n  },\n  safeArea: {\n    paddingTop: process.env.EXPO_OS !== 'ios' ? StatusBar.currentHeight : 40,\n  },\n});\n"]}