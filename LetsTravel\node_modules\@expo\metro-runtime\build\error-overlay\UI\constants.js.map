{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/UI/constants.ts"], "names": [], "mappings": ";;;AAAa,QAAA,SAAS,GACpB,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK;IAC3B,CAAC,CAAC,MAAM;QACN,aAAa;IACf,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,SAAS;QACjC,CAAC,CAAC,UAAU;YACV,WAAW;QACb,CAAC,CAAC,UAAU;YACV,SAAS,CAAC", "sourcesContent": ["export const CODE_FONT =\n  process.env.EXPO_OS === 'ios'\n    ? // iOS\n      'Courier New'\n    : process.env.EXPO_OS === 'android'\n      ? // Android\n        'monospace'\n      : // Default\n        'Courier';\n"]}