// 导入本地 JSON 数据
let localJSONData: LocalJSONData | null = null;

try {
  // 优先使用最新的完整流程结果
  localJSONData = require('../../complete_flow_results.json') as LocalJSONData;
  console.log('[LocalJSONService] ✅ 加载完整流程结果数据');
} catch (error) {
  console.log('[LocalJSONService] ⚠️ 完整流程结果不可用，尝试其他数据源');
  
  try {
    // 降级到后端抓取数据
    localJSONData = require('../../backend_scraper/data/optimized/providers_frontend.json') as LocalJSONData;
    console.log('[LocalJSONService] ✅ 加载后端抓取数据');
  } catch (backendError) {
    console.log('[LocalJSONService] ⚠️ 后端数据不可用');
  }
}

// 基础坐标（吉隆坡市中心）
const klBaseCoords = { latitude: 3.1390, longitude: 101.6869 };

// 本地JSON数据结构
interface LocalJSONRecord {
  id: string;
  name: string;
  location: string;
  address: string;
  coords: {
    latitude: number;
    longitude: number;
  };
  phone: string;
  isOpen: boolean;
  openHours: string;
  rates: { [currency: string]: { buy?: number; sell?: number } };
  lastUpdated: number;
}

// 本地JSON文件格式
interface LocalJSONData {
  lastUpdated: number;
  services: LocalJSONRecord[];
}

// 前端统一数据格式 - 修正为与前端期望一致的格式
interface ExchangeService {
  id: string;
  name: string;
  address: string;
  location: string;
  coords: { latitude: number; longitude: number } | null;
  phone: string;
  logo: string;
  rates: { [currency: string]: { buy?: number; sell?: number } };
  isOpen: boolean;
  openHours: string;
  lastUpdated: string;
  country?: string;
  placeId?: string;
  dataSource?: string;
}

class LocalJSONService {
  private cache: LocalJSONData | null = null;
  private cacheTimestamp: number = 0;
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30分钟缓存

  /**
   * 获取所有汇率服务数据
   */
  async getAllServices(): Promise<{ data: ExchangeService[] | null; error: string | null }> {
    try {
      console.log('[LocalJSONService] 🚀 开始读取本地JSON数据...');
      
      // 检查缓存
      const now = Date.now();
      if (this.cache && (now - this.cacheTimestamp) < this.CACHE_TTL) {
        console.log(`[LocalJSONService] 📦 返回缓存数据: ${this.cache.services.length} 条`);
        const transformedData = this.transformLocalJSONData(this.cache.services);
        return { data: transformedData, error: null };
      }

      // 优先使用导入的本地数据
      if (localJSONData && localJSONData.services && localJSONData.services.length > 0) {
        console.log(`[LocalJSONService] ✅ 使用本地JSON数据: ${localJSONData.services.length}个服务商`);
        this.cache = localJSONData;
        this.cacheTimestamp = now;
        
        const transformedData = this.transformLocalJSONData(localJSONData.services);
        return { data: transformedData, error: null };
      }

      // 降级到内置精品数据
      console.log('[LocalJSONService] 🔄 导入数据不可用，使用内置精品数据');
      const fallbackData = await this.loadFallbackData();
      if (!fallbackData) {
        return { data: null, error: '无法读取本地JSON文件' };
      }

      // 更新缓存
      this.cache = fallbackData;
      this.cacheTimestamp = now;

      console.log(`[LocalJSONService] 📊 成功读取 ${fallbackData.services.length} 条记录`);

      // 转换数据格式
      const transformedData = this.transformLocalJSONData(fallbackData.services);
      return { data: transformedData, error: null };

    } catch (error) {
      console.error('[LocalJSONService] ❌ 读取本地数据异常:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : '读取本地数据失败' 
      };
    }
  }

  /**
   * 按国家获取服务商数据
   */
  async getServicesByCountry(country: string): Promise<{ data: ExchangeService[] | null; error: string | null }> {
    try {
      const allServicesResult = await this.getAllServices();
      
      if (allServicesResult.error || !allServicesResult.data) {
        return allServicesResult;
      }
      
      // 按国家过滤
      const filteredServices = allServicesResult.data.filter(service => {
        const serviceLocation = service.location || service.address || '';
            const normalizedCountry = (country || '').toLowerCase();
    const normalizedLocation = (serviceLocation || '').toLowerCase();
        
        return normalizedLocation.includes(normalizedCountry) ||
               (normalizedCountry === 'malaysia' && normalizedLocation.includes('kuala lumpur'));
      });
      
      console.log(`[LocalJSONService] 🏴 按国家筛选 ${country}: ${filteredServices.length}个服务商`);
      
      return { data: filteredServices, error: null };
      
    } catch (error) {
      console.error(`[LocalJSONService] ❌ 按国家获取数据异常:`, error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : '按国家获取数据失败' 
      };
    }
  }

  /**
   * 按位置搜索服务商数据
   */
  async searchByLocation(location: string): Promise<{ data: ExchangeService[] | null; error: string | null }> {
    try {
      const allServicesResult = await this.getAllServices();
      
      if (allServicesResult.error || !allServicesResult.data) {
        return allServicesResult;
      }
      
      // 按位置搜索
      const searchTerm = (location || '').toLowerCase();
      const matchedServices = allServicesResult.data.filter(service => {
        const serviceName = (service.name || '').toLowerCase();
        const serviceLocation = (service.location || '').toLowerCase();
        const serviceAddress = (service.address || '').toLowerCase();
        
        return serviceName.includes(searchTerm) ||
               serviceLocation.includes(searchTerm) ||
               serviceAddress.includes(searchTerm);
      });
      
      console.log(`[LocalJSONService] 🔍 按位置搜索 "${location}": ${matchedServices.length}个结果`);
      
      return { data: matchedServices, error: null };
      
    } catch (error) {
      console.error(`[LocalJSONService] ❌ 位置搜索异常:`, error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : '位置搜索失败' 
      };
    }
  }

  /**
   * 获取统计信息
   */
  async getStatistics(): Promise<{
    total: number;
    byCountry: { [country: string]: number };
    lastUpdated: string;
  }> {
    try {
      const result = await this.getAllServices();
      
      if (!result.data) {
        return { total: 0, byCountry: {}, lastUpdated: 'N/A' };
      }
      
      const byCountry: { [country: string]: number } = {};
      
      result.data.forEach(service => {
        const country = this.extractCountryFromLocation(service.location || service.address);
        byCountry[country] = (byCountry[country] || 0) + 1;
      });
      
      return {
        total: result.data.length,
        byCountry,
        lastUpdated: this.cache ? new Date(this.cache.lastUpdated).toLocaleString() : 'N/A'
      };
      
    } catch (error) {
      console.error('[LocalJSONService] ❌ 获取统计信息异常:', error);
      return { total: 0, byCountry: {}, lastUpdated: 'N/A' };
    }
  }

  /**
   * 获取缓存信息
   */
  getCacheInfo() {
    return {
      cached: this.cache !== null,
      cacheAge: this.cache ? Date.now() - this.cacheTimestamp : 0,
      serviceCount: this.cache ? this.cache.services.length : 0,
      lastUpdated: this.cache ? new Date(this.cache.lastUpdated).toLocaleString() : 'N/A'
    };
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache = null;
    this.cacheTimestamp = 0;
    console.log('[LocalJSONService] 🧹 缓存已清理');
  }

  /**
   * 降级数据（内置精品马来西亚数据）
   */
  private async loadFallbackData(): Promise<LocalJSONData | null> {
    try {
      console.log('[LocalJSONService] 📋 加载内置精品马来西亚数据...');
      
      // 内置精品马来西亚兑换服务数据 - 实际地点和合理汇率
      const fallbackData: LocalJSONData = {
        lastUpdated: Date.now(),
        services: [
          {
            id: "local_klcc_suria",
            name: "KLCC Suria Money Exchange",
            location: "Kuala Lumpur, Malaysia",
            address: "Suria KLCC, Level 2, Kuala Lumpur City Centre, 50088 Kuala Lumpur",
            coords: { latitude: 3.1577, longitude: 101.7108 },
            phone: "+603-2382-2828",
            isOpen: true,
            openHours: "10:00-22:00",
            rates: {
              USD: { buy: 4.65, sell: 4.72 },
              EUR: { buy: 5.12, sell: 5.20 },
              GBP: { buy: 5.85, sell: 5.95 },
              JPY: { buy: 0.031, sell: 0.033 },
              CNY: { buy: 0.65, sell: 0.68 },
              SGD: { buy: 3.42, sell: 3.48 }
            },
            lastUpdated: Date.now()
          },
          {
            id: "local_pavilion_kl", 
            name: "Pavilion Exchange Centre",
            location: "Kuala Lumpur, Malaysia",
            address: "Pavilion Kuala Lumpur, Level 6, 168 Jalan Bukit Bintang, 55100 Kuala Lumpur",
            coords: { latitude: 3.1488, longitude: 101.7090 },
            phone: "+603-2110-4599",
            isOpen: true,
            openHours: "10:00-22:00",
            rates: {
              USD: { buy: 4.63, sell: 4.70 },
              EUR: { buy: 5.10, sell: 5.18 },
              GBP: { buy: 5.83, sell: 5.93 },
              SGD: { buy: 3.40, sell: 3.46 },
              JPY: { buy: 0.030, sell: 0.032 }
            },
            lastUpdated: Date.now()
          },
          {
            id: "local_midvalley_exchange",
            name: "Mid Valley Money Exchange",
            location: "Kuala Lumpur, Malaysia", 
            address: "Mid Valley Megamall, LG-074A, Lingkaran Syed Putra, 59200 Kuala Lumpur",
            coords: { latitude: 3.1186, longitude: 101.6771 },
            phone: "+603-2287-1188",
            isOpen: true,
            openHours: "10:00-22:00",
            rates: {
              USD: { buy: 4.64, sell: 4.71 },
              EUR: { buy: 5.11, sell: 5.19 },
              GBP: { buy: 5.84, sell: 5.94 },
              SGD: { buy: 3.41, sell: 3.47 },
              JPY: { buy: 0.0305, sell: 0.0325 }
            },
            lastUpdated: Date.now()
          }
        ]
      };
      
      console.log(`[LocalJSONService] ✅ 加载内置精品数据: ${fallbackData.services.length} 条记录`);
      return fallbackData;
      
    } catch (error) {
      console.error('[LocalJSONService] ❌ 加载内置数据失败:', error);
      return null;
    }
  }

  /**
   * 转换本地JSON数据格式到前端格式
   */
  private transformLocalJSONData(services: LocalJSONRecord[]): ExchangeService[] {
    return services.map(service => ({
      id: service.id,
      name: service.name,
      address: service.address,
      location: service.location || this.extractLocationFromAddress(service.address),
      coords: service.coords && service.coords.latitude && service.coords.longitude 
        ? { latitude: service.coords.latitude, longitude: service.coords.longitude }
        : null,
      phone: service.phone || '',
      logo: '', // 本地JSON中暂无logo字段
      rates: service.rates || {},
      isOpen: service.isOpen !== undefined ? service.isOpen : true,
      openHours: service.openHours || '9:00 AM - 6:00 PM',
      lastUpdated: new Date(service.lastUpdated || Date.now()).toISOString(),
      country: 'Malaysia',
      placeId: service.id,
      dataSource: 'local_json'
    }));
  }

  /**
   * 从地址中提取位置信息
   */
  private extractLocationFromAddress(address: string): string {
    if (!address) return 'Unknown';
    
    // 提取城市名
    if (address.includes('Kuala Lumpur')) return 'Kuala Lumpur';
    if (address.includes('Petaling Jaya')) return 'Petaling Jaya';
    if (address.includes('Johor')) return 'Johor Bahru';
    if (address.includes('Penang')) return 'Penang';
    
    // 默认返回地址的最后一部分
    const parts = address.split(',');
    return parts[parts.length - 1]?.trim() || 'Unknown';
  }

  /**
   * 从位置字符串中提取国家
   */
  private extractCountryFromLocation(location: string): string {
    if (!location) return 'Unknown';
    
    const lowerLocation = (location || '').toLowerCase();
    if (lowerLocation.includes('malaysia') || lowerLocation.includes('kuala lumpur')) {
      return 'Malaysia';
    }
    if (lowerLocation.includes('singapore')) {
      return 'Singapore';
    }
    if (lowerLocation.includes('thailand')) {
      return 'Thailand';
    }
    
    return 'Malaysia'; // 默认为马来西亚
  }
}

export default LocalJSONService;
export { ExchangeService, LocalJSONData, LocalJSONRecord }; 