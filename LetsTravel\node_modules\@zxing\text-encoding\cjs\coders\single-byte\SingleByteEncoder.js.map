{"version": 3, "file": "SingleByteEncoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/single-byte/SingleByteEncoder.ts"], "names": [], "mappings": ";;AACA,sDAAwD;AACxD,oDAAmD;AACnD,kDAAyD;AACzD,0DAA6E;AAE7E;;;;;GAKG;AACH;IAIE,2BAAoB,KAAwB,EAAE,OAA4B;QAAtD,UAAK,GAAL,KAAK,CAAmB;QAC1C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,mCAAO,GAAP,UAAQ,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,2BAAa;YAC9B,OAAO,mBAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,8BAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,8DAA8D;QAC9D,eAAe;QACf,IAAM,OAAO,GAAG,yBAAe,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAExD,uDAAuD;QACvD,IAAI,OAAO,KAAK,IAAI;YAClB,wBAAY,CAAC,UAAU,CAAC,CAAC;QAE3B,kDAAkD;QAClD,OAAO,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IACH,wBAAC;AAAD,CAAC,AAlCD,IAkCC;AAlCY,8CAAiB"}