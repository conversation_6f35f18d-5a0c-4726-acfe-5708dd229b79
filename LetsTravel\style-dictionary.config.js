// 简化的 style-dictionary 配置
// const StyleDictionary = require('style-dictionary');

// 注册自定义格式
StyleDictionary.registerFormat({
  name: 'typescript/es6-declarations',
  formatter: function(dictionary) {
    return `// 🎨 Trekmate Design Tokens - Auto Generated
// Do not edit this file directly, edit design-tokens/trekmate-tokens-v1.0.json instead

export const Colors = {
${dictionary.allTokens
  .filter(token => token.type === 'color' || token.path[0] === 'color')
  .map(token => `  ${token.name}: '${token.value}',`)
  .join('\n')}
} as const;

export const Spacing = {
${dictionary.allTokens
  .filter(token => token.path[0] === 'spacing')
  .map(token => `  ${token.name}: ${parseInt(token.value)},`)
  .join('\n')}
} as const;

export const BorderRadius = {
${dictionary.allTokens
  .filter(token => token.path[0] === 'border-radius')
  .map(token => `  ${token.name}: ${token.value === '9999px' ? 9999 : parseInt(token.value)},`)
  .join('\n')}
} as const;

export const Typography = {
  heading: {
    fontFamily: 'Poppins',
    fontWeight: '600',
    sizes: {
      sm: 20,
      md: 28,
      lg: 36,
    },
  },
  body: {
    fontFamily: 'Inter',
    fontWeight: '400',
    sizes: {
      sm: 14,
      md: 16,
      lg: 18,
    },
  },
  slogan: {
    fontFamily: 'Open Sans',
    fontWeight: '300',
    fontStyle: 'italic',
    size: 16,
  },
} as const;

export type ColorKeys = keyof typeof Colors;
export type SpacingKeys = keyof typeof Spacing;
export type BorderRadiusKeys = keyof typeof BorderRadius;
`;
  }
});

module.exports = {
  source: ['design-tokens/**/*.json'],
  platforms: {
    typescript: {
      transformGroup: 'js',
      buildPath: 'constants/',
      files: [{
        destination: 'DesignTokens.ts',
        format: 'typescript/es6-declarations'
      }]
    }
  }
};
