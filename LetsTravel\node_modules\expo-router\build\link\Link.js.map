{"version": 3, "file": "Link.js", "sourceRoot": "", "sources": ["../../src/link/Link.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,wFAAwF;AACxF,mCAAmC;AACnC,qDAA4C;AAC5C,6CAA+B;AAC/B,+CAAgF;AAEhF,iCAAqC;AACrC,8EAAsD;AACtD,oCAAqC;AAErC,sDAAmD;AAiFnD,iEAAiE;AACjE,SAAgB,QAAQ,CAAC,EAAE,IAAI,EAAkB;IAC/C,MAAM,MAAM,GAAG,IAAA,iBAAS,GAAE,CAAC;IAC3B,IAAA,+BAAc,EAAC,GAAG,EAAE;QAClB,IAAI;YACF,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACtB;IACH,CAAC,CAAC,CAAC;IACH,OAAO,IAAI,CAAC;AACd,CAAC;AAVD,4BAUC;AAED;;;;;;;;;;GAUG;AACU,QAAA,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,cAAc,CAA6B,CAAC;AAEjF,YAAI,CAAC,WAAW,GAAG,kBAAW,CAAC;AAE/B,qDAAqD;AACrD,SAAS,mBAAmB,CAAC,KAAyD;IACpF,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;QACzB,OAAO,KAAK,CAAC,KAAK,CAAC;KACpB;IAED,sDAAsD;IACtD,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACxB,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,EAAE;YAC3B,OAAO,KAAK,CAAC,KAAK,CAAC;SACpB;QACD,MAAM,QAAQ,GAAG;YACf,KAAK,EAAE,IAAI;YACX,qBAAqB,EAAE,KAAK,CAAC,SAAS;SACvC,CAAC;QAEF,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC9B,OAAO,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;SACnC;QACD,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACjC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACrC,CAAC;AAED,MAAM,YAAY,GAAG,uBAAQ,CAAC,MAAM,CAElC;IACA,GAAG,EAAE,SAAS,YAAY,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAA2B;QACpF,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;YACxB,MAAM,SAAS,GAAG;gBAChB,GAAG;gBACH,MAAM;gBACN,QAAQ;aACT,CAAC;YACF,IAAI,OAAO,EAAE;gBACX,OAAO,SAAS,CAAC;aAClB;YACD,OAAO;gBACL,SAAS;aACV,CAAC;QACJ,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,OAAO,EAAE,SAAS,YAAY;QAC5B,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,cAAc,CACrB,EACE,IAAI,EACJ,OAAO,EACP,IAAI;AACJ,yDAAyD;AACzD,OAAO,EACP,GAAG,EACH,MAAM,EACN,QAAQ,EACR,GAAG,IAAI,EACQ,EACjB,GAA6B;IAE7B,qDAAqD;IACrD,MAAM,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAExC,+GAA+G;IAC/G,MAAM,SAAS,GAAG,YAAY,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;IAEnE,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACtC,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;SAC3C;QACD,OAAO,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC;IAC3B,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAEX,IAAI,KAAK,CAAC;IACV,IAAI,IAAI;QAAE,KAAK,GAAG,MAAM,CAAC;IACzB,IAAI,OAAO;QAAE,KAAK,GAAG,SAAS,CAAC;IAE/B,MAAM,KAAK,GAAG,IAAA,4BAAkB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;IAEhE,MAAM,OAAO,GAAG,CAAC,CAA0E,EAAE,EAAE;QAC7F,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;SACnB;QACD,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,iBAAI,CAAC,CAAC,CAAC,mBAAI,CAAC;IAEtC,6HAA6H;IAC7H,OAAO,CACL,CAAC,OAAO,CACN,GAAG,CAAC,CAAC,GAAG,CAAC,CACT,IAAI,KAAK,CAAC,CACV,IAAI,SAAS,CAAC,CACd,IAAI,IAAI,CAAC,CACT,KAAK,CAAC,CAAC,KAAK,CAAC,CACb,IAAI,uBAAQ,CAAC,MAAM,CAAC;QAClB,GAAG,EAAE;YACH,OAAO,EAAE,OAAO;SACV;QACR,OAAO,EAAE,EAAE,OAAO,EAAE;KACrB,CAAC,CAAC,EACH,CACH,CAAC;AACJ,CAAC", "sourcesContent": ["// Fork of @react-navigation/native Link.tsx with `href` and `replace` support added and\n// `to` / `action` support removed.\nimport { Slot } from '@radix-ui/react-slot';\nimport * as React from 'react';\nimport { Text, TextProps, GestureResponderEvent, Platform } from 'react-native';\n\nimport { resolveHref } from './href';\nimport useLinkToPathProps from './useLinkToPathProps';\nimport { useRouter } from '../hooks';\nimport { Href } from '../types';\nimport { useFocusEffect } from '../useFocusEffect';\n\nexport interface WebAnchorProps {\n  /**\n   * **Web only:** Specifies where to open the `href`.\n   *\n   * - **_self**: the current tab.\n   * - **_blank**: opens in a new tab or window.\n   * - **_parent**: opens in the parent browsing context. If no parent, defaults to **_self**.\n   * - **_top**: opens in the highest browsing context ancestor. If no ancestors, defaults to **_self**.\n   *\n   * This property is passed to the underlying anchor (`<a>`) tag.\n   *\n   * @default '_self'\n   *\n   * @example\n   * <Link href=\"https://expo.dev\" target=\"_blank\">Go to Expo in new tab</Link>\n   */\n  target?: '_self' | '_blank' | '_parent' | '_top' | (string & object);\n\n  /**\n   * **Web only:** Specifies the relationship between the `href` and the current route.\n   *\n   * Common values:\n   * - **nofollow**: Indicates to search engines that they should not follow the `href`. This is often used for user-generated content or links that should not influence search engine rankings.\n   * - **noopener**: Suggests that the `href` should not have access to the opening window's `window.opener` object, which is a security measure to prevent potentially harmful behavior in cases of links that open new tabs or windows.\n   * - **noreferrer**: Requests that the browser not send the `Referer` HTTP header when navigating to the `href`. This can enhance user privacy.\n   *\n   * The `rel` property is primarily used for informational and instructive purposes, helping browsers and web\n   * crawlers make better decisions about how to handle and interpret the links on a web page. It is important\n   * to use appropriate `rel` values to ensure that links behave as intended and adhere to best practices for web\n   * development and SEO (Search Engine Optimization).\n   *\n   * This property is passed to the underlying anchor (`<a>`) tag.\n   *\n   * @example\n   * <Link href=\"https://expo.dev\" rel=\"nofollow\">Go to Expo</Link>\n   */\n  rel?: string;\n\n  /**\n   * **Web only:** Specifies that the `href` should be downloaded when the user clicks on the link,\n   * instead of navigating to it. It is typically used for links that point to files that the user should download,\n   * such as PDFs, images, documents, etc.\n   *\n   * The value of the `download` property, which represents the filename for the downloaded file.\n   * This property is passed to the underlying anchor (`<a>`) tag.\n   *\n   * @example\n   * <Link href=\"/image.jpg\" download=\"my-image.jpg\">Download image</Link>\n   */\n  download?: string;\n}\n\nexport interface LinkProps<T extends string | object>\n  extends Omit<TextProps, 'href'>,\n    WebAnchorProps {\n  /** Path to route to. */\n  href: Href<T>;\n\n  // TODO(EvanBacon): This may need to be extracted for React Native style support.\n  /** Forward props to child component. Useful for custom buttons. */\n  asChild?: boolean;\n\n  /** Should replace the current route without adding to the history. */\n  replace?: boolean;\n  /** Should push the current route  */\n  push?: boolean;\n\n  /** On web, this sets the HTML `class` directly. On native, this can be used with CSS interop tools like Nativewind. */\n  className?: string;\n\n  onPress?: (e: React.MouseEvent<HTMLAnchorElement, MouseEvent> | GestureResponderEvent) => void;\n}\n\nexport interface LinkComponent {\n  <T extends string | object>(props: React.PropsWithChildren<LinkProps<T>>): JSX.Element;\n  /** Helper method to resolve an Href object into a string. */\n  resolveHref: (href: Href) => string;\n}\n\n/** Redirects to the href as soon as the component is mounted. */\nexport function Redirect({ href }: { href: Href }) {\n  const router = useRouter();\n  useFocusEffect(() => {\n    try {\n      router.replace(href);\n    } catch (error) {\n      console.error(error);\n    }\n  });\n  return null;\n}\n\n/**\n * Component to render link to another route using a path.\n * Uses an anchor tag on the web.\n *\n * @param props.href Absolute path to route (e.g. `/feeds/hot`).\n * @param props.replace Should replace the current route without adding to the history.\n * @param props.push Should push the current route, always adding to the history.\n * @param props.asChild Forward props to child component. Useful for custom buttons.\n * @param props.children Child elements to render the content.\n * @param props.className On web, this sets the HTML `class` directly. On native, this can be used with CSS interop tools like Nativewind.\n */\nexport const Link = React.forwardRef(ExpoRouterLink) as unknown as LinkComponent;\n\nLink.resolveHref = resolveHref;\n\n// Mutate the style prop to add the className on web.\nfunction useInteropClassName(props: { style?: TextProps['style']; className?: string }) {\n  if (Platform.OS !== 'web') {\n    return props.style;\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return React.useMemo(() => {\n    if (props.className == null) {\n      return props.style;\n    }\n    const cssStyle = {\n      $$css: true,\n      __routerLinkClassName: props.className,\n    };\n\n    if (Array.isArray(props.style)) {\n      return [...props.style, cssStyle];\n    }\n    return [props.style, cssStyle];\n  }, [props.style, props.className]);\n}\n\nconst useHrefAttrs = Platform.select<\n  (props: Partial<LinkProps<any>>) => { hrefAttrs?: any } & Partial<LinkProps<any>>\n>({\n  web: function useHrefAttrs({ asChild, rel, target, download }: Partial<LinkProps<any>>) {\n    return React.useMemo(() => {\n      const hrefAttrs = {\n        rel,\n        target,\n        download,\n      };\n      if (asChild) {\n        return hrefAttrs;\n      }\n      return {\n        hrefAttrs,\n      };\n    }, [asChild, rel, target, download]);\n  },\n  default: function useHrefAttrs() {\n    return {};\n  },\n});\n\nfunction ExpoRouterLink(\n  {\n    href,\n    replace,\n    push,\n    // TODO: This does not prevent default on the anchor tag.\n    asChild,\n    rel,\n    target,\n    download,\n    ...rest\n  }: LinkProps<any>,\n  ref: React.ForwardedRef<Text>\n) {\n  // Mutate the style prop to add the className on web.\n  const style = useInteropClassName(rest);\n\n  // If not passing asChild, we need to forward the props to the anchor tag using React Native Web's `hrefAttrs`.\n  const hrefAttrs = useHrefAttrs({ asChild, rel, target, download });\n\n  const resolvedHref = React.useMemo(() => {\n    if (href == null) {\n      throw new Error('Link: href is required');\n    }\n    return resolveHref(href);\n  }, [href]);\n\n  let event;\n  if (push) event = 'PUSH';\n  if (replace) event = 'REPLACE';\n\n  const props = useLinkToPathProps({ href: resolvedHref, event });\n\n  const onPress = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent> | GestureResponderEvent) => {\n    if ('onPress' in rest) {\n      rest.onPress?.(e);\n    }\n    props.onPress(e);\n  };\n\n  const Element = asChild ? Slot : Text;\n\n  // Avoid using createElement directly, favoring JSX, to allow tools like Nativewind to perform custom JSX handling on native.\n  return (\n    <Element\n      ref={ref}\n      {...props}\n      {...hrefAttrs}\n      {...rest}\n      style={style}\n      {...Platform.select({\n        web: {\n          onClick: onPress,\n        } as any,\n        default: { onPress },\n      })}\n    />\n  );\n}\n"]}