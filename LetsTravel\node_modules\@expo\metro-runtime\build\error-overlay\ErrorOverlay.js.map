{"version": 3, "file": "ErrorOverlay.js", "sourceRoot": "", "sources": ["../../src/error-overlay/ErrorOverlay.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,+CAAgE;AAChE,+CAAsE;AAEtE,8DAAgD;AAEhD,kDAA4D;AAC5D,8DAAgD;AAChD,iFAA8E;AAC9E,2EAA8F;AAC9F,2EAA8F;AAC9F,yFAAsF;AACtF,qFAAkF;AAElF,MAAM,gBAAgB,GAAG;IACvB,IAAI,EAAE,iBAAiB;IACvB,KAAK,EAAE,eAAe;IACtB,KAAK,EAAE,gBAAgB;IACvB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,kCAAkC;IAC1C,SAAS,EAAE,cAAc;CAC1B,CAAC;AAEF,SAAgB,wBAAwB;IACtC,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,GAAG,IAAA,oBAAO,GAAE,CAAC;IAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACnC,IAAI,GAAG,IAAI,IAAI,EAAE;QACf,OAAO,IAAI,CAAC;KACb;IACD,OAAO,8BAAC,eAAe,IAAC,GAAG,EAAE,GAAG,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAI,GAAI,CAAC;AACvF,CAAC;AAPD,4DAOC;AAED,SAAgB,eAAe,CAAC,EAC9B,GAAG,EACH,gBAAgB,EAChB,IAAI,GAKL;IACC,MAAM,SAAS,GAAG,IAAA,mBAAW,EAAC,GAAS,EAAE;QACvC,4DAA4D;QAC5D,qDAAqD;QACrD,8CAA8C;QAC9C,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,gBAAgB,IAAI,IAAI,EAAE;YAC5B,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC7B,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/B;iBAAM,IAAI,gBAAgB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnD,UAAU,CAAC,cAAc,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;aACjD;YAED,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;SACjD;IACH,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAEvB,MAAM,UAAU,GAAG,IAAA,mBAAW,EAAC,GAAS,EAAE;QACxC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,qBAAqB,GAAG,IAAA,mBAAW,EAAC,CAAC,KAAa,EAAQ,EAAE;QAChE,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,IAAI,GAAG,EAAE;YACP,UAAU,CAAC,iBAAiB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAC3C,UAAU,CAAC,iBAAiB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;SAChD;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAEV,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,qDAAqD;QACrD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACnB,MAAM,QAAQ,GAAG,gBAAgB,CAAC;YAClC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAClC,MAAM,SAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;YAC9D,MAAM,SAAS,GAAG,QAAQ,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;YAC9D,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,CAAU,EAAE;gBAClD,UAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACrD,UAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aACtD;SACF;IACH,CAAC,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAE7B,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,uBAAQ,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,YAAY,GAAG,IAAA,mBAAW,EAC9B,CAAC,IAAe,EAAE,EAAE;QAClB,UAAU,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC/C,CAAC,EACD,CAAC,GAAG,CAAC,CACN,CAAC;IAEF,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS;QAC3B,8BAAC,6CAAkB,IAAC,aAAa,EAAE,qBAAqB,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,GAAI;QAC9E,8BAAC,gBAAgB,IAAC,OAAO,EAAE,YAAY,GAAI;QAC3C,8BAAC,6CAAkB,IAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,GAAI,CAC/D,CACR,CAAC;AACJ,CAAC;AAxED,0CAwEC;AAED,SAAgB,gBAAgB,CAAC,EAAE,OAAO,EAA0C;IAClF,MAAM,GAAG,GAAG,IAAA,2BAAc,GAAE,CAAC;IAC7B,OAAO,8BAAC,wBAAwB,IAAC,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,GAAI,CAAC;AAClE,CAAC;AAHD,4CAGC;AAED,SAAgB,wBAAwB,CAAC,EACvC,GAAG,EACH,OAAO,GAIR;IACC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC;IAEjD,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,YAAY,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAEV,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC;IAEjG,MAAM,MAAM,GAAG,CACb,8BAAC,2DAA4B,IAC3B,SAAS,EAAE,SAAS,EACpB,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,EACvC,OAAO,EAAE,GAAG,CAAC,OAAO,EACpB,KAAK,EAAE,GAAG,CAAC,KAAK,EAChB,KAAK,EAAE,WAAW,GAClB,CACH,CAAC;IAEF,4BAA4B;IAC5B,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAC3C,2EAA2E,CAC5E,CAAC;IAEF,OAAO,CACL;QACG,SAAS,IAAI,MAAM;QACpB,8BAAC,yBAAU,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU;YACjC,CAAC,SAAS,IAAI,MAAM;YAErB,8BAAC,mDAAwB,IAAC,SAAS,EAAE,GAAG,CAAC,SAAS,GAAI;YACrD,UAAU,IAAI,CACb,8BAAC,uDAA0B,IACzB,IAAI,EAAC,OAAO;gBACZ,6CAA6C;gBAC7C,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,GACvC,CACH;YACA,CAAC,CAAC,GAAG,EAAE,cAAc,EAAE,MAAM,IAAI,CAChC,8BAAC,uDAA0B,IACzB,IAAI,EAAC,WAAW;gBAChB,6CAA6C;gBAC7C,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,GAC3C,CACH,CACU,CACZ,CACJ,CAAC;AACJ,CAAC;AAtDD,4DAsDC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,UAAU,EAAE;QACV,eAAe,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAClD,IAAI,EAAE,CAAC;KACR;IACD,SAAS,EAAE;QACT,GAAG,EAAE,CAAC;QACN,IAAI,EAAE,CAAC;QACP,MAAM,EAAE,CAAC;QACT,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,GAAG;QACX,IAAI,EAAE,CAAC;QACP,qEAAqE;QACrE,QAAQ,EAAE,OAAO;KAClB;CACF,CAAC,CAAC;AAEH,kBAAe,UAAU,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React, { useCallback, useEffect, useState } from 'react';\nimport { Keyboard, ScrollView, View, StyleSheet } from 'react-native';\n\nimport * as LogBoxData from './Data/LogBoxData';\nimport { LogBoxLog, StackType } from './Data/LogBoxLog';\nimport { useLogs, useSelectedLog } from './Data/LogContext';\nimport * as LogBoxStyle from './UI/LogBoxStyle';\nimport { LogBoxInspectorCodeFrame } from './overlay/LogBoxInspectorCodeFrame';\nimport { LogBoxInspectorFooter as ErrorOverlayFooter } from './overlay/LogBoxInspectorFooter';\nimport { LogBoxInspectorHeader as ErrorOverlayHeader } from './overlay/LogBoxInspectorHeader';\nimport { LogBoxInspectorMessageHeader } from './overlay/LogBoxInspectorMessageHeader';\nimport { LogBoxInspectorStackFrames } from './overlay/LogBoxInspectorStackFrames';\n\nconst HEADER_TITLE_MAP = {\n  warn: 'Console Warning',\n  error: 'Console Error',\n  fatal: 'Uncaught Error',\n  syntax: 'Syntax Error',\n  static: 'Static Rendering Error (Node.js)',\n  component: 'Render Error',\n};\n\nexport function LogBoxInspectorContainer() {\n  const { selectedLogIndex, logs } = useLogs();\n  const log = logs[selectedLogIndex];\n  if (log == null) {\n    return null;\n  }\n  return <LogBoxInspector log={log} selectedLogIndex={selectedLogIndex} logs={logs} />;\n}\n\nexport function LogBoxInspector({\n  log,\n  selectedLogIndex,\n  logs,\n}: {\n  log: LogBoxLog;\n  selectedLogIndex: number;\n  logs: LogBoxLog[];\n}) {\n  const onDismiss = useCallback((): void => {\n    // Here we handle the cases when the log is dismissed and it\n    // was either the last log, or when the current index\n    // is now outside the bounds of the log array.\n    const logsArray = Array.from(logs);\n    if (selectedLogIndex != null) {\n      if (logsArray.length - 1 <= 0) {\n        LogBoxData.setSelectedLog(-1);\n      } else if (selectedLogIndex >= logsArray.length - 1) {\n        LogBoxData.setSelectedLog(selectedLogIndex - 1);\n      }\n\n      LogBoxData.dismiss(logsArray[selectedLogIndex]);\n    }\n  }, [selectedLogIndex]);\n\n  const onMinimize = useCallback((): void => {\n    LogBoxData.setSelectedLog(-1);\n  }, []);\n\n  const onChangeSelectedIndex = useCallback((index: number): void => {\n    LogBoxData.setSelectedLog(index);\n  }, []);\n\n  useEffect(() => {\n    if (log) {\n      LogBoxData.symbolicateLogNow('stack', log);\n      LogBoxData.symbolicateLogNow('component', log);\n    }\n  }, [log]);\n\n  useEffect(() => {\n    // Optimistically symbolicate the last and next logs.\n    if (logs.length > 1) {\n      const selected = selectedLogIndex;\n      const lastIndex = logs.length - 1;\n      const prevIndex = selected - 1 < 0 ? lastIndex : selected - 1;\n      const nextIndex = selected + 1 > lastIndex ? 0 : selected + 1;\n      for (const type of ['component', 'stack'] as const) {\n        LogBoxData.symbolicateLogLazy(type, logs[prevIndex]);\n        LogBoxData.symbolicateLogLazy(type, logs[nextIndex]);\n      }\n    }\n  }, [logs, selectedLogIndex]);\n\n  useEffect(() => {\n    Keyboard.dismiss();\n  }, []);\n\n  const _handleRetry = useCallback(\n    (type: StackType) => {\n      LogBoxData.retrySymbolicateLogNow(type, log);\n    },\n    [log]\n  );\n\n  return (\n    <View style={styles.container}>\n      <ErrorOverlayHeader onSelectIndex={onChangeSelectedIndex} level={log.level} />\n      <ErrorOverlayBody onRetry={_handleRetry} />\n      <ErrorOverlayFooter onDismiss={onDismiss} onMinimize={onMinimize} />\n    </View>\n  );\n}\n\nexport function ErrorOverlayBody({ onRetry }: { onRetry: (type: StackType) => void }) {\n  const log = useSelectedLog();\n  return <ErrorOverlayBodyContents log={log} onRetry={onRetry} />;\n}\n\nexport function ErrorOverlayBodyContents({\n  log,\n  onRetry,\n}: {\n  log: LogBoxLog;\n  onRetry: (type: StackType) => void;\n}) {\n  const [collapsed, setCollapsed] = useState(true);\n\n  useEffect(() => {\n    setCollapsed(true);\n  }, [log]);\n\n  const headerTitle = HEADER_TITLE_MAP[log.isComponentError ? 'component' : log.level] ?? log.type;\n\n  const header = (\n    <LogBoxInspectorMessageHeader\n      collapsed={collapsed}\n      onPress={() => setCollapsed(!collapsed)}\n      message={log.message}\n      level={log.level}\n      title={headerTitle}\n    />\n  );\n\n  // Hide useless React stack.\n  const needsStack = !log.message.content.match(\n    /(Expected server HTML to contain a matching|Text content did not match\\.)/\n  );\n\n  return (\n    <>\n      {collapsed && header}\n      <ScrollView style={styles.scrollBody}>\n        {!collapsed && header}\n\n        <LogBoxInspectorCodeFrame codeFrame={log.codeFrame} />\n        {needsStack && (\n          <LogBoxInspectorStackFrames\n            type=\"stack\"\n            // eslint-disable-next-line react/jsx-no-bind\n            onRetry={onRetry.bind(onRetry, 'stack')}\n          />\n        )}\n        {!!log?.componentStack?.length && (\n          <LogBoxInspectorStackFrames\n            type=\"component\"\n            // eslint-disable-next-line react/jsx-no-bind\n            onRetry={onRetry.bind(onRetry, 'component')}\n          />\n        )}\n      </ScrollView>\n    </>\n  );\n}\n\nconst styles = StyleSheet.create({\n  scrollBody: {\n    backgroundColor: LogBoxStyle.getBackgroundColor(1),\n    flex: 1,\n  },\n  container: {\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n    zIndex: 999,\n    flex: 1,\n    // @ts-expect-error: fixed is not in the RN types but it works on web\n    position: 'fixed',\n  },\n});\n\nexport default LogBoxData.withSubscription(LogBoxInspectorContainer);\n"]}