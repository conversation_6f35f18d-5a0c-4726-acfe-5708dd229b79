{"version": 3, "file": "GB18030Encoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/gb18030/GB18030Encoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,KAAK,EAAE,4BAA4B,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAC9F,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAG7E;;;;;GAKG;AACH,MAAM,OAAO,cAAc;IAIzB,YAAY,OAA4B,EAAU,WAAoB,SAAS;QAA7B,aAAQ,GAAR,QAAQ,CAAqB;QAC7E,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3B,kEAAkE;IACpE,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,aAAa;YAC9B,OAAO,QAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,gBAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,4DAA4D;QAC5D,IAAI,UAAU,KAAK,MAAM;YACvB,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;QAElC,6DAA6D;QAC7D,aAAa;QACb,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,KAAK,MAAM;YACxC,OAAO,IAAI,CAAC;QAEd,8DAA8D;QAC9D,WAAW;QACX,IAAI,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,SAAS,CAAa,CAAC,CAAC;QAExE,iDAAiD;QACjD,IAAI,OAAO,KAAK,IAAI,EAAE;YAEpB,mDAAmD;YACnD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;YAE9C,iCAAiC;YACjC,MAAM,KAAK,GAAG,OAAO,GAAG,GAAG,CAAC;YAE5B,uEAAuE;YACvE,MAAM,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YAE1C,gEAAgE;YAChE,OAAO,CAAC,IAAI,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;SAC/B;QAED,uDAAuD;QACvD,IAAI,IAAI,CAAC,QAAQ;YACf,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;QAElC,8DAA8D;QAC9D,SAAS;QACT,OAAO,GAAG,4BAA4B,CAAC,UAAU,CAAC,CAAC;QAEnD,uDAAuD;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;QAElD,sDAAsD;QACtD,OAAO,GAAG,OAAO,GAAG,KAAK,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;QAE1C,mDAAmD;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;QAE7C,iDAAiD;QACjD,OAAO,GAAG,OAAO,GAAG,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC;QAErC,6CAA6C;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;QAEvC,yCAAyC;QACzC,MAAM,KAAK,GAAG,OAAO,GAAG,KAAK,GAAG,EAAE,CAAC;QAEnC,+DAA+D;QAC/D,oCAAoC;QACpC,OAAO,CAAC,KAAK,GAAG,IAAI;YACpB,KAAK,GAAG,IAAI;YACZ,KAAK,GAAG,IAAI;YACZ,KAAK,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;CACF"}