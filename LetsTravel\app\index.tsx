import React, { useEffect, useState } from "react";
import { router } from "expo-router";
import { Platform, View, Text, ActivityIndicator } from 'react-native';
import { Colors } from '../constants/DesignTokens';

// 🎨 Trekmate 4.0 - 简化的应用启动页面
export default function Index() {
  const [initializationStep, setInitializationStep] = useState('Starting...');

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      console.log('🚀 Trekmate 4.0 - 应用启动');

      // 简化的初始化流程
      setInitializationStep('Loading application...');

      // 最小加载时间，确保用户看到启动画面
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 直接跳转到 tabs，简化流程
      console.log('🎯 导航到主应用');
      router.replace('/tabs');

    } catch (error) {
      console.error('App initialization error:', error);
      setInitializationStep('Error occurred, redirecting...');

      // 出错时跳转到 tabs
      setTimeout(() => {
        router.replace('/tabs');
      }, 1000);
    }
  };

  // 🎨 Trekmate 4.0 - 简化的启动加载界面
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: Colors.primary,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {/* Logo Area */}
      <View style={{ alignItems: 'center', marginBottom: 40 }}>
        <Text
          style={{
            fontSize: 48,
            fontWeight: '700',
            color: 'white',
            textAlign: 'center',
            marginBottom: 16,
          }}
        >
          Trekmate
        </Text>
        <Text
          style={{
            fontSize: 16,
            fontWeight: '300',
            color: 'white',
            opacity: 0.9,
            textAlign: 'center',
          }}
        >
          Your AI Travel Companion
        </Text>
      </View>

      {/* Loading Indicator */}
      <View style={{ alignItems: 'center', marginBottom: 40 }}>
        <ActivityIndicator size="large" color="white" />
        <Text
          style={{
            fontSize: 14,
            color: 'white',
            opacity: 0.8,
            textAlign: 'center',
            marginTop: 16,
          }}
        >
          {initializationStep}
        </Text>
      </View>

      {/* Version Info */}
      <Text
        style={{
          fontSize: 12,
          color: 'white',
          opacity: 0.6,
          textAlign: 'center',
        }}
      >
        Version 4.0
      </Text>
    </View>
  );
}