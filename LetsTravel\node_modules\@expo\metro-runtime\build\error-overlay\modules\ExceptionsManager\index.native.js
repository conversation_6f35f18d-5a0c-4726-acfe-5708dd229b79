"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// @ts-expect-error
const ExceptionsManager_1 = __importDefault(require("react-native/Library/Core/ExceptionsManager"));
exports.default = ExceptionsManager_1.default;
//# sourceMappingURL=index.native.js.map