import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  StatusBar,
  Alert,
  Platform,
} from 'react-native';

// Import Button using require
const { Button } = require('react-native');

interface ARCoreTextTranslationProps {
  isVisible: boolean;
}

const ARCoreTextTranslation = ({ isVisible }: ARCoreTextTranslationProps) => {
  const [isSupported, setIsSupported] = useState(null);
  const [arSessionReady, setARSessionReady] = useState(false);
  const [detectedText, setDetectedText] = useState('');
  const [translatedText, setTranslatedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isFocused, setIsFocused] = useState(true);
  const [isARAvailable, setIsARAvailable] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 在组件加载时检查AR可用性
  useEffect(() => {
    const checkARSupport = async () => {
      if (Platform.OS !== 'android') {
        setIsSupported(false);
        setIsARAvailable(false);
        setIsLoading(false);
        console.log('ARCore is only supported on Android.');
        return;
      }
      setIsLoading(true);
      try {
        console.log('检查AR可用性...');
        // 简化的AR检查
        setIsSupported(true);
        setIsARAvailable(true);
        setIsLoading(false);
      } catch (error) {
        console.error('AR检查失败:', error);
        setIsSupported(false);
        setIsARAvailable(false);
        setIsLoading(false);
      }
    };

    checkARSupport();
  }, []);

  const handleLaunchAR = async () => {
    Alert.alert('AR 功能已禁用', '启动 AR 的功能当前已被移除。');
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <View style={styles.contentContainer}>
        <View style={styles.headerContainer}>
          <Text style={styles.titleText}>LetsTravel AR</Text>
        </View>

        {isLoading ? (
          <ActivityIndicator size="large" color="#FFF" />
        ) : (
          <View style={styles.buttonContainer}>
            <Button
              title={isSupported === false ? "设备不支持AR" : "启动 AR 文字识别"}
              onPress={handleLaunchAR}
              disabled={isLoading || !isARAvailable || isProcessing}
            />
            {isSupported === false && <Text style={styles.infoText}>您的设备似乎不支持 ARCore。</Text>}
          </View>
        )}

        {detectedText ? (
          <View style={styles.textContainer}>
            <Text style={styles.sectionTitle}>检测到的文字:</Text>
            <Text style={styles.detectedText}>{detectedText}</Text>
            {translatedText && (
              <>
                <Text style={styles.sectionTitle}>翻译结果:</Text>
                <Text style={styles.translatedText}>{translatedText}</Text>
              </>
            )}
          </View>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  headerContainer: {
    marginBottom: 40,
  },
  titleText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFF',
    textAlign: 'center',
  },
  buttonContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  infoText: {
    fontSize: 16,
    color: '#FFF',
    textAlign: 'center',
    marginTop: 15,
    opacity: 0.8,
  },
  textContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 20,
    borderRadius: 10,
    width: '100%',
    maxWidth: 350,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFF',
    marginBottom: 10,
  },
  detectedText: {
    fontSize: 18,
    color: '#FFF',
    marginBottom: 15,
    fontStyle: 'italic',
  },
  translatedText: {
    fontSize: 18,
    color: '#AEEEEE',
    fontWeight: 'bold',
  },
});

export default ARCoreTextTranslation;
