{"version": 3, "names": ["React", "useDocumentTitle", "ref", "enabled", "formatter", "options", "route", "title", "name", "useEffect", "navigation", "current", "getCurrentOptions", "getCurrentRoute", "document", "addListener", "e", "data"], "sourceRoot": "../../src", "sources": ["useDocumentTitle.tsx"], "mappings": "AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAI9B;AACA;AACA;AACA,eAAe,SAASC,gBAAgB,CACtCC,GAA2D,EAK3D;EAAA,IAJA;IACEC,OAAO,GAAG,IAAI;IACdC,SAAS,GAAG,CAACC,OAAO,EAAEC,KAAK,KAAK,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,KAAK,MAAID,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,IAAI;EACzC,CAAC,uEAAG,CAAC,CAAC;EAE5BR,KAAK,CAACS,SAAS,CAAC,MAAM;IACpB,IAAI,CAACN,OAAO,EAAE;MACZ;IACF;IAEA,MAAMO,UAAU,GAAGR,GAAG,CAACS,OAAO;IAE9B,IAAID,UAAU,EAAE;MACd,MAAMH,KAAK,GAAGH,SAAS,CACrBM,UAAU,CAACE,iBAAiB,EAAE,EAC9BF,UAAU,CAACG,eAAe,EAAE,CAC7B;MAEDC,QAAQ,CAACP,KAAK,GAAGA,KAAK;IACxB;IAEA,OAAOG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,WAAW,CAAC,SAAS,EAAGC,CAAC,IAAK;MAC/C,MAAMT,KAAK,GAAGH,SAAS,CAACY,CAAC,CAACC,IAAI,CAACZ,OAAO,EAAEK,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEG,eAAe,EAAE,CAAC;MAEtEC,QAAQ,CAACP,KAAK,GAAGA,KAAK;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}