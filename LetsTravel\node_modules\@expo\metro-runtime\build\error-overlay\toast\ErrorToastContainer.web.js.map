{"version": 3, "file": "ErrorToastContainer.web.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/toast/ErrorToastContainer.web.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,+CAAoD;AACpD,+CAAgD;AAEhD,6CAA0C;AAC1C,+DAAiD;AAEjD,mDAA6C;AAC7C,gEAA6D;AAE7D,SAAgB,mBAAmB;IACjC,IAAA,yCAAmB,GAAE,CAAC;IACtB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,IAAA,oBAAO,GAAE,CAAC;IACvC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;QAC9B,OAAO,IAAI,CAAC;KACb;IACD,OAAO,8BAAC,eAAe,IAAC,IAAI,EAAE,IAAI,GAAI,CAAC;AACzC,CAAC;AAPD,kDAOC;AAED,SAAS,eAAe,CAAC,EAAE,IAAI,EAAyB;IACtD,MAAM,cAAc,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QACtC,UAAU,CAAC,aAAa,EAAE,CAAC;IAC7B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,eAAe,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QACvC,UAAU,CAAC,WAAW,EAAE,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,cAAc,GAAG,IAAA,mBAAW,EAAC,CAAC,KAAa,EAAQ,EAAE;QACzD,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,SAAS,OAAO,CAAC,GAAc;QAC7B,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAE5B,2EAA2E;QAC3E,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;YACvC,KAAK,IAAI,CAAC,CAAC;SACZ;QACD,cAAc,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED,MAAM,QAAQ,GAAG,IAAA,eAAO,EAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAEnF,MAAM,MAAM,GAAG,IAAA,eAAO,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK,OAAO,IAAI,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,EAC1E,CAAC,IAAI,CAAC,CACP,CAAC;IAEF,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI;QACrB,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CACtB,8BAAC,uBAAU,IACT,GAAG,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAClC,KAAK,EAAC,MAAM,EACZ,aAAa,EAAE,QAAQ,CAAC,MAAM,EAC9B,WAAW,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EACzD,cAAc,EAAE,cAAc,GAC9B,CACH;QAEA,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CACpB,8BAAC,uBAAU,IACT,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAC9B,KAAK,EAAC,OAAO,EACb,aAAa,EAAE,MAAM,CAAC,MAAM,EAC5B,WAAW,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EACrD,cAAc,EAAE,eAAe,GAC/B,CACH,CACI,CACR,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,EAAE;QACJ,MAAM,EAAE,CAAC;QACT,IAAI,EAAE,EAAE;QACR,KAAK,EAAE,EAAE;QACT,QAAQ,EAAE,GAAG;QACb,mBAAmB;QACnB,QAAQ,EAAE,OAAO;KAClB;CACF,CAAC,CAAC;AAEH,kBAAe,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React, { useCallback, useMemo } from 'react';\nimport { StyleSheet, View } from 'react-native';\n\nimport { ErrorToast } from './ErrorToast';\nimport * as LogBoxData from '../Data/LogBoxData';\nimport { LogBoxLog } from '../Data/LogBoxLog';\nimport { useLogs } from '../Data/LogContext';\nimport { useRejectionHandler } from '../useRejectionHandler';\n\nexport function ErrorToastContainer() {\n  useRejectionHandler();\n  const { logs, isDisabled } = useLogs();\n  if (!logs.length || isDisabled) {\n    return null;\n  }\n  return <ErrorToastStack logs={logs} />;\n}\n\nfunction ErrorToastStack({ logs }: { logs: LogBoxLog[] }) {\n  const onDismissWarns = useCallback(() => {\n    LogBoxData.clearWarnings();\n  }, []);\n\n  const onDismissErrors = useCallback(() => {\n    LogBoxData.clearErrors();\n  }, []);\n\n  const setSelectedLog = useCallback((index: number): void => {\n    LogBoxData.setSelectedLog(index);\n  }, []);\n\n  function openLog(log: LogBoxLog) {\n    let index = logs.length - 1;\n\n    // Stop at zero because if we don't find any log, we'll open the first log.\n    while (index > 0 && logs[index] !== log) {\n      index -= 1;\n    }\n    setSelectedLog(index);\n  }\n\n  const warnings = useMemo(() => logs.filter((log) => log.level === 'warn'), [logs]);\n\n  const errors = useMemo(\n    () => logs.filter((log) => log.level === 'error' || log.level === 'fatal'),\n    [logs]\n  );\n\n  return (\n    <View style={styles.list}>\n      {warnings.length > 0 && (\n        <ErrorToast\n          log={warnings[warnings.length - 1]}\n          level=\"warn\"\n          totalLogCount={warnings.length}\n          onPressOpen={() => openLog(warnings[warnings.length - 1])}\n          onPressDismiss={onDismissWarns}\n        />\n      )}\n\n      {errors.length > 0 && (\n        <ErrorToast\n          log={errors[errors.length - 1]}\n          level=\"error\"\n          totalLogCount={errors.length}\n          onPressOpen={() => openLog(errors[errors.length - 1])}\n          onPressDismiss={onDismissErrors}\n        />\n      )}\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  list: {\n    bottom: 6,\n    left: 10,\n    right: 10,\n    maxWidth: 320,\n    // @ts-expect-error\n    position: 'fixed',\n  },\n});\n\nexport default LogBoxData.withSubscription(ErrorToastContainer);\n"]}