{"version": 3, "file": "XcodeProjectFile.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_Xcodeproj", "_iosPlugins", "obj", "__esModule", "default", "withBuildSourceFile", "config", "filePath", "contents", "overwrite", "withXcodeProject", "projectName", "getProjectName", "modRequest", "projectRoot", "modResults", "createBuildSourceFile", "project", "nativeProjectRoot", "platformProjectRoot", "fileContents", "path", "join", "exports", "absoluteFilePath", "fs", "existsSync", "writeFileSync", "groupName", "dirname", "hasFile", "addBuildSourceFileToGroup", "filepath"], "sources": ["../../src/ios/XcodeProjectFile.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport { addBuildSourceFileToGroup, getProjectName } from './utils/Xcodeproj';\nimport { ConfigPlugin, XcodeProject } from '../Plugin.types';\nimport { withXcodeProject } from '../plugins/ios-plugins';\n\n/**\n * Create a build source file and link it to Xcode.\n *\n * @param config\n * @param props.filePath relative to the build source folder. ex: `ViewController.swift` would be created in `ios/myapp/ViewController.swift`.\n * @param props.contents file contents to write.\n * @param props.overwrite should the contents overwrite any existing file in the same location on disk.\n * @returns\n */\nexport const withBuildSourceFile: ConfigPlugin<{\n  filePath: string;\n  contents: string;\n  overwrite?: boolean;\n}> = (config, { filePath, contents, overwrite }) => {\n  return withXcodeProject(config, (config) => {\n    const projectName = getProjectName(config.modRequest.projectRoot);\n\n    config.modResults = createBuildSourceFile({\n      project: config.modResults,\n      nativeProjectRoot: config.modRequest.platformProjectRoot,\n      fileContents: contents,\n      filePath: path.join(projectName, filePath),\n      overwrite,\n    });\n    return config;\n  });\n};\n\n/**\n * Add a source file to the Xcode project and write it to the file system.\n *\n * @param nativeProjectRoot absolute path to the native app root `user/app/ios`\n * @param filePath path relative to the `nativeProjectRoot` for the file to create `user/app/ios/myapp/foobar.swift`\n * @param fileContents string file contents to write to the `filePath`\n * @param overwrite should write file even if one already exists\n */\nexport function createBuildSourceFile({\n  project,\n  nativeProjectRoot,\n  filePath,\n  fileContents,\n  overwrite,\n}: {\n  project: XcodeProject;\n  nativeProjectRoot: string;\n  filePath: string;\n  fileContents: string;\n  overwrite?: boolean;\n}): XcodeProject {\n  const absoluteFilePath = path.join(nativeProjectRoot, filePath);\n  if (overwrite || !fs.existsSync(absoluteFilePath)) {\n    // Create the file\n    fs.writeFileSync(absoluteFilePath, fileContents, 'utf8');\n  }\n\n  // `myapp`\n  const groupName = path.dirname(filePath);\n\n  // Ensure the file is linked with Xcode resource files\n  if (!project.hasFile(filePath)) {\n    project = addBuildSourceFileToGroup({\n      filepath: filePath,\n      groupName,\n      project,\n    });\n  }\n  return project;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,WAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,YAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,WAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0D,SAAAC,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMG,mBAIX,GAAGA,CAACC,MAAM,EAAE;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAClD,OAAO,IAAAC,8BAAgB,EAACJ,MAAM,EAAGA,MAAM,IAAK;IAC1C,MAAMK,WAAW,GAAG,IAAAC,2BAAc,EAACN,MAAM,CAACO,UAAU,CAACC,WAAW,CAAC;IAEjER,MAAM,CAACS,UAAU,GAAGC,qBAAqB,CAAC;MACxCC,OAAO,EAAEX,MAAM,CAACS,UAAU;MAC1BG,iBAAiB,EAAEZ,MAAM,CAACO,UAAU,CAACM,mBAAmB;MACxDC,YAAY,EAAEZ,QAAQ;MACtBD,QAAQ,EAAEc,eAAI,CAACC,IAAI,CAACX,WAAW,EAAEJ,QAAQ,CAAC;MAC1CE;IACF,CAAC,CAAC;IACF,OAAOH,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPAiB,OAAA,CAAAlB,mBAAA,GAAAA,mBAAA;AAQO,SAASW,qBAAqBA,CAAC;EACpCC,OAAO;EACPC,iBAAiB;EACjBX,QAAQ;EACRa,YAAY;EACZX;AAOF,CAAC,EAAgB;EACf,MAAMe,gBAAgB,GAAGH,eAAI,CAACC,IAAI,CAACJ,iBAAiB,EAAEX,QAAQ,CAAC;EAC/D,IAAIE,SAAS,IAAI,CAACgB,aAAE,CAACC,UAAU,CAACF,gBAAgB,CAAC,EAAE;IACjD;IACAC,aAAE,CAACE,aAAa,CAACH,gBAAgB,EAAEJ,YAAY,EAAE,MAAM,CAAC;EAC1D;;EAEA;EACA,MAAMQ,SAAS,GAAGP,eAAI,CAACQ,OAAO,CAACtB,QAAQ,CAAC;;EAExC;EACA,IAAI,CAACU,OAAO,CAACa,OAAO,CAACvB,QAAQ,CAAC,EAAE;IAC9BU,OAAO,GAAG,IAAAc,sCAAyB,EAAC;MAClCC,QAAQ,EAAEzB,QAAQ;MAClBqB,SAAS;MACTX;IACF,CAAC,CAAC;EACJ;EACA,OAAOA,OAAO;AAChB", "ignoreList": []}