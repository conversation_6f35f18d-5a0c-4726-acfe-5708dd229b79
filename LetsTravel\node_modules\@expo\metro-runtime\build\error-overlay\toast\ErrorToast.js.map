{"version": 3, "file": "ErrorToast.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/toast/ErrorToast.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,+CAAyC;AACzC,+CAAkF;AAElF,2DAAwD;AACxD,+DAAiD;AAEjD,+DAAiD;AAUjD,SAAS,kBAAkB,CAAC,GAAc;IACxC,0EAA0E;IAC1E,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,UAAU,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC5C,UAAU,CAAC,kBAAkB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IAClD,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACZ,CAAC;AAED,SAAgB,UAAU,CAAC,KAAY;IACrC,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC;IAE5C,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAExB,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,WAAW,CAAC,SAAS;QAChC,8BAAC,wBAAS,IAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,WAAW,IACtD,CAAC;QACA,uDAAuD;QACvD,OAAO,EACP,OAAO,GACR,EAAE,EAAE,CAAC,CACJ,8BAAC,mBAAI,IACH,KAAK,EAAE;gBACL,WAAW,CAAC,KAAK;gBACjB;oBACE,kCAAkC;oBAClC,kBAAkB,EAAE,OAAO;oBAC3B,eAAe,EAAE,OAAO;wBACtB,CAAC,CAAC,SAAS;wBACX,CAAC,CAAC,OAAO;4BACP,CAAC,CAAC,SAAS;4BACX,CAAC,CAAC,WAAW,CAAC,kBAAkB,EAAE;iBACvC;aACF;YACD,8BAAC,KAAK,IAAC,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,GAAI;YAC7C,8BAAC,qCAAiB,IAAC,OAAO,EAAE,GAAG,CAAC,OAAO,GAAI;YAC3C,8BAAC,OAAO,IAAC,OAAO,EAAE,KAAK,CAAC,cAAc,GAAI,CACrC,CACR,CACS,CACP,CACR,CAAC;AACJ,CAAC;AAlCD,gCAkCC;AAED,SAAS,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAA4C;IACvE,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;QACnD,8BAAC,mBAAI,IAAC,KAAK,EAAE,WAAW,CAAC,IAAI,IAAG,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAQ,CAC3D,CACR,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAC,EAAE,OAAO,EAA2B;IACnD,OAAO,CACL,8BAAC,wBAAS,IACR,KAAK,EAAE;YACL,UAAU,EAAE,CAAC;SACd,EACD,OAAO,EAAE;YACP,GAAG,EAAE,EAAE;YACP,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,EAAE;SACT,EACD,OAAO,EAAE,OAAO,IACf,CAAC;IACA,uDAAuD;IACvD,OAAO,EACP,OAAO,GACR,EAAE,EAAE,CAAC,CACJ,8BAAC,mBAAI,IACH,KAAK,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;QACtF,8BAAC,oBAAK,IACJ,MAAM,EAAE,OAAO,CAAC,sCAAsC,CAAC,EACvD,KAAK,EAAE,aAAa,CAAC,KAAK,GAC1B,CACG,CACR,CACS,CACb,CAAC;AACJ,CAAC;AAED,MAAM,WAAW,GAAG,yBAAU,CAAC,MAAM,CAAC;IACpC,IAAI,EAAE;QACJ,eAAe,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC;KAChD;IACD,KAAK,EAAE;QACL,eAAe,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KAC9C;IACD,GAAG,EAAE;QACH,eAAe,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC;KAC5C;IACD,MAAM,EAAE;QACN,WAAW,EAAE,CAAC;QACd,QAAQ,EAAE,EAAE;QACZ,WAAW,EAAE,CAAC;QACd,iBAAiB,EAAE,CAAC;QACpB,YAAY,EAAE,EAAE;QAChB,cAAc,EAAE,QAAQ;QACxB,UAAU,EAAE,QAAQ;KACrB;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;QAClC,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,EAAE;QACd,SAAS,EAAE,QAAQ;QACnB,UAAU,EAAE,KAAK;QACjB,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,UAAU,EAAE,eAAe,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE;aACjE;SACF,CAAC;KACH;CACF,CAAC,CAAC;AAEH,MAAM,aAAa,GAAG,yBAAU,CAAC,MAAM,CAAC;IACtC,KAAK,EAAE;QACL,eAAe,EAAE,SAAS;QAC1B,MAAM,EAAE,EAAE;QACV,KAAK,EAAE,EAAE;QACT,YAAY,EAAE,EAAE;QAChB,UAAU,EAAE,QAAQ;QACpB,cAAc,EAAE,QAAQ;KACzB;IACD,KAAK,EAAE;QACL,MAAM,EAAE,CAAC;QACT,KAAK,EAAE,CAAC;KACT;CACF,CAAC,CAAC;AAEH,MAAM,WAAW,GAAG,yBAAU,CAAC,MAAM,CAAC;IACpC,SAAS,EAAE;QACT,MAAM,EAAE,EAAE;QACV,cAAc,EAAE,QAAQ;QACxB,YAAY,EAAE,CAAC;KAChB;IACD,KAAK,EAAE;QACL,WAAW,EAAE,CAAC;QACd,YAAY,EAAE,CAAC;QACf,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,QAAQ;QACpB,WAAW,EAAE,SAAS;QACtB,eAAe,EAAE,WAAW,CAAC,kBAAkB,EAAE;QACjD,IAAI,EAAE,CAAC;QACP,iBAAiB,EAAE,EAAE;KACtB;CACF,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React, { useEffect } from 'react';\nimport { Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native';\n\nimport { ErrorToastMessage } from './ErrorToastMessage';\nimport * as LogBoxData from '../Data/LogBoxData';\nimport { LogBoxLog } from '../Data/LogBoxLog';\nimport * as LogBoxStyle from '../UI/LogBoxStyle';\n\ntype Props = {\n  log: LogBoxLog;\n  totalLogCount: number;\n  level: 'warn' | 'error';\n  onPressOpen: () => void;\n  onPressDismiss: () => void;\n};\n\nfunction useSymbolicatedLog(log: LogBoxLog) {\n  // Eagerly symbolicate so the stack is available when pressing to inspect.\n  useEffect(() => {\n    LogBoxData.symbolicateLogLazy('stack', log);\n    LogBoxData.symbolicateLogLazy('component', log);\n  }, [log]);\n}\n\nexport function ErrorToast(props: Props) {\n  const { totalLogCount, level, log } = props;\n\n  useSymbolicatedLog(log);\n\n  return (\n    <View style={toastStyles.container}>\n      <Pressable style={{ flex: 1 }} onPress={props.onPressOpen}>\n        {({\n          /** @ts-expect-error: react-native types are broken. */\n          hovered,\n          pressed,\n        }) => (\n          <View\n            style={[\n              toastStyles.press,\n              {\n                // @ts-expect-error: web-only type\n                transitionDuration: '150ms',\n                backgroundColor: pressed\n                  ? '#323232'\n                  : hovered\n                    ? '#111111'\n                    : LogBoxStyle.getBackgroundColor(),\n              },\n            ]}>\n            <Count count={totalLogCount} level={level} />\n            <ErrorToastMessage message={log.message} />\n            <Dismiss onPress={props.onPressDismiss} />\n          </View>\n        )}\n      </Pressable>\n    </View>\n  );\n}\n\nfunction Count({ count, level }: { count: number; level: Props['level'] }) {\n  return (\n    <View style={[countStyles.inside, countStyles[level]]}>\n      <Text style={countStyles.text}>{count <= 1 ? '!' : count}</Text>\n    </View>\n  );\n}\n\nfunction Dismiss({ onPress }: { onPress: () => void }) {\n  return (\n    <Pressable\n      style={{\n        marginLeft: 5,\n      }}\n      hitSlop={{\n        top: 12,\n        right: 10,\n        bottom: 12,\n        left: 10,\n      }}\n      onPress={onPress}>\n      {({\n        /** @ts-expect-error: react-native types are broken. */\n        hovered,\n        pressed,\n      }) => (\n        <View\n          style={[dismissStyles.press, hovered && { opacity: 0.8 }, pressed && { opacity: 0.5 }]}>\n          <Image\n            source={require('@expo/metro-runtime/assets/close.png')}\n            style={dismissStyles.image}\n          />\n        </View>\n      )}\n    </Pressable>\n  );\n}\n\nconst countStyles = StyleSheet.create({\n  warn: {\n    backgroundColor: LogBoxStyle.getWarningColor(1),\n  },\n  error: {\n    backgroundColor: LogBoxStyle.getErrorColor(1),\n  },\n  log: {\n    backgroundColor: LogBoxStyle.getLogColor(1),\n  },\n  inside: {\n    marginRight: 8,\n    minWidth: 22,\n    aspectRatio: 1,\n    paddingHorizontal: 4,\n    borderRadius: 11,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  text: {\n    color: LogBoxStyle.getTextColor(1),\n    fontSize: 14,\n    lineHeight: 18,\n    textAlign: 'center',\n    fontWeight: '600',\n    ...Platform.select({\n      web: {\n        textShadow: `0px 0px 3px ${LogBoxStyle.getBackgroundColor(0.8)}`,\n      },\n    }),\n  },\n});\n\nconst dismissStyles = StyleSheet.create({\n  press: {\n    backgroundColor: '#323232',\n    height: 20,\n    width: 20,\n    borderRadius: 25,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  image: {\n    height: 8,\n    width: 8,\n  },\n});\n\nconst toastStyles = StyleSheet.create({\n  container: {\n    height: 48,\n    justifyContent: 'center',\n    marginBottom: 4,\n  },\n  press: {\n    borderWidth: 1,\n    borderRadius: 8,\n    overflow: 'hidden',\n    flexDirection: 'row',\n    alignItems: 'center',\n    borderColor: '#323232',\n    backgroundColor: LogBoxStyle.getBackgroundColor(),\n    flex: 1,\n    paddingHorizontal: 12,\n  },\n});\n"]}