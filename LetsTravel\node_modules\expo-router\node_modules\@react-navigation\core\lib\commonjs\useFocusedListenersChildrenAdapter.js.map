{"version": 3, "names": ["useFocusedListenersChildrenAdapter", "navigation", "focusedListeners", "addListener", "React", "useContext", "NavigationBuilderContext", "listener", "useCallback", "callback", "isFocused", "handled", "result", "useEffect"], "sourceRoot": "../../src", "sources": ["useFocusedListenersChildrenAdapter.tsx"], "mappings": ";;;;;;AACA;AAEA;AAGoC;AAAA;AAAA;AAQpC;AACA;AACA;AACe,SAASA,kCAAkC,OAG9C;EAAA,IAH+C;IACzDC,UAAU;IACVC;EACO,CAAC;EACR,MAAM;IAAEC;EAAY,CAAC,GAAGC,KAAK,CAACC,UAAU,CAACC,iCAAwB,CAAC;EAElE,MAAMC,QAAQ,GAAGH,KAAK,CAACI,WAAW,CAC/BC,QAAwC,IAAK;IAC5C,IAAIR,UAAU,CAACS,SAAS,EAAE,EAAE;MAC1B,KAAK,MAAMH,QAAQ,IAAIL,gBAAgB,EAAE;QACvC,MAAM;UAAES,OAAO;UAAEC;QAAO,CAAC,GAAGL,QAAQ,CAACE,QAAQ,CAAC;QAE9C,IAAIE,OAAO,EAAE;UACX,OAAO;YAAEA,OAAO;YAAEC;UAAO,CAAC;QAC5B;MACF;MAEA,OAAO;QAAED,OAAO,EAAE,IAAI;QAAEC,MAAM,EAAEH,QAAQ,CAACR,UAAU;MAAE,CAAC;IACxD,CAAC,MAAM;MACL,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAK,CAAC;IACzC;EACF,CAAC,EACD,CAACV,gBAAgB,EAAED,UAAU,CAAC,CAC/B;EAEDG,KAAK,CAACS,SAAS,CACb,MAAMV,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAG,OAAO,EAAEI,QAAQ,CAAC,EACtC,CAACJ,WAAW,EAAEI,QAAQ,CAAC,CACxB;AACH"}