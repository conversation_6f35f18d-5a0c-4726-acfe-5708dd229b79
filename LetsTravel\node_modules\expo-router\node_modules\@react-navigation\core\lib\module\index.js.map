{"version": 3, "names": ["default", "BaseNavigationContainer", "createNavigationContainerRef", "createNavigatorFactory", "CurrentRenderContext", "findFocusedRoute", "getActionFromState", "getFocusedRouteNameFromRoute", "getPathFromState", "getStateFromPath", "NavigationContainerRefContext", "NavigationContext", "NavigationHelpersContext", "NavigationRouteContext", "PreventRemoveContext", "PreventRemoveProvider", "useFocusEffect", "useIsFocused", "useNavigation", "useNavigationBuilder", "useNavigationContainerRef", "useNavigationState", "UNSTABLE_usePreventRemove", "usePreventRemoveContext", "useRoute", "validatePathConfig"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": "AAAA,SAASA,OAAO,IAAIC,uBAAuB,QAAQ,2BAA2B;AAC9E,SAASD,OAAO,IAAIE,4BAA4B,QAAQ,gCAAgC;AACxF,SAASF,OAAO,IAAIG,sBAAsB,QAAQ,0BAA0B;AAC5E,SAASH,OAAO,IAAII,oBAAoB,QAAQ,wBAAwB;AACxE,SAASJ,OAAO,IAAIK,gBAAgB,QAAQ,oBAAoB;AAChE,SAASL,OAAO,IAAIM,kBAAkB,QAAQ,sBAAsB;AACpE,SAASN,OAAO,IAAIO,4BAA4B,QAAQ,gCAAgC;AACxF,SAASP,OAAO,IAAIQ,gBAAgB,QAAQ,oBAAoB;AAChE,SAASR,OAAO,IAAIS,gBAAgB,QAAQ,oBAAoB;AAChE,SAAST,OAAO,IAAIU,6BAA6B,QAAQ,iCAAiC;AAC1F,SAASV,OAAO,IAAIW,iBAAiB,QAAQ,qBAAqB;AAClE,SAASX,OAAO,IAAIY,wBAAwB,QAAQ,4BAA4B;AAChF,SAASZ,OAAO,IAAIa,sBAAsB,QAAQ,0BAA0B;AAC5E,SAASb,OAAO,IAAIc,oBAAoB,QAAQ,wBAAwB;AACxE,SAASd,OAAO,IAAIe,qBAAqB,QAAQ,yBAAyB;AAC1E,cAAc,SAAS;AACvB,SAASf,OAAO,IAAIgB,cAAc,QAAQ,kBAAkB;AAC5D,SAAShB,OAAO,IAAIiB,YAAY,QAAQ,gBAAgB;AACxD,SAASjB,OAAO,IAAIkB,aAAa,QAAQ,iBAAiB;AAC1D,SAASlB,OAAO,IAAImB,oBAAoB,QAAQ,wBAAwB;AACxE,SAASnB,OAAO,IAAIoB,yBAAyB,QAAQ,6BAA6B;AAClF,SAASpB,OAAO,IAAIqB,kBAAkB,QAAQ,sBAAsB;AACpE,SAASrB,OAAO,IAAIsB,yBAAyB,QAAQ,oBAAoB;AACzE,SAAStB,OAAO,IAAIuB,uBAAuB,QAAQ,2BAA2B;AAC9E,SAASvB,OAAO,IAAIwB,QAAQ,QAAQ,YAAY;AAChD,SAASxB,OAAO,IAAIyB,kBAAkB,QAAQ,sBAAsB;AACpE,cAAc,2BAA2B"}