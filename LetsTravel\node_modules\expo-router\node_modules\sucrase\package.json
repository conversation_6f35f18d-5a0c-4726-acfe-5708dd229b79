{"name": "sucrase", "version": "3.34.0", "description": "Super-fast alternative to Babel for when you can target modern JS runtimes", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "dist/index.js", "module": "dist/esm/index.js", "types": "dist/types/index.d.ts", "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "scripts": {"build": "sucrase-node script/build.ts", "fast-build": "sucrase-node script/build.ts --fast", "clean": "rm -rf ./build ./dist ./dist-self-build ./dist-types ./example-runner/example-repos ./spec-compliance-tests/test262/test262-checkout ./spec-compliance-tests/babel-tests/babel-tests-checkout", "generate": "sucrase-node generator/generate.ts", "benchmark": "cd benchmark && yarn && sucrase-node ./benchmark.ts", "benchmark-compare": "sucrase-node ./benchmark/compare-performance.ts", "microbenchmark": "sucrase-node benchmark/microbenchmark.ts", "lint": "sucrase-node script/lint.ts", "lint-fix": "sucrase-node script/lint.ts --fix", "profile": "node --inspect-brk ./node_modules/.bin/sucrase-node ./benchmark/profile", "profile-project": "node --inspect-brk ./node_modules/.bin/sucrase-node ./benchmark/benchmark-project.ts --profile", "prepublishOnly": "yarn clean && yarn build", "release": "sucrase-node script/release.ts", "run-examples": "sucrase-node example-runner/example-runner.ts", "test": "yarn lint && yarn test-only", "test-only": "mocha './test/**/*.ts'", "integration-test": "cd integration-test && yarn && yarn link @sucrase/jest-plugin && mocha --timeout 10000 ./integration-tests.ts", "test262": "sucrase-node spec-compliance-tests/test262/run-test262.ts", "check-babel-tests": "sucrase-node spec-compliance-tests/babel-tests/check-babel-tests.ts", "test-with-coverage": "nyc mocha './test/**/*.ts'", "report-coverage": "nyc report --reporter=text-lcov > coverage.lcov && codecov"}, "repository": {"type": "git", "url": "https://github.com/alangpierce/sucrase.git"}, "keywords": ["babel", "jsx", "typescript", "flow"], "bugs": {"url": "https://github.com/alangpierce/sucrase/issues"}, "devDependencies": {"@babel/core": "^7.22.5", "@jridgewell/trace-mapping": "^0.3.18", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^20.3.2", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.43.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "~2.26", "eslint-plugin-prettier": "^4.2.1", "mocha": "^10.2.0", "nyc": "^15.1.0", "prettier": "^2.8.8", "sucrase": "^3.33.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "~5.0"}, "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "engines": {"node": ">=8"}, "resolutions": {"**/eshost/socket.io": "4.7.0"}}