{"version": 3, "file": "PrimaryColor.js", "names": ["_Colors", "data", "require", "_Styles", "_androidPlugins", "COLOR_PRIMARY_KEY", "DEFAULT_PRIMARY_COLOR", "withPrimaryColor", "config", "withPrimaryColorColors", "withPrimaryColorStyles", "exports", "withAndroidColors", "modResults", "assignColorValue", "name", "value", "getPrimaryColor", "withAndroidStyles", "assignStylesValue", "add", "parent", "getAppThemeLightNoActionBarGroup", "primaryColor"], "sources": ["../../src/android/PrimaryColor.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { assignColorValue } from './Colors';\nimport { assignStylesValue, getAppThemeLightNoActionBarGroup } from './Styles';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAndroidColors, withAndroidStyles } from '../plugins/android-plugins';\n\nconst COLOR_PRIMARY_KEY = 'colorPrimary';\nconst DEFAULT_PRIMARY_COLOR = '#023c69';\n\nexport const withPrimaryColor: ConfigPlugin = (config) => {\n  config = withPrimaryColorColors(config);\n  config = withPrimaryColorStyles(config);\n  return config;\n};\n\nexport const withPrimaryColorColors: ConfigPlugin = (config) => {\n  return withAndroidColors(config, (config) => {\n    config.modResults = assignColorValue(config.modResults, {\n      name: COLOR_PRIMARY_KEY,\n      value: getPrimaryColor(config),\n    });\n    return config;\n  });\n};\n\nexport const withPrimaryColorStyles: ConfigPlugin = (config) => {\n  return withAndroidStyles(config, (config) => {\n    config.modResults = assignStylesValue(config.modResults, {\n      add: !!getPrimaryColor(config),\n      parent: getAppThemeLightNoActionBarGroup(),\n      name: COLOR_PRIMARY_KEY,\n      value: `@color/${COLOR_PRIMARY_KEY}`,\n    });\n    return config;\n  });\n};\n\nexport function getPrimaryColor(config: Pick<ExpoConfig, 'primaryColor'>) {\n  return config.primaryColor ?? DEFAULT_PRIMARY_COLOR;\n}\n"], "mappings": ";;;;;;;AAEA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,QAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,gBAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,eAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMI,iBAAiB,GAAG,cAAc;AACxC,MAAMC,qBAAqB,GAAG,SAAS;AAEhC,MAAMC,gBAA8B,GAAIC,MAAM,IAAK;EACxDA,MAAM,GAAGC,sBAAsB,CAACD,MAAM,CAAC;EACvCA,MAAM,GAAGE,sBAAsB,CAACF,MAAM,CAAC;EACvC,OAAOA,MAAM;AACf,CAAC;AAACG,OAAA,CAAAJ,gBAAA,GAAAA,gBAAA;AAEK,MAAME,sBAAoC,GAAID,MAAM,IAAK;EAC9D,OAAO,IAAAI,mCAAiB,EAACJ,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACK,UAAU,GAAG,IAAAC,0BAAgB,EAACN,MAAM,CAACK,UAAU,EAAE;MACtDE,IAAI,EAAEV,iBAAiB;MACvBW,KAAK,EAAEC,eAAe,CAACT,MAAM;IAC/B,CAAC,CAAC;IACF,OAAOA,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACG,OAAA,CAAAF,sBAAA,GAAAA,sBAAA;AAEK,MAAMC,sBAAoC,GAAIF,MAAM,IAAK;EAC9D,OAAO,IAAAU,mCAAiB,EAACV,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACK,UAAU,GAAG,IAAAM,2BAAiB,EAACX,MAAM,CAACK,UAAU,EAAE;MACvDO,GAAG,EAAE,CAAC,CAACH,eAAe,CAACT,MAAM,CAAC;MAC9Ba,MAAM,EAAE,IAAAC,0CAAgC,EAAC,CAAC;MAC1CP,IAAI,EAAEV,iBAAiB;MACvBW,KAAK,EAAE,UAAUX,iBAAiB;IACpC,CAAC,CAAC;IACF,OAAOG,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACG,OAAA,CAAAD,sBAAA,GAAAA,sBAAA;AAEK,SAASO,eAAeA,CAACT,MAAwC,EAAE;EACxE,OAAOA,MAAM,CAACe,YAAY,IAAIjB,qBAAqB;AACrD", "ignoreList": []}