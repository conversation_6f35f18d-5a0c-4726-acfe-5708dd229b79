import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Platform,
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Animated,
  Image,
  KeyboardAvoidingView,
  Modal,
  TouchableWithoutFeedback,
  Alert,
  Dimensions,
  FlatList
} from 'react-native';

// 🎨 Trekmate 4.0 - Modern UI Components
import {
  Box,
  Text as ChakraText,
  VStack,
  HStack,
  Button,
} from '@chakra-ui/react';

// 临时添加 Tamagui 组件的模拟实现
const YStack = VStack;
const XStack = HStack;
const TamaguiText = ChakraText;
const TamaguiButton = Button;
const TamaguiScrollView = ScrollView;
const Card = Box;
import { useNavigation } from '@react-navigation/native';
import { useFocusEffect } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import * as ImagePickerTypes from 'expo-image-picker';
import { Audio } from 'expo-av';

// 🎨 Trekmate 4.0 - Design Tokens
import { Colors, Spacing, BorderRadius, Typography } from '../constants/DesignTokens';
import { useToast } from '../ui/Toast';

// 尝试导入图片选择器模块
let ImagePicker: any = null;
try {
  ImagePicker = require('expo-image-picker');
} catch (error) {
  console.warn('Warning: expo-image-picker could not be loaded', error);
}

// 尝试导入FileSystem模块
let FileSystem: any = null;
try {
  FileSystem = require('expo-file-system');
} catch (error) {
  console.warn('Warning: expo-file-system could not be loaded', error);
}

// 将 VAD 相关常量移到顶层
const SILENCE_THRESHOLD = -35; // 静音阈值 (dBFS), 可调整
const SILENCE_DURATION_MS = 1500; // 静音持续多久算结束 (毫秒)
const MAX_RECORDING_DURATION_MS = 30000; // 最大录音时长，防止无限录音

// 日志函数工厂 - 创建一个不依赖于组件状态的基础日志函数
const createLogFunction = (setLogsFunction: (updater: (prev: string[]) => string[]) => void) => {
  return (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    setLogsFunction(prev => [...prev, logEntry]);
  };
};

// 新增Gemini语音识别API端点
const API_CONFIG = {
  apiKey: 'AIzaSyCOZCtQqRQ1YiRyjeY_AiNdhWohfGeP804',  // 使用提供的API密钥
  // 将Gemini模型更新为gemini-2.0-flash-lite
  geminiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent',
  textToSpeechEndpoint: 'https://texttospeech.googleapis.com/v1/text:synthesize',
  speechToTextEndpoint: 'https://speech.googleapis.com/v1/speech:recognize',
  // 将音频处理的Gemini模型也更新为gemini-2.0-flash-lite
  geminiAudioEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent'
};

// 定义消息类型
interface Message {
  id: number;
  text: string;
  sender: 'user' | 'gemini';
  timestamp: number;
  imageUri?: string;
}

// 模拟响应
const mockResponse = (query: string, imageAttached?: boolean) => {
  // 图片响应
  if (imageAttached) {
    return "I can see the image you've shared. Based on what I observe:\n\n**Analysis of your image:**\n- This appears to be a travel-related scene\n- The location seems to be in an interesting area\n- The lighting and composition are good\n\nWould you like recommendations related to this location or information about similar destinations? I can help with identifying landmarks, suggesting nearby attractions, or finding similar places to visit.";
  }

  // 常规响应
  const responses: Record<string, string> = {
    'Recommend travel destinations': 'Here are some travel destinations worth considering:\n\n1. **Bali - Indonesia**\nBeautiful beaches, unique culture, and rich natural landscapes make it ideal for relaxation and exploration.\n\n2. **Kyoto - Japan**\nHome to over 1,600 temples and shrines, it\'s a treasure of Japanese traditional culture.\n\n3. **Barcelona - Spain**\nGaudí\'s architectural masterpieces, Mediterranean cuisine, and vibrant city atmosphere.\n\n4. **Maldives**\nOverwater villas and stunning marine life, perfect for luxury and tranquility.\n\n5. **Santorini - Greece**\nIconic white and blue buildings, plus incredible Aegean Sea sunsets.\n\nDo you have a specific region or type of travel in mind? I can provide more targeted recommendations.',
    
    'Travel tips': 'Here are some essential travel tips for a smooth journey:\n\n**Before You Travel**\n- Check passport validity (6+ months remaining)\n- Research visa requirements and local customs\n- Get travel insurance and vaccinations if needed\n- Notify banks of travel plans\n\n**Packing Smart**\n- Pack light with versatile clothing\n- Bring copies of important documents\n- Keep essentials in carry-on bag\n- Check airline baggage restrictions\n\n**During Your Trip**\n- Stay hydrated and eat safely\n- Keep emergency contacts handy\n- Respect local customs and dress codes\n- Use reliable transportation options\n\nWould you like specific tips for any particular destination or type of travel?',
    
    'Local cuisine': 'Exploring local cuisine is one of the best ways to understand a culture. Here are specialty foods from several travel destinations:\n\n**Southeast Asia**\n- Thailand: Tom Yum Soup, Pad Thai, and Mango Sticky Rice\n- Vietnam: Pho, Spring Rolls, and Banh Mi sandwiches\n- Malaysia: Satay, Nyonya cuisine, and Bak Kut Teh\n\n**Europe**\n- Italy: Unique pasta from each region, pizza, and tiramisu\n- Spain: Paella, tapas, and Iberian ham\n- France: Croissants, steak, cheese, and elegant desserts\n\n**Asia**\n- Japan: Sushi, ramen, and tempura\n- China: Specialty cuisines from various provinces such as Sichuan, Cantonese, Shandong, etc.\n- India: Various curries, naan bread, and spice-rich dishes\n\nTrying street food is often an excellent way to experience the most authentic flavors. Which region\'s cuisine are you particularly interested in?'
  };
  
  // 关键词匹配
  for (const key in responses) {
    const lowerQuery = (query || '').toLowerCase();
    const lowerKey = (key || '').toLowerCase();
    if (lowerQuery.includes(lowerKey) || 
        (lowerKey.includes('destination') && lowerQuery.includes('recommend')) ||
        (lowerKey.includes('travel') && lowerQuery.includes('tips')) ||
        (lowerKey.includes('cuisine') && lowerQuery.includes('food'))) {
      return responses[key];
    }
  }
  
  // 通用响应
  return `About "${query}", I can provide the following information:\n\nAs your travel assistant, I can help you with:\n\n• Recommending popular or off-the-beaten-path destinations worldwide\n• Planning itineraries and optimizing your travel time\n• Introducing local cuisine and cultural specialties\n• Providing accommodation and transportation advice\n• Sharing budget travel tips\n\nPlease tell me which specific aspect you're interested in, and I'll provide more relevant information.`;
};

// 获取当前日期
const getCurrentDate = () => {
  const now = new Date();
  return `${now.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}`;
};

// 打字动画组件
const TypingAnimation = () => {
  const [dot1] = useState(new Animated.Value(0));
  const [dot2] = useState(new Animated.Value(0));
  const [dot3] = useState(new Animated.Value(0));

  useEffect(() => {
    const animateDot = (dot: Animated.Value, delay: number) => {
      Animated.sequence([
        Animated.timing(dot, {
          toValue: 1,
          duration: 400,
          delay,
          useNativeDriver: true
        }),
        Animated.timing(dot, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true
        })
      ]).start();
    };

    const startAnimation = () => {
      animateDot(dot1, 0);
      animateDot(dot2, 200);
      animateDot(dot3, 400);
      
      setTimeout(startAnimation, 1200);
    };

    startAnimation();
    
    return () => {
      dot1.stopAnimation();
      dot2.stopAnimation();
      dot3.stopAnimation();
    };
  }, [dot1, dot2, dot3]);

  return (
    <View style={styles.typingIndicator}>
      <Animated.View 
        style={[
          styles.typingDot,
          {
            transform: [
              {
                translateY: dot1.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -4]
                })
              }
            ]
          }
        ]}
      />
      <Animated.View 
        style={[
          styles.typingDot,
          {
            transform: [
              {
                translateY: dot2.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -4]
                })
              }
            ]
          }
        ]}
      />
      <Animated.View 
        style={[
          styles.typingDot,
          {
            transform: [
              {
                translateY: dot3.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -4]
                })
              }
            ]
          }
        ]}
      />
    </View>
  );
};

// GeminiChat组件集成到IGuidedScreen
export default function IGuidedScreen() {
  const navigation = useNavigation();
  
  // 移动到组件内的状态
  const [debugLogs, setDebugLogs] = useState([]);
  const [showDebugLogs, setShowDebugLogs] = useState(false);
  
  // 聊天相关状态
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [suggestions] = useState([
    'Recommend travel destinations', 
    'Local cuisine',
    'Travel tips'
  ]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(null);
  const scrollViewRef = useRef(null);
  const date = getCurrentDate();
  
  // 图片附件状态
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false);
  const [imageUri, setImageUri] = useState(null);
  const [showImageAttachmentModal, setShowImageAttachmentModal] = useState(false);
  const [imagePickerAvailable, setImagePickerAvailable] = useState(true);

  // 语音聊天状态 (UI展示用，不实际录音)
  const [isRecording, setIsRecording] = useState(false);
  // 添加实时语音聊天状态
  const [showVoiceChatModal, setShowVoiceChatModal] = useState(false);
  const [isVoicePaused, setIsVoicePaused] = useState(false);
  const [voiceChatMessages, setVoiceChatMessages] = useState([]);
  const [isListening, setIsListening] = useState(false);
  
  // 添加以下代码，用于语音波形动画
  const [waveHeights, setWaveHeights] = useState([10, 15, 20, 15, 10]);
  
  // 添加额外状态
  const [userLocation, setUserLocation] = useState(null);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [capturedImage, setCapturedImage] = useState(null);
  
  // 移动到顶部的Gemini STT状态
  const [useGeminiSTT, setUseGeminiSTT] = useState(true);
  
  // 创建实际的日志函数
  const addLog = createLogFunction((updater) => {
    setDebugLogs(updater);
    // 如果日志太多，只保留最近的100条
    setDebugLogs(prev => prev.length > 100 ? prev.slice(prev.length - 100) : prev);
  });
  
  // 使用useEffect添加波形动画
  useEffect(() => {
    let animationFrameId: number;
    
    const animateWaves = () => {
      if (isListening && !isVoicePaused) {
        setWaveHeights([
          5 + Math.random() * 25,
          5 + Math.random() * 25,
          5 + Math.random() * 25,
          5 + Math.random() * 25,
          5 + Math.random() * 25
        ]);
      }
      animationFrameId = requestAnimationFrame(animateWaves);
    };
    
    animationFrameId = requestAnimationFrame(animateWaves);
    
    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [isListening, isVoicePaused]);
  
  // 设置标题栏样式
  useFocusEffect(
    React.useCallback(() => {
      // 修改导航栏
      navigation.setOptions({
        headerShown: false, // 隐藏默认头部
      });
      
      // 状态栏样式
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('#ffffff');
      }
      StatusBar.setBarStyle('dark-content');
      
      // 清理函数
      return () => {
        if (Platform.OS === 'android') {
          StatusBar.setBackgroundColor('#ffffff');
        }
        StatusBar.setBarStyle('dark-content');
      };
    }, [navigation])
  );

  // 检查权限和模块可用性
  useEffect(() => {
    (async () => {
      if (!ImagePicker) {
        setImagePickerAvailable(false);
        return;
      }
      
      if (Platform.OS !== 'web') {
        try {
          const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
          const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
          if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
            Alert.alert(
              'Permissions Required',
              'Please grant camera and photo library permissions to use this feature.',
              [{ text: 'OK' }]
            );
          }
        } catch (error) {
          console.warn('Error requesting camera permissions:', error);
          setImagePickerAvailable(false);
        }
      }
    })();
  }, []);

  const handleTakePhoto = async () => {
    setShowImageAttachmentModal(false);
    
    if (!ImagePicker || !imagePickerAvailable) {
      Alert.alert(
        'Feature Unavailable',
        'Camera functionality is not available on this device.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaType.Images,
        allowsEditing: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.warn('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const handleChooseFromLibrary = async () => {
    setShowImageAttachmentModal(false);
    
    if (!ImagePicker || !imagePickerAvailable) {
      Alert.alert(
        'Feature Unavailable',
        'Photo library access is not available on this device.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaType.Images,
        allowsEditing: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.warn('Error choosing image from library:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const handleRemoveImage = () => {
    setImageUri(null);
  };

  const handleSend = async () => {
    if ((!input.trim() && !imageUri)) return;
    
    // 添加用户消息
    const userMessage: Message = { 
      id: Date.now(), 
      text: input || "Here's an image I'd like to ask about:", 
      sender: 'user',
      timestamp: Date.now(),
      imageUri: imageUri || undefined
    };
    
    setMessages([...messages, userMessage]);
    setInput('');
    setImageUri(null);
    setLoading(true);
    
    // 滚动到底部
    scrollToBottom();
    
    // 模拟AI处理延迟
    setTimeout(() => {
      // 获取响应
      const responseText = mockResponse(input, !!userMessage.imageUri);
      
      // 添加AI响应
      setMessages(prev => [...prev, { 
        id: Date.now() + 1, 
        text: responseText, 
        sender: 'gemini',
        timestamp: Date.now()
      }]);
      setLoading(false);
      
      // 确保滚动到底部
      setTimeout(scrollToBottom, 100);
    }, 2000);
  };

  const handleSuggestionPress = (suggestion: string, index: number) => {
    setSelectedSuggestion(index);
    
    // 添加用户消息
    const userMessage = { 
      id: Date.now(), 
      text: suggestion, 
      sender: 'user' as const,
      timestamp: Date.now()
    };
    setMessages([...messages, userMessage]);
    setLoading(true);
    
    // 滚动到底部
    scrollToBottom();
    
    // 模拟API延迟
    setTimeout(() => {
      // 获取响应
      const responseText = mockResponse(suggestion);
      
      // 添加AI响应
      setMessages(prev => [...prev, { 
        id: Date.now() + 1, 
        text: responseText, 
        sender: 'gemini' as const,
        timestamp: Date.now()
      }]);
      setLoading(false);
      setSelectedSuggestion(null);
      
      // 确保滚动到底部
      setTimeout(scrollToBottom, 100);
    }, 2000);
  };

  const scrollToBottom = () => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  };

  // 处理消息文字格式
  const formatMessageText = (text: string) => {
    // 处理粗体文本 **text**
    const boldPattern = /\*\*(.*?)\*\*/g;
    const parts = [];
    let lastIndex = 0;
    let match;
    
    while ((match = boldPattern.exec(text)) !== null) {
      if (match.index > lastIndex) {
        parts.push(
          <Text key={`text-${lastIndex}`} style={styles.messageText}>
            {text.slice(lastIndex, match.index)}
          </Text>
        );
      }
      
      parts.push(
        <Text 
          key={`bold-${match.index}`} 
          style={[styles.messageText, { fontWeight: 'bold' }]}
        >
          {match[1]}
        </Text>
      );
      
      lastIndex = match.index + match[0].length;
    }
    
    if (lastIndex < text.length) {
      parts.push(
        <Text key={`text-${lastIndex}`} style={styles.messageText}>
          {text.slice(lastIndex)}
        </Text>
      );
    }
    
    return parts.length > 0 ? parts : <Text style={styles.messageText}>{text}</Text>;
  };

  const renderImageMessage = (uri: string) => {
    return (
      <View style={styles.imageMessageContainer}>
        <Image 
          source={{ uri }} 
          style={styles.imageMessage}
          resizeMode="cover"
        />
      </View>
    );
  };

  // 尝试获取用户位置
  useEffect(() => {
    (async () => {
      try {
        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          console.log('Permission to access location was denied');
          return;
        }
        
        let location = await Location.getCurrentPositionAsync({});
        setUserLocation(location);
      } catch (error) {
        console.error('Error getting location:', error);
      }
    })();
  }, []);
  
  // 发送文本到Gemini API
  const sendToGemini = async (text: string, imageBase64: string | null = null) => {
    try {
      let requestBody: {
        contents: {
          parts: Array<{ text?: string, inlineData?: { mimeType: string, data: string } }>
        }[],
        generationConfig: {
          temperature: number,
          maxOutputTokens: number,
          topK?: number,
          topP?: number
        }
      } = {
        contents: [
          {
            parts: [
              { text }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.4,        // 降低温度以获得更精确的回答
          maxOutputTokens: 1024,   // 增加输出长度限制
          topK: 40,                // 优化采样参数
          topP: 0.95               // 优化采样参数
        }
      };
      
      // 如果有图片，添加到请求中
      if (imageBase64) {
        requestBody.contents[0].parts.unshift({
          inlineData: {
            mimeType: "image/jpeg",
            data: imageBase64
          }
        });
      }
      
      // 如果有位置信息，添加到请求中
      if (userLocation && userLocation.coords) {
        const locationText = `User is located at coordinates: ${userLocation.coords.latitude}, ${userLocation.coords.longitude}.`;
        requestBody.contents[0].parts.push({ text: `\n${locationText}` });
      }
      
      const response = await fetch(`${API_CONFIG.geminiEndpoint}?key=${API_CONFIG.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });
      
      const data = await response.json();
      return data.candidates?.[0]?.content?.parts?.[0]?.text || "Sorry, I couldn't generate a response.";
    } catch (error) {
      console.error('Error calling Gemini API:', error);
      return "Sorry, there was an error processing your request.";
    }
  };
  
  // 文本转语音 (使用Standard模型)
  const textToSpeech = async (text: string) => {
    try {
      addLog('调用 Text-to-Speech API (Standard Voice)...');
      const response = await fetch(`${API_CONFIG.textToSpeechEndpoint}?key=${API_CONFIG.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: { text },
          // 指定使用标准语音模型，例如 en-US-Standard-C
          // 您可以根据需要查找并替换为其他标准语音名称
          voice: { languageCode: 'en-US', name: 'en-US-Standard-C' }, 
          audioConfig: { audioEncoding: 'MP3' }
        })
      });
      
      const data = await response.json();
      if (data.audioContent) {
        addLog('Text-to-Speech 成功');
        return data.audioContent; // Base64 encoded audio
      } else {
        addLog(`Text-to-Speech 返回无效数据: ${JSON.stringify(data)}`);
        return null;
      }
    } catch (error) {
      addLog(`Text-to-Speech API 错误: ${error}`);
      return null;
    }
  };
  
  // 播放音频
  const playAudio = async (base64Audio: string) => {
    try {
      addLog(`开始播放音频，数据长度: ${base64Audio.length}`);
      setIsPlayingAudio(true);
      
      // 将Base64转换为临时文件
      const fileUri = FileSystem ? 
        `${FileSystem.documentDirectory}temp_audio.mp3` : 
        '';
      
      if (FileSystem) {
        addLog(`正在将Base64音频写入临时文件: ${fileUri}`);
        await FileSystem.writeAsStringAsync(fileUri, base64Audio, {
          encoding: FileSystem.EncodingType.Base64,
        });
        
        // 查看文件信息
        const fileInfo = await FileSystem.getInfoAsync(fileUri);
        addLog(`临时文件信息: ${JSON.stringify(fileInfo)}`);
        
        if (!fileInfo.exists || fileInfo.size === 0) {
          addLog('错误: 临时文件未成功创建或为空');
          setIsPlayingAudio(false);
          return;
        }
      } else {
        addLog('警告: FileSystem不可用，无法创建临时文件');
        setIsPlayingAudio(false);
        return;
      }
      
      // 创建并播放音频
      addLog('创建音频对象...');
      const { Audio: ExpoAudio } = require('expo-av');
      const soundObject = new ExpoAudio.Sound();
      
      addLog(`加载音频文件: ${fileUri}`);
      await soundObject.loadAsync({ uri: fileUri });
      
      addLog('开始播放音频...');
      await soundObject.playAsync();
      
      // 监听播放完成
      soundObject.setOnPlaybackStatusUpdate((status: any) => {
        addLog(`播放状态更新: ${JSON.stringify(status)}`);
        if (status && status.didJustFinish) {
          addLog('音频播放完成');
          setIsPlayingAudio(false);
        }
      });
    } catch (error) {
      addLog(`播放音频错误: ${error}`);
      setIsPlayingAudio(false);
    }
  };
  
  // 拍照
  const takePicture = async () => {
    setShowAttachmentMenu(false);
    if (!ImagePicker) {
      Alert.alert('Feature Unavailable', 'Camera functionality is not available.');
      return;
    }
    
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaType.Images,
        quality: 0.8,
        base64: true,
        allowsEditing: true
      });
      
      if (!result.canceled && result.assets && result.assets[0]) {
        setCapturedImage(result.assets[0]);
        
        // 添加图片消息并发送到AI
        await handleSendImageMessage(result.assets[0]);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take picture.');
    }
  };
  
  // 从相册选择
  const pickImage = async () => {
    setShowAttachmentMenu(false);
    if (!ImagePicker) {
      Alert.alert('Feature Unavailable', 'Image picker is not available.');
      return;
    }
    
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaType.Images,
        quality: 0.8,
        base64: true,
        allowsEditing: true
      });
      
      if (!result.canceled && result.assets && result.assets[0]) {
        setCapturedImage(result.assets[0]);
        
        // 添加图片消息并发送到AI
        await handleSendImageMessage(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to select image.');
    }
  };
  
  // 处理图片消息发送
  const handleSendImageMessage = async (imageAsset: any) => {
    // 添加用户图片消息
    setVoiceChatMessages(prev => [...prev, `[Sent an image${imageAsset.base64 ? "" : " (base64 data unavailable)"}]`]);
    setIsListening(false);
    
    // 模拟延迟
    setTimeout(() => {
      setVoiceChatMessages(prev => [
        ...prev,
        "I can see the image you've shared. This appears to be a travel-related photo. Would you like me to provide more information about this location?"
      ]);
    }, 2000);
  };
  
  // --- 确保 toggleVoiceChatPause 定义在组件内部 ---
  const toggleVoiceChatPause = () => {
    const newPausedState = !isVoicePaused;
    setIsVoicePaused(newPausedState);
    addLog(`语音聊天暂停状态切换为: ${newPausedState}`);
    
    if (newPausedState) {
      // 如果切换到暂停状态
      setIsListening(false); // 停止显示监听动画
      // 如果当前正在录音，则停止它
      if (recordingRef.current) {
        addLog('用户点击暂停，停止当前录音...');
        // 尝试停止录音，但不触发后续处理 (用 Promise 确保停止完成)
        stopRecordingAndProcess(false).catch(err => addLog(`暂停时停止录音出错: ${err}`)); 
      }
    } else {
      // 如果从暂停状态恢复
      setIsListening(true); // 准备开始监听
      addLog('用户取消暂停，准备开始监听...');
      // 延迟一小段时间后开始监听循环，给用户反应时间
      setTimeout(startListeningLoop, 500);
    }
  };
  
  // --- recordAudio, handleRecordingStatusUpdate, stopRecordingAndProcess 定义 ---
  // (确保它们也定义在 IGuidedScreen 内部)
  const recordAudio = async (): Promise<string | null> => {
    // 使用 Promise 来处理异步的录音结束事件
    return new Promise(async (resolve, reject) => {
      // 将 resolve 和 reject 保存到 Ref 中，以便在回调中使用
      resolveRecordPromiseRef.current = resolve;
      rejectRecordPromiseRef.current = reject;
      
      try {
        addLog('开始录制音频 (VAD 模式)...');
        
        const { Audio: ExpoAudio } = require('expo-av');
        const permissionResult = await ExpoAudio.requestPermissionsAsync();
        addLog(`录音权限状态: ${permissionResult.status}`);
        if (permissionResult.status !== 'granted') {
          addLog('录音权限被拒绝');
          reject(new Error('录音权限被拒绝'));
          return;
        }
        
        if (Audio.setAudioModeAsync) {
          await Audio.setAudioModeAsync({
            allowsRecordingIOS: true,
            playsInSilentModeIOS: true,
          });
        }
        
        addLog('创建录音对象 (高质量模式以支持VAD)...');
        // 使用 HIGH_QUALITY 并配置 metering
        if (Audio.Recording && Audio.RecordingOptionsPresets) {
          const { recording, status } = await Audio.Recording.createAsync(
            Audio.RecordingOptionsPresets.HIGH_QUALITY,
            handleRecordingStatusUpdate, // 传递状态更新回调
            500 // 更新间隔 (毫秒)
          );
        }
        
        recordingRef.current = recording; // 保存录音对象
        
        // --- 移除固定时长的等待 ---
        // await new Promise(resolve => setTimeout(resolve, 5000));
        
        addLog('录音已开始，等待用户发言 (VAD 模式)...');
        setVoiceChatMessages(prev => [...prev, "[系统] 我正在听..."]);
        
        // Promise 将在 handleRecordingStatusUpdate 中通过 resolve/reject 解决
        
      } catch (error) {
        addLog(`录音初始化错误: ${error}`);
        reject(error); // 如果创建录音失败，拒绝 Promise
      }
    });
  };

  // --- 新增录音状态更新处理函数 ---
  const handleRecordingStatusUpdate = async (status: any) => {
    if (!status.isRecording) {
      // 如果因为某种原因停止了录音 (例如手动停止或错误)，确保 Promise 被解决
      if (resolveRecordPromiseRef.current) {
         addLog("录音意外停止");
         await stopRecordingAndProcess(false); // 尝试获取URI但标记为非VAD停止
      }
      return;
    }

    addLog(`录音状态: duration=${status.durationMillis}ms, level=${status.metering?.toFixed(2) ?? 'N/A'}dB`);

    // --- VAD 逻辑 ---
    const currentTime = Date.now();
    const metering = status.metering; // 音量 (dBFS)

    if (metering !== undefined && metering < SILENCE_THRESHOLD) {
      // 检测到低于阈值的音量
      if (silenceStartTimeRef.current === null) {
        // 开始计时静音
        silenceStartTimeRef.current = currentTime;
        addLog(`检测到静音开始，阈值=${SILENCE_THRESHOLD}dB`);
      } else if (currentTime - silenceStartTimeRef.current > SILENCE_DURATION_MS) {
        // 静音持续时间足够长，判断为说话结束
        addLog(`检测到用户停止说话 (静音 > ${SILENCE_DURATION_MS}ms)`);
        await stopRecordingAndProcess(); // 停止录音并处理
        return; // 退出回调
      }
    } else {
      // 音量高于阈值，重置静音计时器
      if (silenceStartTimeRef.current !== null) {
         addLog("检测到声音，重置静音计时");
      }
      silenceStartTimeRef.current = null;
    }

    // 安全措施：达到最大录音时长
    if (status.durationMillis > MAX_RECORDING_DURATION_MS) {
      addLog(`达到最大录音时长 (${MAX_RECORDING_DURATION_MS}ms)，自动停止`);
      await stopRecordingAndProcess(); // 停止录音并处理
    }
  };

  // --- 新增停止录音并处理的辅助函数 ---
  const stopRecordingAndProcess = async (isVadStop: boolean = true) => {
    const recording = recordingRef.current;
    if (!recording) {
       addLog("错误：尝试停止录音但 recordingRef 为空");
       if(rejectRecordPromiseRef.current) rejectRecordPromiseRef.current(new Error("Recording object not found"));
       return; // 退出
    }
    
    // 防止重复停止
    recordingRef.current = null; 
    // 清空静音计时
    silenceStartTimeRef.current = null; 

    try {
      addLog('正在停止录音并卸载...');
      await recording.stopAndUnloadAsync();
      addLog('录音已停止');
      
      const uri = recording.getURI();
      addLog(`录音文件URI: ${uri}`);
      
      if (!uri) {
        addLog('无法获取录音URI');
        if(rejectRecordPromiseRef.current) rejectRecordPromiseRef.current(new Error("无法获取录音URI"));
        return;
      }
      
      if (!FileSystem) {
         addLog('FileSystem模块不可用');
         if(rejectRecordPromiseRef.current) rejectRecordPromiseRef.current(new Error("FileSystem不可用"));
         return;
      }
      
      const fileInfo = await FileSystem.getInfoAsync(uri);
      addLog(`文件信息: ${JSON.stringify(fileInfo)}`);
      if (!fileInfo.exists) {
        addLog('录音文件不存在');
        if(rejectRecordPromiseRef.current) rejectRecordPromiseRef.current(new Error("录音文件不存在"));
        return;
      }
      
      addLog(`开始读取录音文件，大小: ${fileInfo.size} 字节`);
      const base64Audio = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      addLog(`成功读取录音为base64，长度: ${base64Audio.length}`);
      
      // 解决 Promise，将 base64 数据传递出去
      if (resolveRecordPromiseRef.current) {
          resolveRecordPromiseRef.current(base64Audio); 
      }
      
    } catch (error) {
      addLog(`停止或处理录音时出错: ${error}`);
      if (rejectRecordPromiseRef.current) {
        rejectRecordPromiseRef.current(error);
      }
    } finally {
       // 清理 Promise Refs
       resolveRecordPromiseRef.current = null;
       rejectRecordPromiseRef.current = null;
    }
  };

  // --- 修改: 重命名 handleVoiceInput 并调整流程 ---
  const startListeningLoop = async () => {
    // 确保不在播放或暂停状态
    if (isPlayingAudio || isVoicePaused || !isListening) {
      addLog('条件不满足，不启动新的监听循环');
      return;
    }

    try {
      addLog('开始新的监听循环...');
      // 调用新的 recordAudio (VAD 模式)
      const audioBase64 = await recordAudio(); // 这个 Promise 会在 VAD 检测到结束时 resolve

      if (audioBase64) {
        // 音频录制成功并获取到数据，开始处理
        await processAudioAndRespond(audioBase64);
      } else {
        // recordAudio 返回 null 或 Promise 被 reject
        addLog('录音失败或未获取到音频数据，准备下一次监听');
        // 短暂延迟后重新开始监听，避免快速失败循环
        setTimeout(startListeningLoop, 1000);
      }
    } catch (error) {
      addLog(`监听循环出错: ${error}, 准备下一次监听`);
      // 同样，短暂延迟后重新开始监听
      setTimeout(startListeningLoop, 1000);
    }
  };

  // --- 新增: 处理音频并响应的函数 ---
  const processAudioAndRespond = async (audioBase64: string) => {
    try {
      setIsListening(false); // 处理期间暂时不监听
      addLog('开始处理录制的音频...');
      let userQuery = "";

      // 步骤 1: Speech-to-Text (优先使用 Gemini)
      if (useGeminiSTT) {
        addLog('使用 Gemini 2.0-flash-lite API 进行语音识别');
        const transcript = await geminiSpeechToText(audioBase64);
        if (transcript) {
          userQuery = transcript;
          addLog(`Gemini STT 成功: "${userQuery}"`);
        } else {
          addLog('Gemini STT 失败，尝试标准 STT API');
          const standardTranscript = await speechToText(audioBase64);
          if (standardTranscript) {
            userQuery = standardTranscript;
            addLog(`标准 STT 成功: "${userQuery}"`);
          } else {
            addLog('所有 STT 尝试失败');
            throw new Error('语音识别失败');
          }
        }
      } else {
        addLog('使用标准 Speech-to-Text API 进行转录');
        const transcript = await speechToText(audioBase64);
        if (transcript) {
          userQuery = transcript;
          addLog(`标准 STT 成功: "${userQuery}"`);
        } else {
          addLog('标准 STT 失败');
          throw new Error('语音识别失败');
        }
      }

      if (!userQuery) {
        throw new Error('未能获取用户查询文本');
      }

      // 步骤 2: 发送给 Gemini LLM
      addLog(`将文本查询 "${userQuery}" 发送给 Gemini 2.0-flash-lite LLM`);
      setVoiceChatMessages(prev => [...prev, userQuery]); // 显示用户说的话
      setVoiceChatMessages(prev => [...prev, "思考中..."]);
      
      const aiResponse = await sendToGemini(userQuery);
      addLog(`收到 Gemini 2.0-flash-lite LLM 响应: "${aiResponse}"`);

      if (!aiResponse || typeof aiResponse !== 'string') {
        addLog('无效的 LLM 响应');
        throw new Error('无效的 LLM 响应');
      }
      // 更新UI，替换 "思考中..."
      setVoiceChatMessages(prev => {
        const newMessages = [...prev];
        if (newMessages[newMessages.length - 1] === "思考中...") {
          newMessages.pop();
        }
        return [...newMessages, aiResponse];
      });

      // 步骤 3: Text-to-Speech 并播放
      addLog('调用 Google Cloud Text-to-Speech API 播放响应');
      const audioContent = await textToSpeech(aiResponse);
      if (audioContent) {
        await playAudio(audioContent); // playAudio 内部会设置 isPlayingAudio
        // 等待播放完成 - 通过检查 isPlayingAudio 状态
        await waitForAudioPlayback(); 
      } else {
        addLog('TTS 失败');
      }

    } catch (error) {
      addLog(`处理音频或响应时出错: ${error}, 尝试使用备用方案`);
      // 可以在这里添加更详细的错误处理或备用逻辑
      setVoiceChatMessages(prev => [...prev, "抱歉，处理时遇到问题。"]);
      // 即使出错，也要尝试回到监听状态
    } finally {
      // 无论成功或失败，重新开始监听 (如果未暂停)
      if (!isVoicePaused) {
          addLog("准备下一轮监听...");
          setIsListening(true); // 重新设置为监听状态
          setTimeout(startListeningLoop, 500); // 短暂延迟后开始下一轮监听
      } else {
          addLog("当前已暂停，不自动开始下一轮监听。");
          setIsListening(false); // 确保在暂停状态下不显示监听中
      }
    }
  };
  
  // --- 新增: 等待音频播放完成的辅助函数 ---
  const waitForAudioPlayback = (): Promise<void> => {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (!isPlayingAudio) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100); // 每 100ms 检查一次
    });
  };

  // Gemini 2.0 直接处理语音
  const geminiSpeechToText = async (audioBase64: string) => {
    try {
      addLog('调用 Gemini 2.0 语音识别 (gemini-2.0-flash-lite)...');
      addLog(`音频数据长度: ${audioBase64.length}`);
      
      // 验证base64数据
      if (!audioBase64 || audioBase64.length < 100) {
        addLog(`错误: 音频数据太短或为空，长度=${audioBase64.length}`);
        return null;
      }
      
      // 构建请求体 - 针对gemini-2.0-flash-lite优化参数
      const requestBody = {
        contents: [
          {
            parts: [
              {
                audio_data: {
                  mime_type: "audio/amr",  // 尝试使用AMR格式，与LOW_QUALITY预设匹配
                  data: audioBase64
                }
              },
              {
                text: "This is an audio recording. Please transcribe the speech and respond to the user's travel-related query."
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.1,    // 降低温度以获取更精确的转录结果
          maxOutputTokens: 1024, // 增加输出长度限制，以获取完整响应
          topK: 40,            // 优化采样参数
          topP: 0.95           // 优化采样参数
        }
      };

      addLog('正在发送音频数据到Gemini 2.0 (gemini-2.0-flash-lite)...');
      addLog(`请求体部分内容: ${JSON.stringify(requestBody.contents[0].parts[1])}`);
      addLog(`音频数据长度: ${requestBody.contents[0].parts[0]?.audio_data?.data?.length || 'undefined'}`);
      
      // 发送请求到 Gemini 2.0
      const response = await fetch(`${API_CONFIG.geminiAudioEndpoint}?key=${API_CONFIG.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });
      
      // 检查HTTP响应状态
      addLog(`HTTP响应状态: ${response.status} ${response.statusText}`);
      if (!response.ok) {
        const errorText = await response.text();
        addLog(`API错误响应: ${errorText}`);
        return null;
      }
      
      // 解析响应
      const data = await response.json();
      
      // 记录响应但截断过长的内容
      const truncatedResponse = JSON.stringify(data).substring(0, 500) + 
        (JSON.stringify(data).length > 500 ? '...(截断)' : '');
      addLog(`API响应数据: ${truncatedResponse}`);
      
      // 检查响应是否包含错误
      if (data.error) {
        addLog(`Gemini 2.0 返回错误: ${JSON.stringify(data.error)}`);
        return null;
      }
      
      addLog('收到 Gemini 2.0 响应');
      
      // 提取响应文本
      const responseText = data.candidates?.[0]?.content?.parts?.[0]?.text;
      if (responseText) {
        addLog(`识别/理解结果: "${responseText}"`);
        return responseText;
      } else {
        addLog('Gemini 2.0 未返回有效结果');
        return null;
      }
    } catch (error) {
      addLog(`Gemini 2.0 调用错误: ${error}`);
      return null;
    }
  };

  // 在IGuidedScreen函数中添加刷新状态
  const [refreshUI, setRefreshUI] = useState(0);
  
  // 强制刷新UI
  const forceRefresh = () => {
    setRefreshUI(prev => prev + 1);
  };

  // 添加测试录音功能
  const testAudioRecording = async () => {
    try {
      // 清空之前的消息
      setVoiceChatMessages(["[系统] 开始测试录音功能..."]);
      addLog('=== 开始测试录音功能 ===');
      
      // 测试录音
      const audioBase64 = await recordAudio();
      
      if (audioBase64) {
        setVoiceChatMessages(prev => [...prev, `[系统] 录音成功! 数据长度: ${audioBase64.length}`]);
        addLog(`录音成功，数据长度: ${audioBase64.length}`);
        
        // 保存文件到设备以便测试
        if (FileSystem) {
          const testFilePath = `${FileSystem.documentDirectory}test_recording.mp3`;
          await FileSystem.writeAsStringAsync(testFilePath, audioBase64, {
            encoding: FileSystem.EncodingType.Base64,
          });
          addLog(`测试文件已保存至: ${testFilePath}`);
          setVoiceChatMessages(prev => [...prev, `[系统] 测试文件已保存至: ${testFilePath}`]);
        }
        
        // 尝试播放录音 - 这个步骤可以验证录音是否正常
        addLog('尝试播放录音');
        setVoiceChatMessages(prev => [...prev, "[系统] 尝试播放录音..."]);
        
        try {
          await playAudio(audioBase64);
          addLog('播放录音成功!');
          setVoiceChatMessages(prev => [...prev, "[系统] 播放录音成功!"]);
        } catch (playError) {
          addLog(`播放录音失败: ${playError}`);
          setVoiceChatMessages(prev => [...prev, `[系统] 播放录音失败: ${playError}`]);
        }
      } else {
        addLog('录音失败，未获取到音频数据');
        setVoiceChatMessages(prev => [...prev, "[系统] 录音失败，未获取到音频数据"]);
      }
      
      addLog('=== 录音测试完成 ===');
      // 强制刷新UI
      forceRefresh();
    } catch (error) {
      addLog(`测试录音功能时出错: ${error}`);
      setVoiceChatMessages(prev => [...prev, `[系统] 测试录音功能时出错: ${error}`]);
      forceRefresh();
    }
  };

  // 调试日志模态窗
  const renderDebugLogs = () => {
    if (!showDebugLogs) return null;
    
    return (
      <View style={styles.debugLogsContainer}>
        <View style={styles.debugLogsHeader}>
          <Text style={styles.debugLogsTitle}>调试日志</Text>
          <View style={{flexDirection: 'row'}}>
            <TouchableOpacity onPress={forceRefresh} style={styles.debugButton}>
              <Text style={styles.clearLogsText}>刷新</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setDebugLogs([])} style={styles.clearLogsButton}>
              <Text style={styles.clearLogsText}>清除</Text>
            </TouchableOpacity>
          </View>
        </View>
        <FlatList
          data={debugLogs}
          keyExtractor={(_, index) => `log-${index}-${refreshUI}`}
          renderItem={({item}) => (
            <Text style={styles.logEntry}>{item}</Text>
          )}
          style={styles.logsList}
          // 自动滚动到底部
          ref={ref => {
            if (ref && debugLogs.length > 0) {
              setTimeout(() => ref.scrollToEnd({animated: false}), 100);
            }
          }}
        />
      </View>
    );
  };

  // 在IGuidedScreen函数的顶部添加一个useEffect钩子用于初始通知
  useEffect(() => {
    // 在组件挂载时，添加一条通知消息
    const timeoutId = setTimeout(() => {
      Alert.alert(
        "功能已更新",
        "语音聊天界面已添加测试录音功能按钮，点击即可测试麦克风。\n\n点击调试图标可查看详细日志。",
        [{ text: "我知道了" }]
      );
    }, 1000);
    
    return () => clearTimeout(timeoutId);
  }, []);

  // 添加 VAD 相关的 Ref
  const silenceStartTimeRef = useRef(null);
  const recordingRef = useRef(null);
  const resolveRecordPromiseRef = useRef(null);
  const rejectRecordPromiseRef = useRef(null);

  // 关闭语音聊天模态框
  const closeVoiceChat = () => {
    addLog('关闭语音聊天...');
    
    // 如果当前正在录音，停止它
    if (recordingRef.current) {
      addLog('关闭聊天窗口时停止当前录音...');
      stopRecordingAndProcess(false).catch(err => addLog(`关闭时停止录音出错: ${err}`));
    }
    
    // 重置所有状态
    setShowVoiceChatModal(false);
    setIsListening(false);
    setIsVoicePaused(false);
    setIsPlayingAudio(false);
    setVoiceChatMessages([]); // 清空消息
    
    // 确保清理所有录音相关资源
    silenceStartTimeRef.current = null;
    recordingRef.current = null;
    resolveRecordPromiseRef.current = null;
    rejectRecordPromiseRef.current = null;
  };
  
  // 切换语音录制状态 (打开/关闭语音聊天窗口)
  const toggleVoiceRecording = () => {
    // 如果已经打开，则关闭
    if (showVoiceChatModal) {
      closeVoiceChat();
      return;
    }
    
    // 否则打开语音聊天窗口
    setIsVoicePaused(false);
    setShowVoiceChatModal(true);
    setVoiceChatMessages(["欢迎使用 iGuided 语音助手。我可以帮您规划旅行、推荐目的地或解答旅行相关问题。请问有什么我可以帮您的吗？"]);
    
    // 为欢迎消息添加语音 (稍微延迟以确保界面已渲染)
    setTimeout(async () => {
      addLog('播放欢迎语...');
      const welcomeMessage = "欢迎使用 iGuided 语音助手。我可以帮您规划旅行、推荐目的地或解答旅行相关问题。请问有什么我可以帮您的吗？";
      const audioContent = await textToSpeech(welcomeMessage);
      
      if (audioContent) {
        await playAudio(audioContent);
        await waitForAudioPlayback();
        
        // 播放完毕后，启动语音识别
        addLog('欢迎语播放完毕，开始监听...');
        setIsListening(true);
        startListeningLoop();
      } else {
        addLog('欢迎语 TTS 失败，直接开始监听');
        setIsListening(true);
        startListeningLoop();
      }
    }, 500);
  };
  
  // 标准 STT (语音转文字)
  const speechToText = async (audioBase64: string): Promise<string | null> => {
    try {
      addLog('调用标准 Speech-to-Text API...');
      addLog(`音频数据长度: ${audioBase64.length}`);
      
      // 验证 base64 数据
      if (!audioBase64 || audioBase64.length < 100) {
        addLog(`错误: 音频数据太短或为空，长度=${audioBase64.length}`);
        return null;
      }
      
      // 构建请求体
      const requestBody = {
        audio: {
          content: audioBase64
        },
        config: {
          encoding: 'ENCODING_UNSPECIFIED', // 自动检测
          sampleRateHertz: 44100, // 如果知道确切的采样率，请指定
          languageCode: 'en-US', // 中文可使用 'zh-CN'
          model: 'default',
          enableAutomaticPunctuation: true
        }
      };
      
      // 发送请求
      addLog('正在发送音频数据到标准 Speech-to-Text API...');
      const response = await fetch(`${API_CONFIG.speechToTextEndpoint}?key=${API_CONFIG.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });
      
      // 检查响应状态
      addLog(`HTTP 响应状态: ${response.status} ${response.statusText}`);
      if (!response.ok) {
        const errorText = await response.text();
        addLog(`API 错误响应: ${errorText}`);
        return null;
      }
      
      // 解析响应
      const data = await response.json();
      
      // 记录响应
      addLog(`API 响应数据: ${JSON.stringify(data).substring(0, 500)}...`);
      
      // 提取转录结果
      const transcript = data.results?.[0]?.alternatives?.[0]?.transcript;
      if (transcript) {
        addLog(`识别结果: "${transcript}"`);
        return transcript;
      } else {
        addLog('未识别到语音内容');
        return null;
      }
    } catch (error) {
      addLog(`Speech-to-Text API 错误: ${error}`);
      return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#202124" />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <YStack flex={1} backgroundColor="$backgroundMuted">
          {/* 🎨 Trekmate 4.0 - 现代化头部 */}
          <Card
            backgroundColor="$backgroundDefault"
            borderRadius={0}
            shadowColor="$primary"
            shadowOffset={{ width: 0, height: 2 }}
            shadowOpacity={0.08}
            shadowRadius={4}
            elevation={3}
          >
            <XStack
              alignItems="center"
              space="$sm"
              padding="$lg"
              paddingTop="$xl"
            >
              <Ionicons name="navigate" size={20} color={Colors.primary} />
              <TamaguiText fontSize="$lg" fontWeight="600" color="$textPrimary">
                iGuided AI
              </TamaguiText>
            </XStack>
          </Card>
          
          <TamaguiScrollView
            ref={scrollViewRef}
            flex={1}
            padding="$md"
            keyboardShouldPersistTaps="handled"
          >
            {/* 🎨 Trekmate 4.0 - 现代化日期分隔线 */}
            <XStack alignItems="center" space="$sm" marginVertical="$md">
              <View flex={1} height={1} backgroundColor="$borderDefault" />
              <TamaguiText fontSize="$sm" color="$textSecondary">
                {date}
              </TamaguiText>
              <View flex={1} height={1} backgroundColor="$borderDefault" />
            </XStack>

            {messages.length === 0 ? (
              <YStack alignItems="center" space="$lg" padding="$xl">
                <YStack alignItems="center" space="$md">
                  <TamaguiText fontSize="$xl" fontWeight="700" color="$textPrimary">
                    iGuided AI
                  </TamaguiText>
                  <TamaguiText
                    fontSize="$md"
                    color="$textSecondary"
                    textAlign="center"
                    lineHeight={20}
                  >
                    Your intelligent travel assistant, providing professional travel advice, itinerary planning, and destination information
                  </TamaguiText>
                </YStack>

                <YStack space="$sm" width="100%">
                  {suggestions.map((suggestion, index) => (
                    <TamaguiButton
                      key={index}
                      title={suggestion}
                      variant={selectedSuggestion === index ? "default" : "outline"}
                      onPress={() => handleSuggestionPress(suggestion, index)}
                      disabled={loading}
                    />
                  ))}
                </YStack>
              </YStack>
            ) : (
              messages.map((message, index) => (
                <XStack
                  key={message.id}
                  space="$sm"
                  marginVertical="$xs"
                  alignItems="flex-start"
                >
                  {/* 🎨 Trekmate 4.0 - 现代化头像 */}
                  {message.sender === 'gemini' ? (
                    <Card
                      width={32}
                      height={32}
                      borderRadius="$full"
                      backgroundColor="$primary"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <TamaguiText fontSize="$sm" fontWeight="600" color="$textOnPrimary">
                        G
                      </TamaguiText>
                    </Card>
                  ) : (
                    <Card
                      width={32}
                      height={32}
                      borderRadius="$full"
                      backgroundColor="$backgroundMuted"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <TamaguiText fontSize="$sm" fontWeight="600" color="$textPrimary">
                        U
                      </TamaguiText>
                    </Card>
                  )}

                  {/* 🎨 Trekmate 4.0 - 现代化消息气泡 */}
                  <Card
                    flex={1}
                    backgroundColor={message.sender === 'user' ? '$primary' : '$backgroundDefault'}
                    borderRadius="$md"
                    padding="$md"
                    maxWidth="85%"
                  >
                    <TamaguiText
                      color={message.sender === 'user' ? '$textOnPrimary' : '$textPrimary'}
                      fontSize="$md"
                      lineHeight={20}
                    >
                      {formatMessageText(message.text)}
                    </TamaguiText>
                    {message.imageUri && renderImageMessage(message.imageUri)}
                  </Card>
                </XStack>
              ))
            )}
            
            {/* 加载动画 */}
            {loading && (
              <View style={styles.messageRow}>
                <View style={styles.botAvatarContainer}>
                  <Text style={styles.botAvatarText}>G</Text>
                </View>
                <TypingAnimation />
              </View>
            )}
            
            {/* 图片预览 */}
            {imageUri && (
              <View style={styles.imagePreviewContainer}>
                <Image 
                  source={{ uri: imageUri }} 
                  style={styles.imagePreview}
                  resizeMode="cover"
                />
                <TouchableOpacity 
                  style={styles.removeImageButton} 
                  onPress={handleRemoveImage}
                >
                  <Ionicons name="close" size={16} color="#ffffff" />
                </TouchableOpacity>
              </View>
            )}
          </TamaguiScrollView>
          
          {/* 🎨 Trekmate 4.0 - 现代化输入区域 */}
          <Card
            backgroundColor="$backgroundDefault"
            borderTopWidth={1}
            borderTopColor="$borderDefault"
            padding="$md"
          >
            <XStack space="$sm" alignItems="flex-end">
              <TamaguiButton
                variant="ghost"
                size="$sm"
                circular
                onPress={() => setShowImageAttachmentModal(true)}
                icon={<Ionicons name="add-circle" size={24} color={Colors.primary} />}
              />

              <Card
                flex={1}
                backgroundColor="$backgroundMuted"
                borderRadius="$lg"
                padding="$sm"
              >
                <TextInput
                  style={{
                    fontSize: 16,
                    color: Colors.textPrimary,
                    minHeight: 40,
                    maxHeight: 120,
                    textAlignVertical: 'top',
                  }}
                  value={input}
                  onChangeText={setInput}
                  placeholder="Send a message to iGuided..."
                  placeholderTextColor={Colors.textSecondary}
                  multiline
                  maxLength={1000}
                />
              </Card>

              {/* 语音聊天按钮 */}
              <TamaguiButton
                variant="default"
                size="$sm"
                circular
                onPress={toggleVoiceRecording}
                icon={<Ionicons name="mic-outline" size={24} color="white" />}
              />

              <TamaguiButton
                variant="default"
                size="$sm"
                circular
                onPress={handleSend}
                disabled={(!input.trim() && !imageUri) || loading}
                opacity={(!input.trim() && !imageUri) || loading ? 0.5 : 1}
                icon={<Ionicons name="send" size={18} color="white" />}
              />
            </XStack>
          </Card>
          
          {/* 图片附件模态窗 */}
          <Modal
            visible={showImageAttachmentModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setShowImageAttachmentModal(false)}
          >
            <TouchableWithoutFeedback onPress={() => setShowImageAttachmentModal(false)}>
              <View style={styles.modalContainer}>
                <TouchableWithoutFeedback>
                  <View style={styles.modalContent}>
                    <View style={styles.modalHeader}>
                      <Text style={styles.modalTitle}>Add Image</Text>
                      <TouchableOpacity onPress={() => setShowImageAttachmentModal(false)}>
                        <Ionicons name="close" size={24} color="#5f6368" />
                      </TouchableOpacity>
                    </View>
                    
                    <TouchableOpacity 
                      style={styles.modalOption} 
                      onPress={handleTakePhoto}
                    >
                      <Ionicons name="camera" size={24} color="#4285f4" />
                      <Text style={styles.modalOptionText}>Take photo</Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity 
                      style={styles.modalOption} 
                      onPress={handleChooseFromLibrary}
                    >
                      <Ionicons name="image" size={24} color="#4285f4" />
                      <Text style={styles.modalOptionText}>Choose from library</Text>
                    </TouchableOpacity>
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </TouchableWithoutFeedback>
          </Modal>

          {/* 实时语音聊天模态窗 */}
          <Modal
            visible={showVoiceChatModal}
            transparent={true}
            animationType="slide"
            onRequestClose={closeVoiceChat}
          >
            <View style={styles.voiceChatModalContainer}>
              <View style={styles.voiceChatModalHeader}>
                <Text style={styles.voiceChatModalTitle}>iGuided 语音助手</Text>
                <View style={{flexDirection: 'row'}}>
                  {/* 添加刷新按钮 */}
                  <TouchableOpacity onPress={forceRefresh} style={styles.debugButton}>
                    <Ionicons name="refresh" size={24} color="#ffffff" />
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => setShowDebugLogs(!showDebugLogs)} style={styles.debugButton}>
                    <Ionicons name="bug" size={24} color="#ffffff" />
                  </TouchableOpacity>
                  <TouchableOpacity onPress={closeVoiceChat} style={styles.voiceChatCloseButton}>
                    <Ionicons name="close" size={24} color="#ffffff" />
                  </TouchableOpacity>
                </View>
              </View>
              
              {/* 添加快速测试按钮 */}
              <View style={styles.testButtonsContainer}>
                <TouchableOpacity 
                  style={styles.mainTestButton} 
                  onPress={testAudioRecording}
                >
                  <Ionicons name="mic-circle" size={28} color="#fff" />
                  <Text style={styles.mainTestButtonText}>测试录音功能</Text>
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.voiceChatMessagesContainer}>
                {voiceChatMessages.map((message, index) => (
                  <View key={`msg-${index}-${refreshUI}`} style={[
                    styles.voiceChatMessage,
                    message.startsWith('[系统]') 
                      ? styles.voiceChatSystemMessage 
                      : (index % 2 === 0 ? styles.voiceChatAiMessage : styles.voiceChatUserMessage)
                  ]}>
                    <Text style={styles.voiceChatMessageText}>{message}</Text>
                  </View>
                ))}
                
                {isListening && (
                  <View style={styles.listeningIndicator}>
                    <Text style={styles.listeningText}>Listening{isVoicePaused ? " (Paused)" : "..."}</Text>
                    <View style={styles.listeningAnimation}>
                      {waveHeights.map((height, index) => (
                        <View 
                          key={index} 
                          style={[
                            styles.listeningBar, 
                            { 
                              height: isVoicePaused ? 5 : height,
                              backgroundColor: isVoicePaused ? '#9AA0A6' : '#8ab4f8'
                            }
                          ]} 
                        />
                      ))}
                    </View>
                  </View>
                )}
                
                {isPlayingAudio && (
                  <View style={styles.playingAudioIndicator}>
                    <Ionicons name="volume-high" size={24} color="#8ab4f8" />
                    <Text style={styles.playingAudioText}>Playing response...</Text>
                  </View>
                )}
              </ScrollView>
              
              {/* Voice chat footer controls */}
              <View style={styles.voiceChatFooter}>
                {isPlayingAudio ? (
                  <View style={styles.voiceChatButton}>
                    <Ionicons name="volume-high" size={32} color="#fff" />
                  </View>
                ) : (
                  <>
                    <TouchableOpacity 
                      style={[styles.voiceChatButton, isListening && !isVoicePaused ? styles.voiceChatButtonActive : null]} 
                      onPress={startListeningLoop} // 主要的录音触发按钮现在调用 startListeningLoop
                    >
                      <Ionicons 
                        name={isListening && !isVoicePaused ? "mic" : "mic-off"} 
                        size={32} 
                        color="#fff" 
                      />
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={styles.voiceChatToggleButton} 
                      onPress={toggleVoiceChatPause} // 确保调用的是组件内的函数
                    >
                      <Ionicons 
                        name={isVoicePaused ? "play" : "pause"} 
                        size={24} 
                        color="#fff" 
                      />
                    </TouchableOpacity>
                  </>
                )}
                
                {/* Gemini STT / Standard STT toggle */}
                <TouchableOpacity 
                  style={[styles.sttToggleButton, useGeminiSTT ? styles.geminiSttActive : styles.standardSttActive]} 
                  onPress={() => setUseGeminiSTT(!useGeminiSTT)}
                >
                  <Text style={styles.sttToggleText}>
                    {useGeminiSTT ? "Gemini STT" : "Standard STT"}
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.closeChatButton} onPress={closeVoiceChat}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          </Modal>

          {/* 调试日志模态窗 - 改为使用函数 */}
          {renderDebugLogs()}
        </YStack>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

// 样式直接定义在组件文件中
const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#202124',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
    backgroundColor: '#202124',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    marginLeft: 12,
  },
  headerLogo: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesList: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 8,
  },
  welcomeContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 24,
  },
  welcomeTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#9aa0a6',
    marginBottom: 40,
    textAlign: 'center',
    lineHeight: 22,
  },
  suggestionContainer: {
    width: '100%',
    maxWidth: 500,
    alignSelf: 'center',
  },
  suggestionButton: {
    backgroundColor: '#303134',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#3c4043',
  },
  suggestionButtonActive: {
    backgroundColor: '#3c4043',
  },
  suggestionText: {
    color: '#e8eaed',
    fontSize: 15,
  },
  messageBubble: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 6,
    maxWidth: width > 500 ? '70%' : '88%',
  },
  userBubble: {
    backgroundColor: '#394675',
    alignSelf: 'flex-end',
    borderTopRightRadius: 4,
  },
  geminiBubble: {
    backgroundColor: '#303134',
    alignSelf: 'flex-start',
    borderTopLeftRadius: 4,
  },
  messageText: {
    color: '#e8eaed',
    fontSize: 15,
    lineHeight: 22,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#303134',
    borderRadius: 12,
    alignSelf: 'flex-start',
    maxWidth: '70%',
  },
  typingDot: {
    height: 6,
    width: 6,
    borderRadius: 3,
    backgroundColor: '#8ab4f8',
    marginHorizontal: 2,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 12,
    backgroundColor: '#202124',
    borderTopWidth: 1,
    borderTopColor: '#3c4043',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  input: {
    flex: 1,
    backgroundColor: '#303134',
    color: '#e8eaed',
    padding: 12,
    paddingTop: 12,
    borderRadius: 24,
    maxHeight: 120,
    fontSize: 15,
    marginRight: 8,
  },
  sendButton: {
    marginLeft: 12,
    backgroundColor: '#8ab4f8',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#3c4043',
  },
  sendIcon: {
    marginLeft: 2,
  },
  // 语音按钮样式
  voiceButton: {
    marginLeft: 8,
    backgroundColor: '#FF5722',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  voiceButtonRecording: {
    backgroundColor: '#EA4335', // Google红色，表示正在录制
    transform: [{ scale: 1.1 }],
  },
  dateDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#3c4043',
  },
  dateText: {
    color: '#9aa0a6',
    fontSize: 13,
    marginHorizontal: 12,
  },
  avatarContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#8ab4f8',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  botAvatarContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#8ab4f8',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  botAvatarText: {
    color: '#202124',
    fontWeight: 'bold',
    fontSize: 14,
  },
  userAvatarText: {
    color: '#202124',
    fontWeight: 'bold',
    fontSize: 14,
  },
  messageContent: {
    flex: 1,
  },
  // 附件按钮样式
  attachmentButton: {
    width: 40, 
    height: 40,
    borderRadius: 20,
    backgroundColor: '#303134',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  attachmentButtonIcon: {
    color: '#9aa0a6',
  },
  attachmentOptionsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 12,
    backgroundColor: '#303134',
    borderRadius: 12,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 1,
    borderColor: '#3c4043',
  },
  attachmentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  attachmentOptionText: {
    marginLeft: 12,
    color: '#e8eaed',
    fontSize: 14,
  },
  imagePreviewContainer: {
    width: '100%',
    padding: 8,
    backgroundColor: '#303134',
    borderRadius: 8,
    marginVertical: 8,
  },
  imagePreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#3c4043',
  },
  imageMessageContainer: {
    width: '100%',
    maxWidth: 300,
  },
  imageMessage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    backgroundColor: '#303134',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#3c4043',
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e8eaed',
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#3c4043',
  },
  modalOptionText: {
    marginLeft: 16,
    fontSize: 15,
    color: '#e8eaed',
  },
  voiceChatModalContainer: {
    flex: 1,
    backgroundColor: '#202124',
  },
  voiceChatModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#3c4043',
    backgroundColor: '#303134',
  },
  voiceChatModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  voiceChatCloseButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceChatMessagesContainer: {
    flex: 1,
    padding: 16,
  },
  voiceChatMessage: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    maxWidth: '85%',
  },
  voiceChatAiMessage: {
    backgroundColor: '#303134',
    alignSelf: 'flex-start',
    borderTopLeftRadius: 4,
  },
  voiceChatUserMessage: {
    backgroundColor: '#394675',
    alignSelf: 'flex-end',
    borderTopRightRadius: 4,
  },
  voiceChatMessageText: {
    color: '#e8eaed',
    fontSize: 16,
    lineHeight: 22,
  },
  listeningIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    marginVertical: 20,
  },
  listeningText: {
    color: '#8ab4f8',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 12,
  },
  listeningAnimation: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 30,
  },
  listeningBar: {
    width: 4,
    backgroundColor: '#8ab4f8',
    marginHorizontal: 3,
    borderRadius: 2,
  },
  voiceChatControlsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#3c4043',
    backgroundColor: '#202124',
  },
  voiceChatControlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#8ab4f8',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginHorizontal: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  voiceChatResumeButton: {
    backgroundColor: '#66BB6A',
  },
  voiceChatPauseButton: {
    backgroundColor: '#F44336',
  },
  voiceChatControlText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  voiceChatListenButton: {
    backgroundColor: '#8ab4f8',
  },
  voiceChatAddButton: {
    backgroundColor: '#66BB6A',
    width: 40,
    height: 40,
    borderRadius: 20,
    paddingHorizontal: 0,
  },
  attachmentMenuContainer: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    backgroundColor: '#202124',
    borderRadius: 12,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 1,
    borderColor: '#3c4043',
  },
  attachmentMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  attachmentMenuText: {
    marginLeft: 12,
    color: '#e8eaed',
    fontSize: 14,
  },
  playingAudioIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    marginVertical: 16,
  },
  playingAudioText: {
    color: '#8ab4f8',
    fontSize: 14,
    marginLeft: 8,
    fontWeight: '500',
  },
  debugButton: {
    padding: 8,
    marginRight: 8,
  },
  debugLogsContainer: {
    position: 'absolute',
    top: 60,
    left: 10,
    right: 10,
    bottom: 80,
    backgroundColor: 'rgba(0,0,0,0.9)',
    borderRadius: 8,
    zIndex: 1000,
  },
  debugLogsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#444',
  },
  debugLogsTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  clearLogsButton: {
    padding: 5,
  },
  clearLogsText: {
    color: '#8ab4f8',
    fontSize: 14,
  },
  logsList: {
    flex: 1,
  },
  logEntry: {
    color: '#0f0',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontSize: 12,
    padding: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  voiceChatFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#3c4043',
  },
  voiceChatButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#8ab4f8',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceChatButtonActive: {
    backgroundColor: '#66BB6A',
  },
  voiceChatToggleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#444',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  sttToggleButton: {
    position: 'absolute',
    left: 130,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
  },
  geminiSttActive: {
    backgroundColor: '#8ab4f8',
    borderColor: '#4285f4',
  },
  standardSttActive: {
    backgroundColor: '#34a853',
    borderColor: '#0d652d',
  },
  sttToggleText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  closeChatButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  testButtonsContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#3c4043',
  },
  mainTestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF5722',
    padding: 12,
    borderRadius: 30,
    marginVertical: 6,
  },
  mainTestButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  voiceChatSystemMessage: {
    backgroundColor: '#444',
    alignSelf: 'center',
    borderRadius: 12,
    maxWidth: '90%',
  },
}); 