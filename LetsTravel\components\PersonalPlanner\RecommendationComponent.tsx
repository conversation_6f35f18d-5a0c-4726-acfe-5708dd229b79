import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Modal,
  Image,
  ActivityIndicator,
  Alert
} from 'react-native';
import RecommendationService, { 
  RecommendationResult, 
  RecommendationFilter 
} from '../../services/personalPlanner/RecommendationService';
import { Activity } from '../types/PersonalPlannerTypes';

interface RecommendationComponentProps {
  visible: boolean;
  onClose: () => void;
  onRecommendationSelected: (activity: Activity) => void;
  destination: string;
}

export const RecommendationComponent = ({
  visible,
  onClose,
  onRecommendationSelected,
  destination
}: RecommendationComponentProps) => {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState({});
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (visible && destination) {
      loadRecommendations();
    }
  }, [visible, destination, filter]);

  const loadRecommendations = async () => {
    setLoading(true);
    try {
      const results = await RecommendationService.getRecommendations(
        destination,
        undefined,
        filter
      );
      setRecommendations(results);
    } catch (error) {
      console.error('Error loading recommendations:', error);
      Alert.alert('加载失败', '无法获取推荐信息');
    } finally {
      setLoading(false);
    }
  };

  const handleRecommendationSelect = (recommendation: any) => {
    const activity = RecommendationService.recommendationToActivity(recommendation);
    onRecommendationSelected(activity);
    onClose();
  };

  const handleFilterChange = (newFilter: any) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
  };

  const filteredRecommendations = recommendations.filter(rec =>
    searchQuery === '' || 
          (rec.place.name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
      (rec.place.address || '').toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderStars = (rating?: number) => {
    if (!rating) return null;
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push('⭐');
    }
    if (hasHalfStar) {
      stars.push('✨');
    }
    
    return (
      <View style={styles.ratingContainer}>
        <Text style={styles.stars}>{stars.join('')}</Text>
        <Text style={styles.ratingText}>{rating.toFixed(1)}</Text>
      </View>
    );
  };

  const getPriceText = (level?: number) => {
    if (!level) return '';
    const priceLabels = {
      1: '💰',
      2: '💰💰',
      3: '💰💰💰',
      4: '💰💰💰💰'
    };
    return priceLabels[level as keyof typeof priceLabels] || '';
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>智能推荐 - {destination}</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>关闭</Text>
          </TouchableOpacity>
        </View>

        {/* 搜索和筛选 */}
        <View style={styles.searchAndFilter}>
          <TextInput
            style={styles.searchInput}
            placeholder="搜索地点..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterBar}>
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.type === 'restaurant' && styles.filterButtonActive
              ]}
              onPress={() => handleFilterChange({ 
                type: filter.type === 'restaurant' ? undefined : 'restaurant' 
              })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.type === 'restaurant' && styles.filterButtonTextActive
              ]}>🍽️ 餐厅</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.type === 'attraction' && styles.filterButtonActive
              ]}
              onPress={() => handleFilterChange({ 
                type: filter.type === 'attraction' ? undefined : 'attraction' 
              })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.type === 'attraction' && styles.filterButtonTextActive
              ]}>🎯 景点</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.type === 'accommodation' && styles.filterButtonActive
              ]}
              onPress={() => handleFilterChange({ 
                type: filter.type === 'accommodation' ? undefined : 'accommodation' 
              })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.type === 'accommodation' && styles.filterButtonTextActive
              ]}>🏨 住宿</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.type === 'shopping' && styles.filterButtonActive
              ]}
              onPress={() => handleFilterChange({ 
                type: filter.type === 'shopping' ? undefined : 'shopping' 
              })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.type === 'shopping' && styles.filterButtonTextActive
              ]}>🛍️ 购物</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.priceRange === 'budget' && styles.filterButtonActive
              ]}
              onPress={() => handleFilterChange({ 
                priceRange: filter.priceRange === 'budget' ? undefined : 'budget' 
              })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.priceRange === 'budget' && styles.filterButtonTextActive
              ]}>💰 经济</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.rating === 4.0 && styles.filterButtonActive
              ]}
              onPress={() => handleFilterChange({ 
                rating: filter.rating === 4.0 ? undefined : 4.0 
              })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.rating === 4.0 && styles.filterButtonTextActive
              ]}>⭐ 高评分</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* 推荐列表 */}
        <ScrollView style={styles.content}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#007bff" />
              <Text style={styles.loadingText}>正在加载推荐...</Text>
            </View>
          ) : (
            <View style={styles.recommendationsList}>
              {filteredRecommendations.map((recommendation) => (
                <TouchableOpacity
                  key={recommendation.place.placeId}
                  style={styles.recommendationCard}
                  onPress={() => handleRecommendationSelect(recommendation)}
                >
                  {/* 地点照片 */}
                  {recommendation.place.photos && recommendation.place.photos[0] && (
                    <Image
                      source={{ uri: recommendation.place.photos[0] }}
                      style={styles.placeImage}
                      resizeMode="cover"
                    />
                  )}

                  <View style={styles.placeContent}>
                    <View style={styles.placeHeader}>
                      <Text style={styles.placeName} numberOfLines={2}>
                        {recommendation.place.name}
                      </Text>
                      <View style={styles.scoreContainer}>
                        <Text style={styles.scoreText}>
                          {Math.round(recommendation.relevanceScore)}
                        </Text>
                      </View>
                    </View>

                    <Text style={styles.placeAddress} numberOfLines={2}>
                      {recommendation.place.address}
                    </Text>

                    <View style={styles.placeMetadata}>
                      {renderStars(recommendation.place.rating)}
                      <Text style={styles.priceLevel}>
                        {getPriceText(recommendation.place.priceLevel)}
                      </Text>
                    </View>

                    <Text style={styles.reasonText}>
                      💡 {recommendation.reasonForRecommendation}
                    </Text>

                    <View style={styles.estimatedInfo}>
                      {recommendation.estimatedDuration && (
                        <Text style={styles.estimatedText}>
                          ⏱️ 约{recommendation.estimatedDuration}分钟
                        </Text>
                      )}
                      {recommendation.estimatedCost && (
                        <Text style={styles.estimatedText}>
                          💰 约¥{recommendation.estimatedCost}
                        </Text>
                      )}
                    </View>

                    {/* 营业状态 */}
                    {recommendation.place.openingHours?.openNow !== undefined && (
                      <View style={styles.statusContainer}>
                        <Text style={[
                          styles.statusText,
                          recommendation.place.openingHours.openNow ? 
                            styles.statusOpen : styles.statusClosed
                        ]}>
                          {recommendation.place.openingHours.openNow ? '营业中' : '已关闭'}
                        </Text>
                      </View>
                    )}

                    {/* 用户评价 */}
                    {recommendation.place.reviews && recommendation.place.reviews[0] && (
                      <View style={styles.reviewContainer}>
                        <Text style={styles.reviewText} numberOfLines={2}>
                          "{recommendation.place.reviews[0].text}"
                        </Text>
                        <Text style={styles.reviewAuthor}>
                          - {recommendation.place.reviews[0].author}
                        </Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              ))}

              {filteredRecommendations.length === 0 && !loading && (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>暂无推荐内容</Text>
                  <Text style={styles.emptySubtext}>尝试调整筛选条件</Text>
                </View>
              )}
            </View>
          )}
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    color: '#007bff',
    fontSize: 16,
  },
  searchAndFilter: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    marginBottom: 12,
  },
  filterBar: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  filterButtonActive: {
    backgroundColor: '#007bff',
    borderColor: '#007bff',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  recommendationsList: {
    padding: 16,
  },
  recommendationCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  placeImage: {
    width: '100%',
    height: 200,
  },
  placeContent: {
    padding: 16,
  },
  placeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  placeName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    marginRight: 12,
  },
  scoreContainer: {
    backgroundColor: '#007bff',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 32,
    alignItems: 'center',
  },
  scoreText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  placeAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  placeMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  stars: {
    fontSize: 14,
    marginRight: 4,
  },
  ratingText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  priceLevel: {
    fontSize: 14,
  },
  reasonText: {
    fontSize: 14,
    color: '#28a745',
    fontStyle: 'italic',
    marginBottom: 8,
  },
  estimatedInfo: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  estimatedText: {
    fontSize: 12,
    color: '#666',
    marginRight: 16,
  },
  statusContainer: {
    marginBottom: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusOpen: {
    color: '#28a745',
  },
  statusClosed: {
    color: '#dc3545',
  },
  reviewContainer: {
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    paddingTop: 8,
    marginTop: 8,
  },
  reviewText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  reviewAuthor: {
    fontSize: 10,
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
  },
});

export default RecommendationComponent; 