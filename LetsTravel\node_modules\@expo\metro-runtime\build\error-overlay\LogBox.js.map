{"version": 3, "file": "LogBox.js", "sourceRoot": "", "sources": ["../../src/error-overlay/LogBox.ts"], "names": [], "mappings": ";;AAgBA,MAAM,MAAM,GAAY;IACtB,OAAO;QACL,cAAc;IAChB,CAAC;IAED,SAAS;QACP,cAAc;IAChB,CAAC;IAED,WAAW;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IAED,UAAU,CAAC,QAAkC;QAC3C,cAAc;IAChB,CAAC;IAED,aAAa,CAAC,KAAe;QAC3B,cAAc;IAChB,CAAC;IAED,YAAY;QACV,cAAc;IAChB,CAAC;IAED,MAAM,CAAC,GAAY;QACjB,cAAc;IAChB,CAAC;IAED,YAAY,CAAC,EAAyB;QACpC,cAAc;IAChB,CAAC;CACF,CAAC;AAEF,kBAAe,MAAM,CAAC", "sourcesContent": ["import { IgnorePattern, LogData } from './Data/LogBoxData';\nimport { ExtendedExceptionData } from './Data/parseLogBoxLog';\n\nexport { LogData, ExtendedExceptionData, IgnorePattern };\n\ninterface ILogBox {\n  install(): void;\n  uninstall(): void;\n  isInstalled(): boolean;\n  ignoreLogs(patterns: readonly IgnorePattern[]): void;\n  ignoreAllLogs(ignore?: boolean): void;\n  clearAllLogs(): void;\n  addLog(log: LogData): void;\n  addException(error: ExtendedExceptionData): void;\n}\n\nconst LogBox: ILogBox = {\n  install(): void {\n    // Do nothing.\n  },\n\n  uninstall(): void {\n    // Do nothing.\n  },\n\n  isInstalled(): boolean {\n    return false;\n  },\n\n  ignoreLogs(patterns: readonly IgnorePattern[]): void {\n    // Do nothing.\n  },\n\n  ignoreAllLogs(value?: boolean): void {\n    // Do nothing.\n  },\n\n  clearAllLogs(): void {\n    // Do nothing.\n  },\n\n  addLog(log: LogData): void {\n    // Do nothing.\n  },\n\n  addException(ex: ExtendedExceptionData): void {\n    // Do nothing.\n  },\n};\n\nexport default LogBox;\n"]}