// 🎨 Trekmate 4.0 - API Mock 服务处理器
// Day 1: 配置 API Mock 服务（MSW），定义核心 API 的 Mock 数据

import { rest } from 'msw';
import { SearchResult } from '../services/personalPlanner/SmartSearchService';
import { AIRecommendation } from '../services/personalPlanner/AIRecommendationService';

// 🎯 Mock 搜索数据
const mockSearchResults: SearchResult[] = [
  {
    id: 'attraction-1',
    name: 'Tokyo Tower',
    type: 'attraction',
    location: {
      name: 'Minato City, Tokyo',
      address: '4 Chome-2-8 Shibakoen, Minato City, Tokyo 105-0011, Japan',
      coordinates: { latitude: 35.6586, longitude: 139.7454 },
    },
    rating: 4.2,
    priceLevel: 2,
    description: 'Iconic red and white tower offering panoramic city views',
    images: ['https://example.com/tokyo-tower.jpg'],
    openingHours: '9:00 AM - 11:00 PM',
    website: 'https://www.tokyotower.co.jp/',
    phone: '+81-3-3433-5111',
    tags: ['landmark', 'observation deck', 'city views', 'iconic'],
  },
  {
    id: 'restaurant-1',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    type: 'restaurant',
    location: {
      name: 'Ginza, Tokyo',
      address: 'Tsukamoto Sogyo Building B1F, 4-2-15 Ginza, Chuo City, Tokyo',
      coordinates: { latitude: 35.6762, longitude: 139.7653 },
    },
    rating: 4.8,
    priceLevel: 5,
    description: 'World-renowned sushi restaurant by master chef Jiro Ono',
    images: ['https://example.com/jiro-sushi.jpg'],
    openingHours: '11:30 AM - 2:00 PM, 5:00 PM - 8:30 PM',
    tags: ['sushi', 'fine dining', 'michelin star', 'traditional'],
  },
  {
    id: 'hotel-1',
    name: 'The Ritz-Carlton Tokyo',
    type: 'accommodation',
    location: {
      name: 'Roppongi, Tokyo',
      address: '9-7-1 Akasaka, Minato City, Tokyo 107-6245, Japan',
      coordinates: { latitude: 35.6627, longitude: 139.7279 },
    },
    rating: 4.6,
    priceLevel: 5,
    description: 'Luxury hotel with stunning city views and world-class service',
    images: ['https://example.com/ritz-carlton.jpg'],
    openingHours: '24 hours',
    website: 'https://www.ritzcarlton.com/tokyo',
    phone: '+81-3-3423-8000',
    tags: ['luxury', 'hotel', 'city views', 'spa'],
  },
  {
    id: 'attraction-2',
    name: 'Senso-ji Temple',
    type: 'attraction',
    location: {
      name: 'Asakusa, Tokyo',
      address: '2-3-1 Asakusa, Taito City, Tokyo 111-0032, Japan',
      coordinates: { latitude: 35.7148, longitude: 139.7967 },
    },
    rating: 4.4,
    priceLevel: 1,
    description: 'Ancient Buddhist temple and Tokyo\'s oldest temple',
    images: ['https://example.com/sensoji.jpg'],
    openingHours: '6:00 AM - 5:00 PM',
    tags: ['temple', 'buddhist', 'historic', 'cultural', 'traditional'],
  },
  {
    id: 'restaurant-2',
    name: 'Ichiran Ramen',
    type: 'restaurant',
    location: {
      name: 'Shibuya, Tokyo',
      address: '1-22-7 Jinnan, Shibuya City, Tokyo 150-0041, Japan',
      coordinates: { latitude: 35.6627, longitude: 139.7006 },
    },
    rating: 4.1,
    priceLevel: 2,
    description: 'Famous tonkotsu ramen chain with individual booth seating',
    images: ['https://example.com/ichiran.jpg'],
    openingHours: '24 hours',
    website: 'https://ichiran.com/',
    tags: ['ramen', 'casual dining', 'tonkotsu', 'chain'],
  },
];

// 🎯 Mock AI 推荐数据
const mockAIRecommendations: AIRecommendation[] = [
  {
    id: 'ai-rec-1',
    type: 'activity',
    title: '建议添加文化体验活动',
    description: '在下午2-4点的空隙中，建议参观附近的传统茶道体验',
    confidence: 0.85,
    priority: 'high',
    suggestedAction: {
      type: 'add',
      data: {
        type: 'attraction',
        name: '茶道体验',
        duration: 120,
        startTime: '14:00',
        endTime: '16:00',
      },
    },
    reasoning: [
      '您的偏好显示对文化体验感兴趣',
      '当前时间段有2小时空隙',
      '附近有高评分的茶道体验场所',
    ],
    estimatedImpact: {
      time: 120,
      satisfaction: 4,
    },
    tags: ['culture', 'experience', 'traditional'],
    createdAt: new Date(),
  },
  {
    id: 'ai-rec-2',
    type: 'optimization',
    title: '活动顺序优化建议',
    description: '建议调整景点参观顺序，可节省30分钟交通时间',
    confidence: 0.92,
    priority: 'medium',
    suggestedAction: {
      type: 'reorder',
      data: {
        newOrder: ['attraction-2', 'attraction-1', 'restaurant-1'],
      },
    },
    reasoning: [
      '按地理位置优化路线',
      '避免重复往返',
      '考虑营业时间限制',
    ],
    estimatedImpact: {
      time: -30,
      satisfaction: 2,
    },
    tags: ['optimization', 'route', 'time-saving'],
    createdAt: new Date(),
  },
];

// 🎯 API 处理器
export const handlers = [
  // 智能搜索 API
  rest.get('/api/search', (req, res, ctx) => {
    const query = req.url.searchParams.get('q') || '';
    const type = req.url.searchParams.get('type');
    const limit = parseInt(req.url.searchParams.get('limit') || '10');

    // 模拟搜索延迟
    const delay = Math.random() * 500 + 100; // 100-600ms

    let filteredResults = mockSearchResults.filter(result => 
              (result.name || '').toLowerCase().includes((query || '').toLowerCase()) ||
        (result.location.name || '').toLowerCase().includes((query || '').toLowerCase()) ||
        result.tags?.some(tag => (tag || '').toLowerCase().includes((query || '').toLowerCase()))
    );

    // 按类型过滤
    if (type && type !== 'all') {
      filteredResults = filteredResults.filter(result => result.type === type);
    }

    // 限制结果数量
    filteredResults = filteredResults.slice(0, limit);

    // 模拟评分排序
    filteredResults.sort((a, b) => (b.rating || 0) - (a.rating || 0));

    return res(
      ctx.delay(delay),
      ctx.status(200),
      ctx.json({
        success: true,
        data: filteredResults,
        total: filteredResults.length,
        query,
        timestamp: new Date().toISOString(),
      })
    );
  }),

  // 搜索建议 API
  rest.get('/api/search/suggestions', (req, res, ctx) => {
    const query = req.url.searchParams.get('q') || '';
    const limit = parseInt(req.url.searchParams.get('limit') || '5');

    if (query.length < 2) {
      return res(
        ctx.status(200),
        ctx.json({
          success: true,
          data: ['Tokyo Tower', 'Senso-ji Temple', 'Shibuya Crossing'],
          query,
        })
      );
    }

    const suggestions = mockSearchResults
      .filter(result => 
        (result.name || '').toLowerCase().includes((query || '').toLowerCase())
      )
      .map(result => result.name)
      .slice(0, limit);

    return res(
      ctx.delay(200),
      ctx.status(200),
      ctx.json({
        success: true,
        data: suggestions,
        query,
      })
    );
  }),

  // AI 推荐 API
  rest.post('/api/ai/recommendations', (req, res, ctx) => {
    const delay = Math.random() * 1000 + 500; // 500-1500ms

    return res(
      ctx.delay(delay),
      ctx.status(200),
      ctx.json({
        success: true,
        data: mockAIRecommendations,
        total: mockAIRecommendations.length,
        timestamp: new Date().toISOString(),
      })
    );
  }),

  // 地点详情 API
  rest.get('/api/places/:id', (req, res, ctx) => {
    const { id } = req.params;
    const place = mockSearchResults.find(result => result.id === id);

    if (!place) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          error: 'Place not found',
        })
      );
    }

    return res(
      ctx.delay(300),
      ctx.status(200),
      ctx.json({
        success: true,
        data: place,
      })
    );
  }),

  // 错误处理示例
  rest.get('/api/search/error', (req, res, ctx) => {
    return res(
      ctx.delay(1000),
      ctx.status(500),
      ctx.json({
        success: false,
        error: 'Internal server error',
        message: 'Search service temporarily unavailable',
      })
    );
  }),

  // 网络超时示例
  rest.get('/api/search/timeout', (req, res, ctx) => {
    return res(
      ctx.delay(10000), // 10秒超时
      ctx.status(200),
      ctx.json({
        success: true,
        data: [],
      })
    );
  }),
];

export default handlers;
