{"version": 3, "file": "sort-routes.js", "sourceRoot": "", "sources": ["../../src/global-state/sort-routes.ts"], "names": [], "mappings": ";;;AACA,oCAAsC;AAEtC,SAAgB,eAAe;IAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;KACpC;IAED,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kBAAU,CAAC,CAAC;AACrF,CAAC;AAND,0CAMC", "sourcesContent": ["import type { RouterStore } from './router-store';\nimport { sortRoutes } from '../Route';\n\nexport function getSortedRoutes(this: RouterStore) {\n  if (!this.routeNode) {\n    throw new Error('No routes found');\n  }\n\n  return this.routeNode.children.filter((route) => !route.internal).sort(sortRoutes);\n}\n"]}