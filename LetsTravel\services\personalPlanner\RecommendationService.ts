import { Activity } from '../../app/tabs/tripplanning/components/PersonalPlanner/PersonalPlannerHome';

// Google Places API interfaces
export interface PlaceDetails {
  placeId: string;
  name: string;
  address: string;
  location: {
    lat: number;
    lng: number;
  };
  rating?: number;
  priceLevel?: number;
  photos?: string[];
  types: string[];
  openingHours?: {
    openNow: boolean;
    weekdayText: string[];
  };
  website?: string;
  phoneNumber?: string;
  reviews?: PlaceReview[];
}

export interface PlaceReview {
  author: string;
  rating: number;
  text: string;
  time: string;
}

export interface RecommendationFilter {
  type?: 'restaurant' | 'attraction' | 'accommodation' | 'shopping' | 'entertainment';
  priceRange?: 'budget' | 'moderate' | 'expensive' | 'luxury';
  rating?: number;
  distance?: number; // in km
  openNow?: boolean;
  budget?: number;
  preferences?: string[];
}

export interface RecommendationResult {
  place: PlaceDetails;
  relevanceScore: number;
  estimatedCost?: number;
  estimatedDuration?: number;
  suggestedTimeSlots?: string[];
  reasonForRecommendation: string;
}

export class RecommendationService {
  private static readonly GOOGLE_PLACES_API_KEY = process.env.GOOGLE_PLACES_API_KEY || '';
  private static readonly GOOGLE_PLACES_BASE_URL = 'https://maps.googleapis.com/maps/api/place';
  
  // 主要推荐方法
  static async getRecommendations(
    destination: string,
    userLocation?: { lat: number; lng: number },
    filters?: RecommendationFilter
  ): Promise<RecommendationResult[]> {
    try {
      return this.getMockRecommendations(destination, filters);
    } catch (error) {
      console.error('Error getting recommendations:', error);
      return [];
    }
  }

  // 获取目的地坐标
  private static async getDestinationCoordinates(destination: string): Promise<{ lat: number; lng: number } | null> {
    if (!this.GOOGLE_PLACES_API_KEY) {
      // 返回默认坐标（北京）
      return { lat: 39.9042, lng: 116.4074 };
    }

    try {
      const response = await fetch(
        `${this.GOOGLE_PLACES_BASE_URL}/textsearch/json?query=${encodeURIComponent(destination)}&key=${this.GOOGLE_PLACES_API_KEY}`
      );

      if (!response.ok) {
        throw new Error(`Geocoding API error: ${response.status}`);
      }

      const data = await response.json();
      if (data.results && data.results.length > 0) {
        const location = data.results[0].geometry.location;
        return { lat: location.lat, lng: location.lng };
      }

      return null;
    } catch (error) {
      console.error('Error getting destination coordinates:', error);
      return null;
    }
  }

  // 搜索地点
  private static async searchPlaces(
    location: { lat: number; lng: number },
    filters?: RecommendationFilter
  ): Promise<PlaceDetails[]> {
    if (!this.GOOGLE_PLACES_API_KEY) {
      throw new Error('Google Places API key not configured');
    }

    try {
      const radius = (filters?.distance || 10) * 1000; // 转换为米
      const type = this.getGooglePlaceType(filters?.type);
      
      let url = `${this.GOOGLE_PLACES_BASE_URL}/nearbysearch/json?location=${location.lat},${location.lng}&radius=${radius}&key=${this.GOOGLE_PLACES_API_KEY}`;
      
      if (type) {
        url += `&type=${type}`;
      }

      if (filters?.priceRange) {
        const priceLevel = this.getPriceLevel(filters.priceRange);
        url += `&minprice=${priceLevel}&maxprice=${priceLevel}`;
      }

      if (filters?.rating) {
        // Google Places API 不直接支持最低评分筛选，需要在结果中过滤
      }

      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Google Places API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.results) {
        return [];
      }

      // 处理结果并获取详细信息
      const places: PlaceDetails[] = [];
      for (const result of data.results.slice(0, 20)) { // 限制结果数量
        const placeDetails = await this.getPlaceDetails(result.place_id);
        if (placeDetails) {
          places.push(placeDetails);
        }
      }

      return places;
    } catch (error) {
      console.error('Error searching places:', error);
      throw error;
    }
  }

  // 获取地点详细信息
  private static async getPlaceDetails(placeId: string): Promise<PlaceDetails | null> {
    if (!this.GOOGLE_PLACES_API_KEY) {
      return null;
    }

    try {
      const response = await fetch(
        `${this.GOOGLE_PLACES_BASE_URL}/details/json?place_id=${placeId}&fields=name,formatted_address,geometry,rating,price_level,photos,types,opening_hours,website,formatted_phone_number,reviews&key=${this.GOOGLE_PLACES_API_KEY}`
      );

      if (!response.ok) {
        throw new Error(`Place details API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.result) {
        return null;
      }

      const result = data.result;
      return {
        placeId,
        name: result.name,
        address: result.formatted_address,
        location: {
          lat: result.geometry.location.lat,
          lng: result.geometry.location.lng
        },
        rating: result.rating,
        priceLevel: result.price_level,
        photos: result.photos?.map((photo: any) => 
          `${this.GOOGLE_PLACES_BASE_URL}/photo?maxwidth=400&photoreference=${photo.photo_reference}&key=${this.GOOGLE_PLACES_API_KEY}`
        ),
        types: result.types,
        openingHours: result.opening_hours ? {
          openNow: result.opening_hours.open_now,
          weekdayText: result.opening_hours.weekday_text
        } : undefined,
        website: result.website,
        phoneNumber: result.formatted_phone_number,
        reviews: result.reviews?.slice(0, 3).map((review: any) => ({
          author: review.author_name,
          rating: review.rating,
          text: review.text,
          time: review.relative_time_description
        }))
      };
    } catch (error) {
      console.error('Error getting place details:', error);
      return null;
    }
  }

  // 计算推荐分数
  private static calculateRecommendationScore(
    place: PlaceDetails,
    filters?: RecommendationFilter
  ): RecommendationResult {
    let score = 0;
    let reasonParts: string[] = [];

    // 基础评分
    if (place.rating) {
      score += place.rating * 10; // 评分权重
      if (place.rating >= 4.0) {
        reasonParts.push('高评分');
      }
    }

    // 类型匹配
    if (filters?.type && place.types.includes(this.getGooglePlaceType(filters.type) || '')) {
      score += 20;
      reasonParts.push('类型匹配');
    }

    // 价格匹配
    if (filters?.priceRange && place.priceLevel) {
      const expectedPriceLevel = this.getPriceLevel(filters.priceRange);
      if (place.priceLevel === expectedPriceLevel) {
        score += 15;
        reasonParts.push('价格合适');
      }
    }

    // 营业状态
    if (place.openingHours?.openNow) {
      score += 10;
      reasonParts.push('现在营业');
    }

    // 有照片加分
    if (place.photos && place.photos.length > 0) {
      score += 5;
    }

    // 有网站加分
    if (place.website) {
      score += 5;
    }

    // 评论数量（通过reviews数组长度估算）
    if (place.reviews && place.reviews.length > 0) {
      score += 5;
      reasonParts.push('有用户评价');
    }

    return {
      place,
      relevanceScore: score,
      estimatedCost: this.estimateCost(place, filters),
      estimatedDuration: this.estimateDuration(place),
      suggestedTimeSlots: this.suggestTimeSlots(place),
      reasonForRecommendation: reasonParts.length > 0 ? reasonParts.join('、') : '推荐地点'
    };
  }

  // 估算费用
  private static estimateCost(place: PlaceDetails, filters?: RecommendationFilter): number | undefined {
    if (!place.priceLevel) return undefined;

    const baseCosts = {
      1: 100,   // 便宜
      2: 300,   // 中等
      3: 600,   // 较贵
      4: 1200   // 昂贵
    };

    return baseCosts[place.priceLevel as keyof typeof baseCosts] || undefined;
  }

  // 估算游览时间
  private static estimateDuration(place: PlaceDetails): number {
    const types = place.types;
    
    if (types.includes('museum')) return 120; // 博物馆 2小时
    if (types.includes('amusement_park')) return 480; // 游乐园 8小时
    if (types.includes('zoo')) return 180; // 动物园 3小时
    if (types.includes('restaurant')) return 90; // 餐厅 1.5小时
    if (types.includes('shopping_mall')) return 120; // 商场 2小时
    if (types.includes('tourist_attraction')) return 90; // 景点 1.5小时
    
    return 60; // 默认 1小时
  }

  // 建议时间段
  private static suggestTimeSlots(place: PlaceDetails): string[] {
    const types = place.types;
    
    if (types.includes('restaurant')) {
      if (types.includes('breakfast') || place.name.includes('早餐')) {
        return ['07:00-10:00'];
      }
      if (types.includes('lunch') || place.name.includes('午餐')) {
        return ['11:30-14:00'];
      }
      return ['18:00-21:00']; // 晚餐
    }
    
    if (types.includes('bar') || types.includes('night_club')) {
      return ['20:00-02:00'];
    }
    
    if (types.includes('museum') || types.includes('tourist_attraction')) {
      return ['09:00-17:00'];
    }
    
    return ['10:00-18:00']; // 默认时间段
  }

  // 转换为Google Places API类型
  private static getGooglePlaceType(type?: string): string | null {
    const typeMapping = {
      'restaurant': 'restaurant',
      'attraction': 'tourist_attraction',
      'accommodation': 'lodging',
      'shopping': 'shopping_mall',
      'entertainment': 'amusement_park'
    };

    return type ? typeMapping[type as keyof typeof typeMapping] || null : null;
  }

  // 获取价格等级
  private static getPriceLevel(priceRange: string): number {
    const priceLevels = {
      'budget': 1,
      'moderate': 2,
      'expensive': 3,
      'luxury': 4
    };

    return priceLevels[priceRange as keyof typeof priceLevels] || 2;
  }

  // 将推荐转换为Activity
  static recommendationToActivity(recommendation: RecommendationResult): Activity {
    const startTime = new Date();
    const endTime = new Date(startTime.getTime() + (recommendation.estimatedDuration || 60) * 60 * 1000);
    
    return {
      id: `rec-${recommendation.place.placeId}-${Date.now()}`,
      type: this.getActivityType(recommendation.place.types),
      title: recommendation.place.name,
      description: `${recommendation.reasonForRecommendation} | 评分: ${recommendation.place.rating || 'N/A'}`,
      startTime,
      endTime,
      location: recommendation.place.address,
      cost: recommendation.estimatedCost,
      currency: 'CNY',
      bookingUrl: recommendation.place.website,
      notes: `推荐原因: ${recommendation.reasonForRecommendation}\n电话: ${recommendation.place.phoneNumber || 'N/A'}`
    };
  }

  // 获取活动类型
  private static getActivityType(types: string[]): Activity['type'] {
    if (types.includes('restaurant') || types.includes('food')) return 'restaurant';
    if (types.includes('lodging')) return 'accommodation';
    if (types.includes('tourist_attraction') || types.includes('museum')) return 'attraction';
    if (types.includes('transit_station')) return 'transport';
    return 'custom';
  }

  // 模拟推荐数据
  static getMockRecommendations(destination: string, filters?: RecommendationFilter): RecommendationResult[] {
    const mockPlaces: PlaceDetails[] = [
      {
        placeId: 'mock-1',
        name: `${destination}博物馆`,
        address: `${destination}市中心`,
        location: { lat: 39.9042, lng: 116.4074 },
        rating: 4.5,
        priceLevel: 2,
        types: ['museum', 'tourist_attraction'],
        openingHours: {
          openNow: true,
          weekdayText: ['周一: 09:00-17:00', '周二: 09:00-17:00']
        }
      },
      {
        placeId: 'mock-2',
        name: `${destination}特色餐厅`,
        address: `${destination}美食街`,
        location: { lat: 39.9052, lng: 116.4084 },
        rating: 4.2,
        priceLevel: 3,
        types: ['restaurant', 'food'],
        openingHours: {
          openNow: true,
          weekdayText: ['每日: 11:00-22:00']
        }
      },
      {
        placeId: 'mock-3',
        name: `${destination}古城景区`,
        address: `${destination}古城区`,
        location: { lat: 39.9032, lng: 116.4064 },
        rating: 4.7,
        priceLevel: 1,
        types: ['tourist_attraction', 'establishment'],
        openingHours: {
          openNow: true,
          weekdayText: ['每日: 08:00-18:00']
        }
      }
    ];

    return mockPlaces.map(place => ({
      place,
      relevanceScore: (place.rating || 0) * 10,
      estimatedCost: place.priceLevel ? place.priceLevel * 200 : undefined,
      estimatedDuration: 120,
      suggestedTimeSlots: ['09:00-17:00'],
      reasonForRecommendation: '热门推荐'
    }));
  }

  // 基于偏好的智能推荐
  static async getPersonalizedRecommendations(
    destination: string,
    preferences: string[],
    userHistory?: Activity[]
  ): Promise<RecommendationResult[]> {
    try {
      // 分析用户偏好
      const filters = this.analyzePreferences(preferences);
      
      // 获取基础推荐
      const recommendations = await this.getRecommendations(destination, undefined, filters);
      
      // 基于历史记录调整推荐
      if (userHistory && userHistory.length > 0) {
        return this.adjustRecommendationsBasedOnHistory(recommendations, userHistory);
      }
      
      return recommendations;
    } catch (error) {
      console.error('Error getting personalized recommendations:', error);
      return this.getMockRecommendations(destination);
    }
  }

  // 分析用户偏好
  private static analyzePreferences(preferences: string[]): RecommendationFilter {
    const filter: RecommendationFilter = {};
    
    preferences.forEach(pref => {
              const lowerPref = (pref || '').toLowerCase();
      
      if (lowerPref.includes('美食') || lowerPref.includes('餐厅')) {
        filter.type = 'restaurant';
      }
      if (lowerPref.includes('景点') || lowerPref.includes('观光')) {
        filter.type = 'attraction';
      }
      if (lowerPref.includes('购物')) {
        filter.type = 'shopping';
      }
      if (lowerPref.includes('便宜') || lowerPref.includes('经济')) {
        filter.priceRange = 'budget';
      }
      if (lowerPref.includes('豪华') || lowerPref.includes('高端')) {
        filter.priceRange = 'luxury';
      }
      if (lowerPref.includes('高评分')) {
        filter.rating = 4.0;
      }
    });
    
    return filter;
  }

  // 基于历史记录调整推荐
  private static adjustRecommendationsBasedOnHistory(
    recommendations: RecommendationResult[],
    history: Activity[]
  ): RecommendationResult[] {
    // 分析用户历史偏好
    const historyTypes = history.map(activity => activity.type);
    const mostFrequentType = this.getMostFrequent(historyTypes);
    
    // 调整推荐分数
    return recommendations.map(rec => {
      if (this.getActivityType(rec.place.types) === mostFrequentType) {
        rec.relevanceScore += 10; // 偏好类型加分
        rec.reasonForRecommendation += '、符合您的偏好';
      }
      return rec;
    }).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  // 获取最频繁的类型
  private static getMostFrequent<T>(arr: T[]): T | undefined {
    const frequency: { [key: string]: number } = {};
    let maxCount = 0;
    let mostFrequent: T | undefined;
    
    arr.forEach(item => {
      const key = String(item);
      frequency[key] = (frequency[key] || 0) + 1;
      if (frequency[key] > maxCount) {
        maxCount = frequency[key];
        mostFrequent = item;
      }
    });
    
    return mostFrequent;
  }
}

export default RecommendationService; 