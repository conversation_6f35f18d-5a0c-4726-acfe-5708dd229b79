import * as React from 'react';
import {
  Platform,
  Alert,
  Linking,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
} from 'react-native';
import {
  <PERSON>ton,
  Card,
  Text,
  XStack,
  YStack,
  Input,
  ScrollView,
  Spinner,
  View,
} from '@chakra-ui/react';
import { Ionicons, FontAwesome5 } from '@expo/vector-icons';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MarkdownDisplay from 'react-native-markdown-display';

// Import the travel planner service functions
import {
  callGoogleSearchApi,
  callLlama4Api,
  generateTripPlanPrompt,
  generateAITripPlanPrompt,
  GoogleSearchResponseItem,
  callGoogleImageSearchApi,
  callOpenWeather<PERSON><PERSON>,
  WeatherSummary,
} from '../../services/travelPlannerService';

// Import location service and config
import { LocationInfo, getCurrentLocation } from '../../services/locationService';
import { CURRENCY_UNITS, DEFAULT_COUNTRY } from '../../constants/Config';
import { CurrencyCode } from '../../services/types';

// Import Personal Planner components
import { PersonalPlannerHome } from '../../components/PersonalPlanner/PersonalPlannerHome_v4';
import { TripBasicInfo } from '../../components/types/PersonalPlannerTypes';

// Import design tokens and UI components
import { Colors, Spacing, BorderRadius, Typography } from '../../constants/DesignTokens';
// TamaguiButton removed - using Chakra UI Button
import { useToast } from '../../ui/Toast';

// Define PlanningMode type - Updated for Personal Planner
type PlanningMode = 'personal' | 'ai';

// Define Season type
type Season = 'Spring' | 'Summer' | 'Autumn' | 'Winter' | 'Any' | '';

const USER_LOCATION_INFO_KEY = '@userLocationInfo'; // AsyncStorage key

// --- Helper function to extract keywords from [IMAGE_FOR=...] markers --- //
const extractImageKeywords = (text: string): string[] => {
  const keywords: string[] = [];
  const regex = /\[IMAGE_FOR=(.*?)\]/g; // Regex to find [IMAGE_FOR=...] markers
  
  // 提取包含"Day"的行，用于识别每一天
  const dayMatches = text.match(/## Day \d+/g) || [];
  const totalDays = dayMatches.length;
  
  console.log('extractImageKeywords: totalDays =', totalDays);
  console.log('Text contains [IMAGE_FOR] markers:', text.includes('[IMAGE_FOR='));
  
  // 没有明确的天数划分时，仍然提取关键词
  if (totalDays === 0) {
    let match;
    while ((match = regex.exec(text)) !== null) { // 移除最大图片数量的限制
      const keyword = match[1].trim();
      if (keyword && !keywords.includes(keyword)) { // 避免重复
        keywords.push(keyword);
      }
    }
  } 
  // 如果有天数划分，确保每天至少有一张图片
  else {
    // 将文本按天分割
    const dayTexts: string[] = [];
    for (let i = 0; i < totalDays; i++) {
      const currentDayPattern = dayMatches[i];
      const nextDayPattern = i < totalDays - 1 ? dayMatches[i + 1] : null;
      
      const startIdx = text.indexOf(currentDayPattern);
      const endIdx = nextDayPattern ? text.indexOf(nextDayPattern) : text.length;
      
      if (startIdx >= 0 && endIdx > startIdx) {
        dayTexts.push(text.substring(startIdx, endIdx));
      }
    }
    
    // 从每天的文本中提取关键词
    dayTexts.forEach((dayText, index) => {
      console.log(`Day ${index + 1} text contains [IMAGE_FOR] markers:`, dayText.includes('[IMAGE_FOR='));
      const dayKeywords: string[] = [];
      let match;
      const dayRegex = new RegExp(/\[IMAGE_FOR=(.*?)\]/g);
      
      while ((match = dayRegex.exec(dayText)) !== null) {
        const keyword = match[1].trim();
        if (keyword && !dayKeywords.includes(keyword)) {
          dayKeywords.push(keyword);
        }
      }
      
      // 确保每天至少添加一个关键词到总列表
      if (dayKeywords.length > 0) {
        console.log(`Day ${index + 1} keywords:`, dayKeywords);
        dayKeywords.forEach(keyword => {
          if (!keywords.includes(keyword)) {
            keywords.push(keyword);
          }
        });
      } else {
        console.log(`Day ${index + 1} has no [IMAGE_FOR] markers`);
      }
    });
  }
  
  console.log('Final extracted image keywords from [IMAGE_FOR]:', keywords);
  return keywords;
};

// 在extractImageKeywords函数后添加新的处理导航标记的函数
const extractNavigationMarkers = (text: string): Map<string, string> => {
  const navMap = new Map<string, string>();
  // 修改正则表达式以同时支持[NAV=和[nav=格式（不区分大小写）
  const regex = /\[(NAV|nav)=(.*?)\]/g;
  let match;
  
  while ((match = regex.exec(text)) !== null) {
    const locationData = match[2].trim();
    if (locationData) {
      const encodedLocation = encodeURIComponent(locationData);
      const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;
      navMap.set(match[0], googleMapsUrl);
    }
  }
  
  console.log('Extracted navigation markers:', Array.from(navMap.keys()));
  return navMap;
};

// 在extractNavigationMarkers函数后添加URL编码工具函数
const safeEncodeImageUrl = (url: string): string => {
  if (!url) return '';
  
  try {
    // 先尝试解码URL，避免双重编码问题
    let decodedUrl = url;
    try {
      // 尝试解码URL，如果已经是解码状态，不会有影响
      decodedUrl = decodeURIComponent(url);
    } catch (e) {
      // 如果解码失败，保持原URL
      console.log('URL已经是解码状态或包含特殊字符:', url);
    }
    
    // 现在安全地编码URL，保留关键字符
    return encodeURIComponent(decodedUrl)
      .replace(/%2F/g, '/') // 保留斜杠
      .replace(/%3A/g, ':') // 保留冒号
      .replace(/%2E/g, '.') // 保留点号
      .replace(/%3F/g, '?') // 保留问号
      .replace(/%3D/g, '=') // 保留等号
      .replace(/%26/g, '&'); // 保留&符号
  } catch (error) {
    console.error('Error encoding URL:', error);
    // 如果整个编码过程失败，返回原始URL
    return url;
  }
};

// 在组件的return上方添加Markdown样式
const markdownStyles = {
  // 基础样式
  body: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    padding: 5, // 添加整体内边距
  },
  heading1: {
    fontSize: 26, // 增大标题
    fontWeight: 'bold',
    marginTop: 24,
    marginBottom: 16,
    color: '#1a5276', // 深蓝色
    borderBottomWidth: 2,
    borderBottomColor: '#D9E2EC',
    paddingBottom: 8,
    textAlign: 'center', // 居中主标题
  },
  heading2: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 12,
    color: '#2874A6', // 中蓝色
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8F0',
    paddingBottom: 6,
    backgroundColor: '#f7fbfc', // 添加轻微背景色
    padding: 8,
    borderRadius: 5,
  },
  heading3: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 18,
    marginBottom: 10,
    color: '#2E86C1', // 浅蓝色
    paddingBottom: 4,
    paddingLeft: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#3498DB',
  },
  heading4: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    color: '#3498DB', // 亮蓝色
    paddingLeft: 8,
  },
  paragraph: {
    marginBottom: 14, // 增加段落间距
    lineHeight: 24,
    paddingHorizontal: 2, // 添加水平内边距
  },
  // 列表样式
  bullet_list: {
    marginLeft: 12,
    marginBottom: 16,
    backgroundColor: '#f8f9fa', // 添加背景色
    paddingVertical: 8,
    paddingRight: 8,
    borderRadius: 6,
  },
  ordered_list: {
    marginLeft: 12,
    marginBottom: 16,
    backgroundColor: '#f8f9fa',
    paddingVertical: 8,
    paddingRight: 8,
    borderRadius: 6,
  },
  list_item: {
    marginBottom: 10, // 增加列表项间距
    lineHeight: 22,
    paddingLeft: 4,
  },
  // 图片样式
  image: {
    width: '100%',
    height: 210,
    alignSelf: 'center',
    marginVertical: 16,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  // 表格样式
  table: {
    borderWidth: 1,
    borderColor: '#D9E2EC',
    borderRadius: 6,
    marginVertical: 14,
    backgroundColor: '#FAFBFC',
    overflow: 'hidden',
    shadowColor: "#000", // 添加阴影
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  thead: {
    backgroundColor: '#E1F5FE',
    borderBottomWidth: 1,
    borderBottomColor: '#D9E2EC',
  },
  th: {
    padding: 12,
    fontWeight: 'bold',
    color: '#0A2463',
    textAlign: 'center',
  },
  tr: {
    borderBottomWidth: 1, 
    borderBottomColor: '#E1E8F0',
  },
  td: {
    padding: 10,
    borderRightWidth: 1,
    borderRightColor: '#E1E8F0',
  },
  // 链接样式
  link: {
    color: '#0277BD',
    textDecorationLine: 'none',
    fontWeight: 'bold',
  },
  // 强调样式
  strong: {
    fontWeight: 'bold',
    color: '#0A2463',
  },
  em: {
    fontStyle: 'italic',
    color: '#333',
  },
  // 引用样式
  blockquote: {
    borderLeftWidth: 4,
    borderLeftColor: '#0277BD',
    paddingLeft: 12,
    marginLeft: 10,
    marginRight: 10,
    marginVertical: 12,
    backgroundColor: '#F5F7FA',
    paddingVertical: 10,
    paddingRight: 10,
    borderRadius: 4,
  },
  // 代码样式
  code_inline: {
    backgroundColor: '#F5F7FA',
    color: '#E91E63',
    borderRadius: 4,
    paddingHorizontal: 5,
    paddingVertical: 2,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  code_block: {
    backgroundColor: '#F5F7FA',
    color: '#333',
    borderRadius: 6,
    padding: 12,
    marginVertical: 12,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  fence: {
    backgroundColor: '#F5F7FA',
    color: '#333',
    borderRadius: 6,
    padding: 12,
    marginVertical: 12,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  // 水平线样式
  hr: {
    backgroundColor: '#9eccf0', // 更亮的颜色
    height: 2,
    marginVertical: 16,
  },
  // 导航按钮样式
  navButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 10,
    marginVertical: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  navButtonInner: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  navButtonIcon: {
    marginRight: 5,
  },
  navButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  navLocationName: {
    flex: 1,
    fontSize: 14,
    color: '#444',
    fontWeight: '500',
  },
  transportContainer: {
    backgroundColor: '#f0f7ff',
    borderLeftWidth: 3,
    borderLeftColor: '#4fc3f7',
    paddingVertical: 10,
    paddingHorizontal: 14,
    marginVertical: 10,
    borderRadius: 6,
    shadowColor: "#000", // 添加轻微阴影
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  seasonPickerContainer: {
    flex: 1,
  },
  seasonPickerWrapper: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden', // 确保圆角效果应用
    backgroundColor: '#f7f7f7',
  },
  seasonPicker: {
    height: 50,
    width: '100%',
    // Android和iOS的样式微调
    paddingLeft: Platform.OS === 'android' ? 10 : 0,
    color: '#333',
  },
  seasonPickerItemIOS: {
    height: 150, // 调整iOS选择器项的高度
    fontSize: 16,
  },
  androidPickerTrigger: {
    padding: 10,
    width: '100%',
  },
  androidSelectedValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  androidSelectedValue: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
  },
  androidPickerPlaceholder: {
    color: '#aaa',
  },
  pickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#f7f7f7',
    paddingLeft: 40, // 为图标腾出空间
    height: 50,
  },
  pickerIconContainer: {
    position: 'absolute',
    left: 10,
    top: 10,
    zIndex: 1, // 确保图标在Picker之上
  },
  picker: {
    flex: 1,
    width: '100%',
    color: '#333',
    marginLeft: Platform.OS === 'android' ? -8 : 0,
    paddingVertical: 0,
    textAlignVertical: Platform.OS === 'android' ? 'center' : undefined,
  },
  pickerItem: {
    height: 150,
    fontSize: 16,
    textAlignVertical: 'center',
  },
  placeholderItem: {
    color: '#aaa',
    fontSize: 16,
    height: Platform.OS === 'ios' ? 150 : undefined,
    textAlignVertical: 'center',
  },
  travelersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
  },
  travelerInputGroup: {
    flex: 1,
    marginHorizontal: 4,
  },
  travelerLabel: {
    fontSize: 14,
    color: '#555',
    marginBottom: 4,
  },
  travelerCounterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    overflow: 'hidden',
  },
  travelerCounterButton: {
    backgroundColor: '#f1f1f1',
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  travelerCounterButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#555',
  },
  travelerCountInput: {
    textAlign: 'center',
    paddingVertical: 4,
    width: 30,
    fontSize: 16,
  },
  navButtonContainer: {
    marginVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  disabledOpacity: {
    opacity: 0.5,
  },
  errorText: {
    color: '#e74c3c', // Softer red
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 14,
  },
  generateButton: {
    backgroundColor: '#3498db', // Brighter blue
    paddingVertical: 16,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
  },
  buttonDisabled: {
    backgroundColor: '#a9cce3',
    opacity: 0.8,
  },
  generateButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultsCard: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e3effc', // Softer border color
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1, // Increased shadow effect
    shadowRadius: 4,
    elevation: 3,
    paddingBottom: 20, // Extra padding at the bottom
  },
  resultsHeader: {
    fontSize: 24, // Larger title
    fontWeight: 'bold',
    marginBottom: 18,
    color: '#2874A6', // Blue theme
    borderBottomWidth: 2, // Thicker bottom border
    borderBottomColor: '#bbdefb', // Light blue border
    paddingBottom: 12,
    textAlign: 'center',
  },
};

export default function TripPlanningScreen() {
  const [planningMode, setPlanningMode] = React.useState('personal');
  const [destination, setDestination] = React.useState('');
  const [startDate, setStartDate] = React.useState(null);
  const [endDate, setEndDate] = React.useState(null);
  const [showStartDatePicker, setShowStartDatePicker] = React.useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = React.useState(false);
  const [duration, setDuration] = React.useState('');
  const [season, setSeason] = React.useState('');
  const [budget, setBudget] = React.useState('');
  const [budgetCurrency, setBudgetCurrency] = React.useState('USD');
  const [preferences, setPreferences] = React.useState('');
  const [adultTravelers, setAdultTravelers] = React.useState('2');
  const [childTravelers, setChildTravelers] = React.useState('0');
  const [isLoading, setIsLoading] = React.useState(false);
  // Separate plans for each mode
  const [personalPlan, setPersonalPlan] = React.useState(null);
  const [aiPlan, setAIPlan] = React.useState(null);
  // generatedPlan will be used for display only
  const [generatedPlan, setGeneratedPlan] = React.useState(null);
  const [error, setError] = React.useState(null);
  const [locationInfo, setLocationInfo] = React.useState(null);

  // Toast hook for user feedback
  const { showToast } = useToast();

  // Fetch location and set initial currency on mount
  React.useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Try getting location from AsyncStorage first
        const storedLocationJson = await AsyncStorage.getItem(USER_LOCATION_INFO_KEY);
        let locInfo = storedLocationJson ? JSON.parse(storedLocationJson) : null;

        if (!locInfo) {
          console.log('No location in storage, fetching current location...');
          // Fallback: Get current location if not in storage
          // Corrected: Call getCurrentLocation without arguments
          const locationResult = await getCurrentLocation();
          if (locationResult && locationResult.coords) {
             // Need to get country/currency from reverse geocode, 
             // but getCurrentLocation doesn't return that directly.
             // For simplicity, we might need getUserCountry or similar logic here,
             // or just use default if reverse geocoding isn't readily available.
             // Let's use default for now to fix the immediate error.
            locInfo = {
              country: DEFAULT_COUNTRY, // Placeholder, ideally get from geocode
              currencyCode: 'USD', // Placeholder, ideally get from geocode or country map
              coords: locationResult.coords,
              // address: locationResult.address, // address not directly available
            };
            await AsyncStorage.setItem(USER_LOCATION_INFO_KEY, JSON.stringify(locInfo));
          } else {
              // Still no location, use default country
              locInfo = { country: DEFAULT_COUNTRY, currencyCode: 'USD' };
          }
        }
        setLocationInfo(locInfo);

        // Set initial budget currency based on location
        const initialCurrency = locInfo?.currencyCode || 'USD';
        // Validate if the currency exists in our config
        if (initialCurrency in CURRENCY_UNITS) {
            setBudgetCurrency(initialCurrency as CurrencyCode);
        } else {
            console.warn(`Initial currency ${initialCurrency} not in CURRENCY_UNITS, defaulting to USD.`);
            setBudgetCurrency('USD');
        }

      } catch (err) {
        console.error("Error loading initial location/currency:", err);
        let message = "Could not determine your location's currency. Defaulting to USD.";
        if (err instanceof Error) message += ` Error: ${err.message}`;
        setError(message);
        setBudgetCurrency('USD'); // Default on error
      }
    };
    loadInitialData();
  }, []);

  // After other useEffect hooks, add a new one for mode changes
  React.useEffect(() => {
    // Update the displayed plan based on current mode
    if (planningMode === 'personal') {
      setGeneratedPlan(personalPlan);
    } else {
      setGeneratedPlan(aiPlan);
    }
  }, [planningMode, personalPlan, aiPlan]);

  // --- Date Picker Logic ---
  const onStartDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowStartDatePicker(false); // Hide picker immediately
    if (event.type === 'set' && selectedDate) {
      // Basic validation: start date should not be after end date if end date exists
      if (endDate && selectedDate > endDate) {
          setError("Start date cannot be after end date.");
          setStartDate(null);
      } else {
          setStartDate(selectedDate);
          setError(null); // Clear error if valid
          // Optionally auto-show end date picker
          // if (!endDate) { setTimeout(() => setShowEndDatePicker(true), 100); }
      }
    }
  };

  const onEndDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowEndDatePicker(false); // Hide picker immediately
    if (event.type === 'set' && selectedDate) {
        // Basic validation: end date should be after start date
        if (startDate && selectedDate < startDate) {
            setError("End date cannot be before start date.");
            setEndDate(null);
        } else {
            setEndDate(selectedDate);
            setError(null); // Clear error if valid
        }
    }
  };

  const formatDate = (date: Date | null): string => {
    return date ? date.toLocaleDateString() : 'Select Date';
  };
  // --- End Date Picker Logic ---

  // 获取季节图标
  const getSeasonIcon = (seasonName: any) => {
    switch(seasonName) {
      case 'Spring':
        return <FontAwesome5 name="seedling" size={16} color="#4CAF50" />;
      case 'Summer':
        return <FontAwesome5 name="sun" size={16} color="#FF9800" />;
      case 'Autumn':
        return <FontAwesome5 name="leaf" size={16} color="#F44336" />;
      case 'Winter':
        return <FontAwesome5 name="snowflake" size={16} color="#2196F3" />;
      case 'Any':
        return <Ionicons name="calendar-outline" size={16} color="#607D8B" />;
      default:
        return null;
    }
  };

  // 获取季节文本描述
  const getSeasonDescription = (seasonName: Season): string => {
    switch(seasonName) {
      case 'Spring':
        return "(Mar-May)";
      case 'Summer':
        return "(Jun-Aug)";
      case 'Autumn':
        return "(Sep-Nov)";
      case 'Winter':
        return "(Dec-Feb)";
      case 'Any':
        return "(Flexible)";
      default:
        return "";
    }
  };

  const handleGeneratePlan = async () => {
    // --- Validation ---
    let isValid = false;
    if (planningMode === 'personal') {
      if (!destination || !startDate || !endDate || !budget || !preferences || !budgetCurrency) {
        setError('Personal Planner: Please fill in all fields.');
      } else {
        setError(null);
        isValid = true;
      }
    } else { // AI Mode validation
      if (!destination || !duration || !season || !budget || !preferences || !budgetCurrency || !adultTravelers) {
        setError('AI Mode: Please fill in all fields including the number of travelers.');
      } else {
        setError(null);
        isValid = true;
      }
    }

    if (!isValid) return;

    // --- Start Loading ---
    setIsLoading(true);
    setGeneratedPlan(null);

    // Show loading toast
    showToast({
      type: 'info',
      title: 'Generating Plan',
      message: `Creating your ${planningMode === 'personal' ? 'personal' : 'AI-powered'} travel plan...`,
    });

    // 计算旅客构成
    const travelerComposition = `${adultTravelers} adult${parseInt(adultTravelers) !== 1 ? 's' : ''}${parseInt(childTravelers) > 0 ? `, ${childTravelers} child${parseInt(childTravelers) !== 1 ? 'ren' : ''}` : ''}`;

    // Correctly log based on mode
    console.log(`Generating plan (${planningMode} mode) with:`, {
      destination,
      ...(planningMode === 'personal' ? { startDate, endDate } : { duration, season, travelerComposition }),
      budget,
      budgetCurrency,
      preferences
    });

    try {
      // --- Research Agent (Common) ---
      console.log('Starting Text Research Agent...');
      const researchQuery = planningMode === 'ai' && season
        ? `Things to do in ${destination} during ${season}, travel tips for ${destination}, family-friendly activities in ${destination}`
        : `Things to do in ${destination}, travel tips for ${destination}`;
      const searchResults = await callGoogleSearchApi(researchQuery);
      console.log(`Text Research found ${searchResults.length} items.`);
      
      // --- Weather Agent (Common) ---
      console.log('Starting Weather Agent...');
      let weatherInfo: WeatherSummary | null = null;
      try {
        weatherInfo = await callOpenWeatherApi(
          destination,
          planningMode === 'personal' ? startDate : null,
          planningMode === 'personal' ? endDate : null
        );
        console.log('Weather Agent finished:', weatherInfo ? 'Weather data retrieved successfully' : 'No weather data available');
      } catch (weatherError) {
        console.error('Error in Weather Agent:', weatherError);
        // Continue with the planning process even if weather data fails
      }

      // --- Planning Agent (Choose Prompt based on Mode) ---
      let promptMessages;
      if (planningMode === 'personal') {
        console.log('Starting Personal Planning Agent...');
        promptMessages = generateTripPlanPrompt(
          destination,
          formatDate(startDate!), // Use non-null assertion as validation passed
          formatDate(endDate!),   // Use non-null assertion as validation passed
          `${budget} ${budgetCurrency}`,
          preferences,
          searchResults,
          weatherInfo // 传递天气信息
        );
      } else { // AI Mode - Ensure variables are defined in this scope
         console.log('Starting AI Planning Agent...');
         // 将人数参数传递给AI提示函数，使用新的旅客构成字符串
         promptMessages = generateAITripPlanPrompt(
           destination,
           duration,
           season,
           `${budget} ${budgetCurrency}`,
           preferences,
           travelerComposition, // 使用格式化的旅客构成
           searchResults,
           weatherInfo // 传递天气信息
         );
      }

      const textPlanWithMarkers = await callLlama4Api(promptMessages);
      console.log('Planning Agent finished.');

      // --- Image Integration (Common) ---
      console.log('Starting Image Integration based on [IMAGE_FOR] Markers...');
      const keywords = extractImageKeywords(textPlanWithMarkers);
      const imageUrlsMap = new Map<string, string>();
      
      // 追踪已使用的图片URL以避免重复
      const usedImageUrls = new Set<string>();
      
      // 准备默认/备用图片Map，按目的地分类
      const defaultImagesForDestination = new Map<string, string>([
        ['beach', 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=600'],
        ['mountain', 'https://images.unsplash.com/photo-1454496522488-7a8e488e8606?w=600'],
        ['city', 'https://images.unsplash.com/photo-1480714378408-67cf0d13bc1b?w=600'],
        ['landmark', 'https://images.unsplash.com/photo-1552832230-c0197dd311b5?w=600'],
        ['food', 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600'],
        ['nature', 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=600'],
        ['default', 'https://images.unsplash.com/photo-1503220317375-aaad61436b1b?w=600'], // 通用备用图
      ]);
      
      // 为每个关键词获取图片
      await Promise.all(keywords.map(async (keyword, index) => {
        try {
          // 限制同时处理的图片数量，避免API限制
          if (index >= 8) {
            console.log(`跳过图片获取: "${keyword}" (达到最大图片数量限制)`);
            return;
          }
          
          // 获取图片URL
          const imageUrl = await callGoogleImageSearchApi(`${keyword}, ${destination}`);
          
          // 检查URL是否有效且未被使用
          if (imageUrl && !usedImageUrls.has(imageUrl)) {
            // 测试图片URL是否真实可访问
            try {
              const imageTestResponse = await fetch(imageUrl, { method: 'HEAD' });
              if (imageTestResponse && imageTestResponse.ok) {
                usedImageUrls.add(imageUrl);
                imageUrlsMap.set(keyword, imageUrl);
                console.log(`获取有效图片URL: "${keyword}": ${imageUrl.substring(0, 50)}...`);
                return; // 成功获取图片后，退出此关键词的处理
              }
              console.warn(`图片URL返回非200状态码: ${imageUrl}`);
            } catch (error) {
              console.warn(`测试图片URL时出错: ${error}`);
            }
          } else if (imageUrl && usedImageUrls.has(imageUrl)) {
            console.warn(`为"${keyword}"获取的图片URL已被使用: ${imageUrl.substring(0, 30)}...`);
          } else {
            console.warn(`未能为"${keyword}"获取图片URL`);
          }
          
          // 如果Google搜索无法获取有效图片或URL重复，使用备用图片
          // 根据关键词选择适合的备用图片
          let backupImage = '';
          
          for (const [category, url] of defaultImagesForDestination.entries()) {
            if (category !== 'default' && 
               ((keyword || '').toLowerCase().includes(category) || 
                (destination || '').toLowerCase().includes(category))) {
              backupImage = url;
              break;
            }
          }
          
          if (!backupImage) {
            backupImage = defaultImagesForDestination.get('default') as string;
          }
          
          imageUrlsMap.set(keyword, backupImage);
          console.log(`使用备用图片: "${keyword}": ${backupImage}`);
          
        } catch (error) {
          console.error(`处理关键词"${keyword}"图片时出错:`, error);
          // 出错时使用默认图片
          const defaultImage = defaultImagesForDestination.get('default') as string;
          imageUrlsMap.set(keyword, defaultImage);
        }
      }));
      console.log('Image URLs fetched:', imageUrlsMap);

      // --- 处理导航标记 ---
      console.log('Processing navigation markers...');
      const navMap = extractNavigationMarkers(textPlanWithMarkers);

      // --- 构建最终Markdown ---
      let finalPlanMarkdown = textPlanWithMarkers;
      
      // 替换图片标记
      imageUrlsMap.forEach((url, keyword) => {
        try {
          // 使用安全的URL编码函数
          const encodedUrl = safeEncodeImageUrl(url);
          
          console.log(`Encoding image URL for "${keyword}": 
            Original: ${url.substring(0, 30)}...
            Encoded: ${encodedUrl.substring(0, 30)}...`);
          
          const imageMarkdown = `\n\n![${keyword}](${encodedUrl})\n\n`;
          const escapedKeyword = keyword.replace(/[-\/\\^$*+?.()|[{}]]/g, '\\$&');
          const markerRegex = new RegExp(`\\[IMAGE_FOR=${escapedKeyword}\\]`, 'g');
          const beforeReplace = finalPlanMarkdown;
          finalPlanMarkdown = finalPlanMarkdown.replace(markerRegex, imageMarkdown);
          
          // 检查替换是否成功
          if (beforeReplace === finalPlanMarkdown) {
            console.warn(`No [IMAGE_FOR=${keyword}] marker found in the text to replace`);
          } else {
            console.log(`Successfully replaced [IMAGE_FOR=${keyword}] with image markdown`);
          }
        } catch (error) {
          console.error(`Error replacing image marker for "${keyword}":`, error);
        }
      });

      // 移除剩余图片标记
      const beforeCleanup = finalPlanMarkdown;
      finalPlanMarkdown = finalPlanMarkdown.replace(/\[IMAGE_FOR=(.*?)\]/g, '');
      if (beforeCleanup !== finalPlanMarkdown) {
        console.log(`Removed ${(beforeCleanup.match(/\[IMAGE_FOR=(.*?)\]/g) || []).length} remaining image markers`);
      }

      // 替换导航标记
      navMap.forEach((url, marker) => {
        const linkMarkdown = `[📍导航](${url})`;
        finalPlanMarkdown = finalPlanMarkdown.replace(marker, linkMarkdown);
      });

      console.log('Image and Navigation Integration finished.');
      console.log('---- FULL FINAL MARKDOWN ----');
      console.log(finalPlanMarkdown);
      
      // Store the plan in the appropriate state variable based on mode
      if (planningMode === 'personal') {
        setPersonalPlan(finalPlanMarkdown);
      } else {
        setAIPlan(finalPlanMarkdown);
      }
      // Also set the generatedPlan for display
      setGeneratedPlan(finalPlanMarkdown);

      // Show success toast
      showToast({
        type: 'success',
        title: 'Plan Generated!',
        message: 'Your travel plan has been created successfully.',
      });

    } catch (err) {
      console.error("Error generating plan:", err);
      const message = err instanceof Error ? err.message : 'An unknown error occurred.';
      setError(`Failed to generate plan: ${message}`);

      // Show error toast
      showToast({
        type: 'error',
        title: 'Generation Failed',
        message: 'Failed to generate your travel plan. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 创建自定义渲染器
  const customRenderers = {
    // 增强图片渲染器，添加错误处理
    image: (node: any, children: any, parent: any, styles: any) => {
      const { src, alt } = node.attributes;
      const imageKey = `img-${src}-${alt}`;
      
      // 检查图片URL是否有效
      if (!src || typeof src !== 'string' || src.trim() === '') {
        console.warn(`Invalid image source for ${alt || 'unknown image'}`);
        // 返回空视图而不是null，避免潜在的布局问题
        return <View key={imageKey} style={{height: 20}} />;
      }
      
      // 使用useState钩子处理图片加载错误状态
      const [hasError, setHasError] = React.useState(false);
      const [isLoading, setIsLoading] = React.useState(true);
      
      // 选择备用图片 - 基于alt文本选择分类
      const getDefaultImageForType = (imageAlt: string): string => {
        const altText = (imageAlt || '').toLowerCase();
        if (altText.includes('beach') || altText.includes('海滩')) 
          return 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=600';
        if (altText.includes('mountain') || altText.includes('山')) 
          return 'https://images.unsplash.com/photo-1454496522488-7a8e488e8606?w=600';
        if (altText.includes('city') || altText.includes('城市')) 
          return 'https://images.unsplash.com/photo-1480714378408-67cf0d13bc1b?w=600';
        if (altText.includes('food') || altText.includes('美食') || altText.includes('餐')) 
          return 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600';
        if (altText.includes('nature') || altText.includes('自然')) 
          return 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=600';
        
        // 默认备用图
        return 'https://images.unsplash.com/photo-1503220317375-aaad61436b1b?w=600';
      };
      
      // 图片发生错误时使用的默认图
      const fallbackSrc = getDefaultImageForType(alt);
      
      console.log(`Rendering image: ${alt} with source: ${hasError ? 'FALLBACK' : src.substring(0, 30)}...`);
      
      return (
        <View key={imageKey} style={{marginVertical: 16}}>
          {isLoading && (
            <View style={{
              position: 'absolute', 
              top: 0, left: 0, right: 0, bottom: 0, 
              justifyContent: 'center', 
              alignItems: 'center',
              backgroundColor: '#f0f0f0',
              borderRadius: 10,
              zIndex: 1
            }}>
              <ActivityIndicator size="large" color="#3498db" />
            </View>
          )}
          
          <Image 
            style={[
              styles.image, 
              hasError ? { borderColor: '#f0f0f0', borderWidth: 1 } : {}
            ]}
            source={{ uri: hasError ? fallbackSrc : src }}
            accessible={true}
            accessibilityLabel={alt || "旅行相关图片"}
            onError={(error: any) => {
              console.error(`Error loading image ${alt}:`, error.nativeEvent.error);
              setHasError(true);
              setIsLoading(false);
            }}
            onLoad={() => setIsLoading(false)}
          />
          
          {alt && (
            <Text style={{
              textAlign: 'center',
              color: '#666',
              marginTop: 6,
              marginBottom: 10,
              fontStyle: 'italic',
              fontSize: 14
            }}>
              {alt}
            </Text>
          )}
        </View>
      );
    },
    
    // 优化导航链接渲染器 - 使用英文
    link: (node: any, children: any, _parent: any, _styles: any) => {
      const { href } = node.attributes;
      const linkText = children[0] || '';
      const linkKey = `link-${href}-${typeof linkText === 'string' ? linkText.substring(0, 10) : Math.random().toString(36).substring(7)}`;
      
      // 检查是否为导航链接（通过链接文本中是否包含导航emoji判断）
      const isNavLink = typeof linkText === 'string' && linkText.includes('📍');
      
      if (isNavLink) {
        // 从链接文本中提取位置名称
        const locationName = typeof linkText === 'string' 
          ? linkText.replace('📍', '').trim() 
          : 'Location';
          
        return (
          <View key={linkKey} style={styles.navButtonContainer}>
            <TouchableOpacity
              style={styles.navButton}
              onPress={() => {
                // 尝试打开链接
                Linking.canOpenURL(href).then(supported => {
                  if (supported) {
                    Linking.openURL(href);
                  } else {
                    console.log(`Cannot open URL: ${href}`);
                    // 显示错误提示
                    Alert.alert(
                      "Navigation Error",
                      "Could not open maps for this location. Please try again later.",
                      [{ text: "OK" }]
                    );
                  }
                }).catch(err => {
                  console.error('Error opening URL:', err);
                  Alert.alert(
                    "Navigation Error",
                    "An error occurred while trying to open maps.",
                    [{ text: "OK" }]
                  );
                });
              }}
              activeOpacity={0.7}
            >
              <View style={styles.navButtonInner}>
                <FontAwesome5 name="directions" size={16} color="white" style={styles.navButtonIcon} />
                <Text style={styles.navButtonText}>Navigate</Text>
              </View>
            </TouchableOpacity>
            <Text style={styles.navLocationName} numberOfLines={1} ellipsizeMode="tail">
              {locationName}
            </Text>
          </View>
        );
      }
      
      // 对于非导航链接，使用默认样式
      return (
        <Text
          key={linkKey}
          style={styles.link}
          onPress={() => {
            Linking.canOpenURL(href).then(supported => {
              if (supported) {
                Linking.openURL(href);
              } else {
                console.log(`Cannot open URL: ${href}`);
              }
            });
          }}
        >
          {linkText}
        </Text>
      );
    },
    
    // 添加自定义段落渲染器，以增强交通信息的显示
    paragraph: (node: any, children: any, parent: any, styles: any) => {
      // 为段落生成唯一的key
      const paragraphKey = `paragraph-${node.key || Math.random().toString(36).substring(7)}`;
      
      // 检查是否为交通信息段落
      const isTransportParagraph = 
        children && 
        typeof children[0] === 'string' && 
        children[0].includes('➡️') && 
        (children[0].includes('🚗') || 
         children[0].includes('🚶‍♂️') || 
         children[0].includes('🚇') || 
         children[0].includes('🚌') || 
         children[0].includes('🚕') || 
         children[0].includes('🚆') ||
         children[0].includes('✈️') ||
         children[0].includes('🚢'));
      
      if (isTransportParagraph) {
        return (
          <View key={paragraphKey} style={styles.transportContainer}>
            {children}
          </View>
        );
      }
      
      // 对于非交通段落，使用默认样式
      return (
        <View key={paragraphKey} style={styles.paragraph}>
          {children}
        </View>
      );
    },
    
    // 添加对列表项的处理
    list_item: (node: any, children: any, parent: any, styles: any) => {
      const itemKey = `list-item-${node.key || Math.random().toString(36).substring(7)}`;
      
      return (
        <View key={itemKey} style={styles.list_item}>
          {children}
        </View>
      );
    },
    
    // 处理其他可能的元素
    text: (node: any, children: any, parent: any, styles: any) => {
      if (typeof node.content !== 'string') {
        return null;
      }
      
      const textKey = `text-${node.key || Math.random().toString(36).substring(7)}`;
      return <Text key={textKey} style={styles.text}>{node.content}</Text>;
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: Colors.backgroundDefault }}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={{ padding: Spacing.md }}
          keyboardShouldPersistTaps="handled"
        >
          {/* 🎨 Trekmate 4.0 - 现代化标题 */}
          <YStack space="$md" marginBottom="$xl" alignItems="center">
            <Text
              fontSize={32}
              fontWeight="700"
              color="$textPrimary"
              textAlign="center"
              lineHeight={38}
            >
              Plan Your Next Adventure
            </Text>
            <Text
              fontSize="$md"
              color="$textSecondary"
              textAlign="center"
              maxWidth={300}
              lineHeight={22}
            >
              Create the perfect itinerary with AI assistance or personal planning
            </Text>
          </YStack>

          {/* 🎨 Trekmate 4.0 - 现代化模式切换器 */}
          <Card
            padding="$lg"
            marginBottom="$xl"
            borderRadius="$lg"
            backgroundColor="$backgroundDefault"
            shadowColor="$primary"
            shadowOffset={{ width: 0, height: 2 }}
            shadowOpacity={0.08}
            shadowRadius={8}
            elevation={3}
          >
            <YStack space="$md">
              <Text fontSize="$lg" fontWeight="600" color="$textPrimary" textAlign="center">
                Choose Your Planning Mode
              </Text>
              <XStack space="$sm">
                <TamaguiButton
                  flex={1}
                  title="Personal Planner"
                  onPress={() => {
                    setPlanningMode('personal');
                    setGeneratedPlan(personalPlan);
                    setError(null);
                  }}
                  variant={planningMode === 'personal' ? 'primary' : 'outline'}
                  size="$lg"
                  leftIcon="person"
                />
                <TamaguiButton
                  flex={1}
                  title="AI Assistant"
                  onPress={() => {
                    setPlanningMode('ai');
                    setGeneratedPlan(aiPlan);
                    setError(null);
                  }}
                  variant={planningMode === 'ai' ? 'primary' : 'outline'}
                  size="$lg"
                  leftIcon="sparkles"
                />
              </XStack>
              <Text fontSize="$sm" color="$textSecondary" textAlign="center" lineHeight={18}>
                {planningMode === 'personal'
                  ? "Create detailed itineraries with specific dates and preferences"
                  : "Let AI generate smart travel plans based on your preferences"
                }
              </Text>
            </YStack>
          </Card>

          {/* 🎨 Trekmate 4.0 - 根据模式渲染不同的界面 */}
          {planningMode === 'personal' ? (
            <PersonalPlannerHome />
          ) : (
            /* 🎨 Trekmate 4.0 - AI Plan Input Card */
            <Card
              padding="$xl"
              marginBottom="$xl"
              borderRadius="$lg"
              backgroundColor="$backgroundDefault"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 2 }}
              shadowOpacity={0.08}
              shadowRadius={8}
              elevation={3}
            >
              <YStack space="$lg">
                <Text fontSize="$lg" fontWeight="600" color="$textPrimary" textAlign="center">
                  AI Travel Planning
                </Text>

                {/* Destination */}
                <YStack space="$sm">
                  <XStack alignItems="center" space="$sm">
                    <Ionicons name="location-outline" size={20} color={Colors.primary} />
                    <Text fontSize="$md" fontWeight="600" color="$textPrimary">
                      Destination
                    </Text>
                  </XStack>
                  <Input
                    placeholder="e.g., Tokyo, Japan"
                    value={destination}
                    onChangeText={setDestination}
                    borderRadius="$md"
                    borderColor="$borderDefault"
                    backgroundColor="$backgroundMuted"
                    fontSize="$md"
                    height={50}
                    paddingHorizontal="$md"
                  />
                </YStack>

                {/* Duration */}
                <YStack space="$sm">
                  <XStack alignItems="center" space="$sm">
                    <Ionicons name="time-outline" size={20} color={Colors.primary} />
                    <Text fontSize="$md" fontWeight="600" color="$textPrimary">
                      Duration
                    </Text>
                  </XStack>
                  <Input
                    placeholder="e.g., 5 days, 1 week"
                    value={duration}
                    onChangeText={setDuration}
                    borderRadius="$md"
                    borderColor="$borderDefault"
                    backgroundColor="$backgroundMuted"
                    fontSize="$md"
                    height={50}
                    paddingHorizontal="$md"
                  />
                </YStack>

                {/* Season */}
                <YStack space="$sm">
                  <XStack alignItems="center" space="$sm">
                    <Ionicons name="partly-sunny-outline" size={20} color={Colors.primary} />
                    <Text fontSize="$md" fontWeight="600" color="$textPrimary">
                      Season
                    </Text>
                  </XStack>
                  <View style={{
                    borderWidth: 1,
                    borderColor: Colors.textPlaceholder,
                    borderRadius: BorderRadius.md,
                    backgroundColor: Colors.backgroundMuted,
                    overflow: 'hidden',
                    height: 50,
                  }}>
                    <Picker
                      selectedValue={season}
                      style={{ height: 50, color: Colors.textPrimary }}
                      onValueChange={(itemValue: string) => setSeason(itemValue as Season)}
                      mode="dropdown"
                    >
                      <Picker.Item label="Select Season..." value="" />
                      <Picker.Item label="Spring (Mar-May)" value="Spring" />
                      <Picker.Item label="Summer (Jun-Aug)" value="Summer" />
                      <Picker.Item label="Autumn (Sep-Nov)" value="Autumn" />
                      <Picker.Item label="Winter (Dec-Feb)" value="Winter" />
                      <Picker.Item label="Any Season" value="Any" />
                    </Picker>
                  </View>
                </YStack>

                {/* Travelers */}
                <YStack space="$sm">
                  <XStack alignItems="center" space="$sm">
                    <Ionicons name="people-outline" size={20} color={Colors.primary} />
                    <Text fontSize="$md" fontWeight="600" color="$textPrimary">
                      Travelers
                    </Text>
                  </XStack>
                  <XStack space="$md">
                    <YStack flex={1} space="$sm">
                      <Text fontSize="$sm" color="$textSecondary" fontWeight="500">Adults</Text>
                      <XStack
                        alignItems="center"
                        space="$sm"
                        borderWidth={1}
                        borderColor="$borderDefault"
                        borderRadius="$md"
                        backgroundColor="$backgroundMuted"
                        paddingHorizontal="$sm"
                        height={50}
                      >
                        <Button
                          size="$sm"
                          circular
                          onPress={() => {
                            const current = parseInt(adultTravelers) || 0;
                            if (current > 1) setAdultTravelers((current - 1).toString());
                          }}
                          backgroundColor="$backgroundDefault"
                          borderColor="$borderDefault"
                          pressStyle={{ backgroundColor: '$primary', borderColor: '$primary' }}
                        >
                          <Text color="$textPrimary">-</Text>
                        </Button>
                        <Text flex={1} textAlign="center" fontSize="$lg" fontWeight="600" color="$textPrimary">
                          {adultTravelers}
                        </Text>
                        <Button
                          size="$sm"
                          circular
                          onPress={() => {
                            const current = parseInt(adultTravelers) || 0;
                            setAdultTravelers((current + 1).toString());
                          }}
                          backgroundColor="$backgroundDefault"
                          borderColor="$borderDefault"
                          pressStyle={{ backgroundColor: '$primary', borderColor: '$primary' }}
                        >
                          <Text color="$textPrimary">+</Text>
                        </Button>
                      </XStack>
                    </YStack>
                    <YStack flex={1} space="$sm">
                      <Text fontSize="$sm" color="$textSecondary" fontWeight="500">Children</Text>
                      <XStack
                        alignItems="center"
                        space="$sm"
                        borderWidth={1}
                        borderColor="$borderDefault"
                        borderRadius="$md"
                        backgroundColor="$backgroundMuted"
                        paddingHorizontal="$sm"
                        height={50}
                      >
                        <Button
                          size="$sm"
                          circular
                          onPress={() => {
                            const current = parseInt(childTravelers) || 0;
                            if (current > 0) setChildTravelers((current - 1).toString());
                          }}
                          backgroundColor="$backgroundDefault"
                          borderColor="$borderDefault"
                          pressStyle={{ backgroundColor: '$primary', borderColor: '$primary' }}
                        >
                          <Text color="$textPrimary">-</Text>
                        </Button>
                        <Text flex={1} textAlign="center" fontSize="$lg" fontWeight="600" color="$textPrimary">
                          {childTravelers}
                        </Text>
                        <Button
                          size="$sm"
                          circular
                          onPress={() => {
                            const current = parseInt(childTravelers) || 0;
                            setChildTravelers((current + 1).toString());
                          }}
                          backgroundColor="$backgroundDefault"
                          borderColor="$borderDefault"
                          pressStyle={{ backgroundColor: '$primary', borderColor: '$primary' }}
                        >
                          <Text color="$textPrimary">+</Text>
                        </Button>
                      </XStack>
                    </YStack>
                  </XStack>
                </YStack>

                {/* Budget */}
                <YStack space="$sm">
                  <XStack alignItems="center" space="$sm">
                    <Ionicons name="wallet-outline" size={20} color={Colors.primary} />
                    <Text fontSize="$md" fontWeight="600" color="$textPrimary">
                      Budget
                    </Text>
                  </XStack>
                  <XStack space="$sm" alignItems="center">
                    <Input
                      flex={1}
                      placeholder="Budget Amount"
                      value={budget}
                      onChangeText={setBudget}
                      keyboardType="numeric"
                      borderRadius="$md"
                      borderColor="$borderDefault"
                      backgroundColor="$backgroundMuted"
                      fontSize="$md"
                      height={50}
                      paddingHorizontal="$md"
                    />
                    <View style={{
                      borderWidth: 1,
                      borderColor: Colors.textPlaceholder,
                      borderRadius: BorderRadius.md,
                      backgroundColor: Colors.backgroundMuted,
                      overflow: 'hidden',
                      minWidth: 90,
                      height: 50,
                    }}>
                      <Picker
                        selectedValue={budgetCurrency}
                        style={{ height: 50, color: Colors.textPrimary }}
                        onValueChange={(itemValue: string) => setBudgetCurrency(itemValue as CurrencyCode)}
                        mode="dropdown"
                      >
                        {Object.keys(CURRENCY_UNITS).map((code) => (
                          <Picker.Item key={`currency-${code}`} label={code} value={code} />
                        ))}
                      </Picker>
                    </View>
                  </XStack>
                </YStack>

                {/* Preferences */}
                <YStack space="$sm">
                  <XStack alignItems="center" space="$sm">
                    <Ionicons name="options-outline" size={20} color={Colors.primary} />
                    <Text fontSize="$md" fontWeight="600" color="$textPrimary">
                      Preferences
                    </Text>
                  </XStack>
                  <Input
                    placeholder="e.g., relaxing, adventure, cultural sites..."
                    value={preferences}
                    onChangeText={setPreferences}
                    borderRadius="$md"
                    borderColor="$borderDefault"
                    backgroundColor="$backgroundMuted"
                    fontSize="$md"
                    height={50}
                    paddingHorizontal="$md"
                    multiline
                    numberOfLines={2}
                  />
                </YStack>
            </YStack>
          </Card>
          )}

          {/* 🎨 Trekmate 4.0 - Error Message */}
          {error && (
            <Card
              padding="$lg"
              marginBottom="$lg"
              backgroundColor={Colors.primaryLight}
              borderColor={Colors.error}
              borderWidth={1}
              borderRadius="$lg"
            >
              <XStack alignItems="center" space="$sm">
                <Ionicons name="alert-circle" size={20} color={Colors.error} />
                <Text color={Colors.error} fontSize="$md" flex={1} lineHeight={20}>
                  {error}
                </Text>
              </XStack>
            </Card>
          )}

          {/* 🎨 Trekmate 4.0 - Generate Button */}
          <TamaguiButton
            title={isLoading ? "Generating Your Plan..." : "Generate Trip Plan"}
            onPress={handleGeneratePlan}
            disabled={isLoading}
            variant="primary"
            size="$lg"
            marginBottom="$xl"
            leftIcon={isLoading ? undefined : "sparkles"}
            style={{
              shadowColor: Colors.primary,
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.2,
              shadowRadius: 8,
              elevation: 6,
            }}
          >
            {isLoading && (
              <XStack alignItems="center" space="$sm">
                <Spinner size="small" color="white" />
                <Text color="white" fontSize="$md" fontWeight="600">
                  This may take a moment...
                </Text>
              </XStack>
            )}
          </TamaguiButton>

          {/* 🎨 Trekmate 4.0 - Results Area */}
          {generatedPlan && !isLoading && (
            <Card
              padding="$xl"
              marginBottom="$xl"
              borderRadius="$lg"
              backgroundColor="$backgroundDefault"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 4 }}
              shadowOpacity={0.12}
              shadowRadius={12}
              elevation={8}
            >
              <YStack space="$lg">
                <XStack alignItems="center" justifyContent="center" space="$sm">
                  <Ionicons name="checkmark-circle" size={24} color={Colors.success} />
                  <Text fontSize="$xl" fontWeight="700" color="$textPrimary">
                    Your Travel Plan
                  </Text>
                </XStack>

                <ScrollView
                  nestedScrollEnabled={true}
                  style={{
                    maxHeight: 500,
                    borderRadius: BorderRadius.md,
                    backgroundColor: Colors.backgroundMuted,
                    padding: Spacing.md,
                  }}
                  showsVerticalScrollIndicator={true}
                  contentContainerStyle={{ paddingBottom: Spacing.lg }}
                >
                  <MarkdownDisplay
                    style={markdownStyles}
                    mergeStyle={true}
                    rules={customRenderers}
                  >
                    {generatedPlan}
                  </MarkdownDisplay>
                </ScrollView>

                {/* Action buttons for the generated plan */}
                <XStack space="$sm" justifyContent="center">
                  <TamaguiButton
                    title="Save Plan"
                    variant="outline"
                    size="$md"
                    leftIcon="bookmark"
                    onPress={() => {
                      // TODO: Implement save functionality
                      Alert.alert('Save Plan', 'Save functionality will be implemented soon!');
                    }}
                  />
                  <TamaguiButton
                    title="Share"
                    variant="outline"
                    size="$md"
                    leftIcon="share"
                    onPress={() => {
                      // TODO: Implement share functionality
                      Alert.alert('Share Plan', 'Share functionality will be implemented soon!');
                    }}
                  />
                </XStack>
              </YStack>
            </Card>
          )}

          {/* Date pickers (conditionally rendered based on mode and state) remain below */}
          {planningMode === 'personal' && showStartDatePicker && (
            <DateTimePicker
              testID="dateTimePickerStart"
              value={startDate || new Date()}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              onChange={onStartDateChange}
              minimumDate={new Date()}
            />
          )}
          {planningMode === 'personal' && showEndDatePicker && startDate && (
            <DateTimePicker
              testID="dateTimePickerEnd"
              value={endDate || startDate}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              onChange={onEndDateChange}
              minimumDate={startDate}
            />
          )}

        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

// 🎨 Trekmate 4.0 - 保留必要的样式用于 Markdown 和图片显示
const styles = StyleSheet.create({
  // 🎨 Trekmate 4.0 - 保留图片和 Markdown 相关样式
  image: {
    width: '100%',
    height: 210,
    alignSelf: 'center',
    marginVertical: Spacing.md,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.textPlaceholder,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  // 🎨 Trekmate 4.0 - 导航按钮样式（用于 Markdown 中的导航链接）
  navButtonContainer: {
    marginVertical: Spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  navButton: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    borderRadius: BorderRadius.full,
    marginRight: Spacing.sm,
    marginVertical: Spacing.xs,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  navButtonInner: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  navButtonIcon: {
    marginRight: 5,
  },
  navButtonText: {
    color: Colors.buttonPrimaryText,
    fontWeight: 'bold',
    fontSize: 14,
  },
  navLocationName: {
    flex: 1,
    fontSize: 14,
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  // 🎨 Trekmate 4.0 - 交通信息容器样式（用于 Markdown 渲染）
  transportContainer: {
    backgroundColor: Colors.primaryLight,
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    marginVertical: Spacing.sm,
    borderRadius: BorderRadius.sm,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
});