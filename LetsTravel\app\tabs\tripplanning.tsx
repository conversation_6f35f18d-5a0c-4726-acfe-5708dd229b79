import React from 'react';
import { View, Text, StyleSheet, useColorScheme, ScrollView } from 'react-native';
import { Box, VStack, Button, HStack } from '@chakra-ui/react';
import { Colors } from '../../constants/DesignTokens';

// 🎨 Trekmate 4.0 - 简化的 Trip Planning 页面
export default function TripPlanningScreen() {
  const colorScheme = useColorScheme();
  const backgroundColor = colorScheme === 'dark' ? '#121212' : Colors.backgroundDefault;
  const cardBackground = colorScheme === 'dark' ? '#1E1E1E' : 'white';
  const textColor = colorScheme === 'dark' ? 'white' : Colors.textPrimary;

  return (
    <Box flex={1} bg={backgroundColor}>
      <ScrollView>
        {/* 🎨 Trekmate 4.0 - 标题 */}
        <VStack p={6} pt={16}>
          <Text
            style={{
              fontSize: 32,
              fontWeight: '700',
              color: textColor,
              textAlign: 'center',
              marginBottom: 8,
            }}
          >
            Trip Planning
          </Text>
          <Text
            style={{
              fontSize: 16,
              color: textColor,
              opacity: 0.8,
              textAlign: 'center',
              marginBottom: 32,
            }}
          >
            Plan your perfect journey with AI assistance
          </Text>
        </VStack>

        {/* 🎨 Trekmate 4.0 - 功能卡片 */}
        <VStack spacing={4} px={6}>
          <Box
            bg={cardBackground}
            borderRadius="lg"
            p={6}
            shadow="sm"
          >
            <Text
              style={{
                fontSize: 20,
                fontWeight: '600',
                color: textColor,
                marginBottom: 12,
              }}
            >
              Personal Planner
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: textColor,
                opacity: 0.7,
                marginBottom: 16,
              }}
            >
              Create custom itineraries with smart suggestions
            </Text>
            <Button
              colorScheme="brand"
              size="md"
            >
              Start Planning
            </Button>
          </Box>

          <Box
            bg={cardBackground}
            borderRadius="lg"
            p={6}
            shadow="sm"
          >
            <Text
              style={{
                fontSize: 20,
                fontWeight: '600',
                color: textColor,
                marginBottom: 12,
              }}
            >
              AI Trip Generator
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: textColor,
                opacity: 0.7,
                marginBottom: 16,
              }}
            >
              Let AI create complete travel plans for you
            </Text>
            <Button
              colorScheme="brand"
              variant="outline"
              size="md"
            >
              Generate Trip
            </Button>
          </Box>

          <Box
            bg={cardBackground}
            borderRadius="lg"
            p={6}
            shadow="sm"
          >
            <Text
              style={{
                fontSize: 20,
                fontWeight: '600',
                color: textColor,
                marginBottom: 12,
              }}
            >
              Saved Plans
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: textColor,
                opacity: 0.7,
                marginBottom: 16,
              }}
            >
              Access your previously created itineraries
            </Text>
            <Button
              colorScheme="gray"
              variant="outline"
              size="md"
            >
              View Plans
            </Button>
          </Box>
        </VStack>

        {/* 版本信息 */}
        <VStack alignItems="center" p={6} mt={6}>
          <Text
            style={{
              fontSize: 12,
              color: textColor,
              opacity: 0.6,
            }}
          >
            Trip Planning Module - Trekmate 4.0
          </Text>
        </VStack>
      </ScrollView>
    </Box>
  );
}
