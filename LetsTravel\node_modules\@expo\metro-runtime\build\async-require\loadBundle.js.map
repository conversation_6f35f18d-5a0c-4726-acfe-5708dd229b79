{"version": 3, "file": "loadBundle.js", "sourceRoot": "", "sources": ["../../src/async-require/loadBundle.ts"], "names": [], "mappings": ";;;AAAA;;;;;GAKG;AACH,2DAAwD;AACxD,mDAAqD;AAErD,IAAI,eAAe,GAAG,CAAC,CAAC;AAExB;;;;GAIG;AACI,KAAK,UAAU,eAAe,CAAC,UAAkB;IACtD,MAAM,UAAU,GAAG,IAAA,qCAAiB,EAAC,UAAU,CAAC,CAAC;IAEjD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;QACzC,OAAO,IAAA,kCAAkB,EAAC,UAAU,CAAC,CAAC;KACvC;SAAM;QACL,MAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,CAAC;aAC1C,OAAkD,CAAC;QACtD,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE;YACjC,qEAAqE;YACrE,WAAW,CAAC,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;SACnD;QACD,eAAe,EAAE,CAAC;QAElB,OAAO,IAAA,kCAAkB,EAAC,UAAU,CAAC;aAClC,IAAI,CAAC,GAAG,EAAE;YACT,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,OAAgD,CAAC;YAC3F,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC,CAAC;aACD,OAAO,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,EAAE,eAAe,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE;gBACvD,WAAW,CAAC,IAAI,EAAE,CAAC;aACpB;QACH,CAAC,CAAC,CAAC;KACN;AACH,CAAC;AAzBD,0CAyBC", "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { buildUrlForBundle } from './buildUrlForBundle';\nimport { fetchThenEvalAsync } from './fetchThenEval';\n\nlet pendingRequests = 0;\n\n/**\n * Load a bundle for a URL using fetch + eval on native and script tag injection on web.\n *\n * @param bundlePath Given a statement like `import('./Bacon')` `bundlePath` would be `Bacon.bundle?params=from-metro`.\n */\nexport async function loadBundleAsync(bundlePath: string): Promise<void> {\n  const requestUrl = buildUrlForBundle(bundlePath);\n\n  if (process.env.NODE_ENV === 'production') {\n    return fetchThenEvalAsync(requestUrl);\n  } else {\n    const LoadingView = require('../LoadingView')\n      .default as typeof import('../LoadingView').default;\n    if (process.env.EXPO_OS !== 'web') {\n      // Send a signal to the `expo` package to show the loading indicator.\n      LoadingView.showMessage('Downloading...', 'load');\n    }\n    pendingRequests++;\n\n    return fetchThenEvalAsync(requestUrl)\n      .then(() => {\n        const HMRClient = require('../HMRClient').default as typeof import('../HMRClient').default;\n        HMRClient.registerBundle(requestUrl);\n      })\n      .finally(() => {\n        if (!--pendingRequests && process.env.EXPO_OS !== 'web') {\n          LoadingView.hide();\n        }\n      });\n  }\n}\n"]}