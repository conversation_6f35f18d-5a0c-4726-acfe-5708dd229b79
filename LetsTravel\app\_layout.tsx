// Import WebFixes first to ensure it runs before anything else
import './react-native-web-fix';

import 'react-native-gesture-handler';
import { useFonts } from 'expo-font';
import { Stack, useRouter } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState, useCallback } from 'react';
import { useColorScheme, View, Text, Platform } from 'react-native';
import { Asset } from 'expo-asset';
import 'react-native-reanimated'; // Re-enabled

// 🎨 Trekmate 4.0 - 导入设计令牌
import { Colors, Typography } from '../constants/DesignTokens';

// 🎨 Trekmate 4.0 - 导入 Chakra UI Provider
import { ChakraProvider } from '../providers/ChakraProvider';

// 🎨 Trekmate 4.0 - 导入 Providers 和组件
import { ToastProvider } from '../ui/Toast';
import { GlobalLoaderProvider } from '../ui/GlobalLoader';
import { AppProvider } from '../contexts/AppContext';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { PerformanceProvider } from '../components/PerformanceMonitor';

// 导入调度服务
import { scheduler } from '../services/scheduler';

// Prevent splash screen from auto-hiding before resources are ready
SplashScreen.preventAutoHideAsync().catch(e => console.warn("Error preventing splash screen auto-hide:", e));

export default function RootLayout() {
  console.log("RootLayout component initializing");
  
  const colorScheme = useColorScheme();
  const [fontError, setFontError] = useState<Error | null>(null);
  const [fontTimeout, setFontTimeout] = useState(false);
  const [appIsReady, setAppIsReady] = useState(false);
  const router = useRouter();
  
  // Add logs for debugging
  useEffect(() => {
    console.log('RootLayout component mounted');
    
    // // 启动调度服务 - COMMENTED OUT FOR DEBUGGING
    // console.log('Starting scheduler service...');
    // try {
    //   scheduler.start();
    //   console.log('Scheduler service started successfully');
    // } catch (error) {
    //   console.error('Failed to start scheduler service:', error);
    // }
    
    // // 在组件卸载时停止调度服务 - COMMENTED OUT FOR DEBUGGING
    // return () => {
    //   console.log('Stopping scheduler service...');
    //   try {
    //     scheduler.stop();
    //     console.log('Scheduler service stopped successfully');
    //   } catch (error) {
    //     console.error('Failed to stop scheduler service:', error);
    //   }
    // };
  }, []);

  // 🎨 Trekmate 4.0 - 加载品牌字体
  const [fontsLoaded, fontLoadError] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    // TODO: 添加 Poppins, Inter, Open Sans 字体文件
    // Poppins: require('../assets/fonts/Poppins-SemiBold.ttf'),
    // Inter: require('../assets/fonts/Inter-Regular.ttf'),
    // OpenSans: require('../assets/fonts/OpenSans-LightItalic.ttf'),
  });

  // Preload assets and setup app - COMMENTED OUT FOR DEBUGGING
  useEffect(() => {
    // async function prepare() {
    //   try {
    //     console.log("Preparing assets and app resources...");
        
    //     // Pre-load assets safely with detailed error logging
    //     try {
    //       // First check if image files exist by creating Image objects
    //       const imageAssets = [
    //         require('../assets/images/LETSTRAVELLOGO.png'),
    //         require('../assets/images/LETSTRAVELWELCOME.png')
    //       ];
          
    //       console.log("Image assets found, now loading...");
          
    //       // Load assets with progress tracking
    //       const assetPromises = imageAssets.map(async (asset, index) => {
    //         try {
    //           await Asset.loadAsync(asset);
    //           console.log(`Asset ${index + 1} loaded successfully`);
    //           return true;
    //         } catch (assetError) {
    //           console.warn(`Failed to load asset ${index + 1}:`, assetError);
    //           return false;
    //         }
    //       });
          
    //       const results = await Promise.all(assetPromises);
    //       console.log(`Assets loaded: ${results.filter(Boolean).length}/${results.length}`);
    //     } catch (assetError) {
    //       console.warn("Non-critical asset loading error:", assetError);
    //       // Continue even if assets fail to load
    //     }
    //   } catch (e) {
    //     console.warn("Error preparing app:", e);
    //   } finally {
    //     // Force app to be ready after a timeout even if assets are still loading
    //     console.log("Setting app as ready");
    //     setAppIsReady(true);
    //   }
    // }

    // prepare(); // COMMENTED OUT

    // // Ensure app is set as ready even if prepare fails silently - COMMENTED OUT
    // const backupTimeout = setTimeout(() => {
    //   if (!appIsReady) {
    //     console.log("Backup timeout: forcing app to ready state");
    //     setAppIsReady(true);
    //   }
    // }, 3000);
    
    // // return () => clearTimeout(backupTimeout); // COMMENTED OUT

    // FORCE app ready immediately for debugging
    setAppIsReady(true);
    console.log("[DEBUG] Forcing appIsReady to true immediately");

  }, []); // Keep dependency array empty

  // Handle font loading errors
  useEffect(() => {
    if (fontLoadError) {
      console.warn('Font loading error (non-critical):', fontLoadError);
      setFontError(fontLoadError);
    }

    // Set timeout to prevent infinite waiting for fonts
    const timeoutId = setTimeout(() => {
      if (!fontsLoaded) {
        console.log('Font loading timeout after 2 seconds - continuing anyway');
        setFontTimeout(true);
      }
    }, 2000);

    return () => clearTimeout(timeoutId);
  }, [fontsLoaded, fontLoadError]);

  // Callback to hide splash screen when ready
  const onLayoutRootView = useCallback(async () => {
    console.log(`[DEBUG] onLayoutRootView called. appIsReady=${appIsReady}, fontsLoaded=${fontsLoaded}, fontError=${!!fontError}, fontTimeout=${fontTimeout}`);

    // 🎨 Trekmate 4.0 - 立即隐藏 SplashScreen，让 welcome 页面接管
    if (appIsReady || fontsLoaded || fontError || fontTimeout) {
      try {
        await SplashScreen.hideAsync();
        console.log("✅ SplashScreen hidden successfully");
      } catch (e) {
        console.warn("Non-critical: Error hiding splash screen:", e);
      }
    }

  }, [appIsReady, fontsLoaded, fontError, fontTimeout]);

  // Force hide splash screen after a timeout - COMMENTED OUT FOR DEBUGGING
  // useEffect(() => {
  //   const forceHideTimeout = setTimeout(() => {
  //     console.log("Force hiding splash screen after timeout");
  //     SplashScreen.hideAsync().catch(e => {
  //       console.warn("Error force hiding splash screen:", e);
  //     });
  //   }, 5000);
    
  //   return () => clearTimeout(forceHideTimeout);
  // }, []);

  // When the app is not ready yet, render a basic loading screen - COMMENTED OUT (forced ready)
  // if (!appIsReady) {
  //   return (
  //     <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
  //       <Text>Loading application...</Text>
  //     </View>
  //   );
  // }

  // 🎨 Trekmate 4.0 - 简化的主题配置（移除 React Navigation 主题）

  // Navigation structure with Trekmate theme and Tamagui
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('App-level error:', error, errorInfo);
        // In production, send to error reporting service
      }}
    >
      <ChakraProvider>
        <PerformanceProvider enableGlobalMonitoring={__DEV__}>
          <AppProvider>
            <GlobalLoaderProvider>
              <ToastProvider>
                <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
                  <Stack
                    screenOptions={{
                      headerShown: false,
                      contentStyle: {
                        backgroundColor: colorScheme === 'dark' ? '#121212' : Colors.backgroundDefault
                      }
                    }}
                  >
                    <Stack.Screen name="index" options={{ headerShown: false }} />
                    <Stack.Screen name="welcome" options={{ headerShown: false }} />
                    <Stack.Screen name="login" options={{ headerShown: false }} />
                    <Stack.Screen name="tabs" options={{ headerShown: false }} />
                  </Stack>
                  <StatusBar
                    style={colorScheme === 'dark' ? 'light' : 'dark'}
                    backgroundColor={colorScheme === 'dark' ? '#121212' : Colors.backgroundDefault}
                  />
                </View>
              </ToastProvider>
            </GlobalLoaderProvider>
          </AppProvider>
        </PerformanceProvider>
      </ChakraProvider>
    </ErrorBoundary>
  );
}
