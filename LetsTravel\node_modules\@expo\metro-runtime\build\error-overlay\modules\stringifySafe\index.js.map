{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/error-overlay/modules/stringifySafe/index.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH;;;GAGG;AACH,SAAgB,6BAA6B,CAAC,MAK7C;IACC,MAAM,EACJ,QAAQ,GAAG,MAAM,CAAC,iBAAiB,EACnC,cAAc,GAAG,MAAM,CAAC,iBAAiB,EACzC,aAAa,GAAG,MAAM,CAAC,iBAAiB,EACxC,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,GAC9C,GAAG,MAAM,CAAC;IACX,MAAM,KAAK,GAAU,EAAE,CAAC;IACxB,SAAS,QAAQ,CAAgB,IAAY,EAAE,KAAU;QACvD,OAAO,KAAK,CAAC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;YACxC,KAAK,CAAC,KAAK,EAAE,CAAC;SACf;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,eAAe,GAAG,mBAAmB,CAAC;YAC5C,IAAI,KAAK,CAAC,MAAM,GAAG,cAAc,GAAG,eAAe,CAAC,MAAM,EAAE;gBAC1D,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,eAAe,CAAC;aAC7D;YACD,OAAO,KAAK,CAAC;SACd;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;YAC/C,OAAO,KAAK,CAAC;SACd;QAED,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,EAAE;gBAC5B,MAAM,GAAG,oBAAoB,KAAK,CAAC,MAAM,eAAe,CAAC;aAC1D;iBAAM,IAAI,KAAK,CAAC,MAAM,GAAG,aAAa,EAAE;gBACvC,MAAM,GAAG,KAAK;qBACX,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC;qBACvB,MAAM,CAAC,CAAC,aAAa,KAAK,CAAC,MAAM,GAAG,aAAa,uBAAuB,CAAC,CAAC,CAAC;aAC/E;SACF;aAAM;YACL,2CAA2C;YAC3C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;aACnD;YACD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChC,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,EAAE;gBAC5B,MAAM,GAAG,qBAAqB,IAAI,CAAC,MAAM,aAAa,CAAC;aACxD;iBAAM,IAAI,IAAI,CAAC,MAAM,GAAG,kBAAkB,EAAE;gBAC3C,+BAA+B;gBAC/B,MAAM,GAAG,EAAE,CAAC;gBACZ,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,kBAAkB,CAAC,EAAE;oBACjD,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;iBACtB;gBACD,MAAM,YAAY,GAAG,wBAAwB,CAAC;gBAC9C,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC;aACzD;SACF;QACD,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACtB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,SAAS,aAAa,CAAC,GAAQ;QACpC,IAAI,GAAG,KAAK,SAAS,EAAE;YACrB,OAAO,WAAW,CAAC;SACpB;aAAM,IAAI,GAAG,KAAK,IAAI,EAAE;YACvB,OAAO,MAAM,CAAC;SACf;aAAM,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;YACpC,IAAI;gBACF,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;aACvB;YAAC,MAAM;gBACN,OAAO,oBAAoB,CAAC;aAC7B;SACF;aAAM,IAAI,GAAG,YAAY,KAAK,EAAE;YAC/B,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC;SACtC;aAAM;YACL,8DAA8D;YAC9D,uDAAuD;YACvD,IAAI;gBACF,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;gBAC1C,IAAI,GAAG,KAAK,SAAS,EAAE;oBACrB,OAAO,IAAI,GAAG,OAAO,GAAG,GAAG,wBAAwB,CAAC;iBACrD;gBACD,OAAO,GAAG,CAAC;aACZ;YAAC,MAAM;gBACN,IAAI,OAAO,GAAG,CAAC,QAAQ,KAAK,UAAU,EAAE;oBACtC,IAAI;wBACF,mFAAmF;wBACnF,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;qBACvB;oBAAC,MAAM,GAAE;iBACX;aACF;SACF;QACD,OAAO,IAAI,GAAG,OAAO,GAAG,GAAG,wBAAwB,CAAC;IACtD,CAAC,CAAC;AACJ,CAAC;AA7FD,sEA6FC;AAED,MAAM,aAAa,GAAG,6BAA6B,CAAC;IAClD,QAAQ,EAAE,EAAE;IACZ,cAAc,EAAE,GAAG;IACnB,aAAa,EAAE,EAAE;IACjB,kBAAkB,EAAE,EAAE;CACvB,CAAC,CAAC;AAEH,kBAAe,aAAa,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n/**\n * Tries to stringify with JSON.stringify and toString, but catches exceptions\n * (e.g. from circular objects) and always returns a string and never throws.\n */\nexport function createStringifySafeWithLimits(limits: {\n  maxDepth?: number;\n  maxStringLimit?: number;\n  maxArrayLimit?: number;\n  maxObjectKeysLimit?: number;\n}): (foo: any) => string {\n  const {\n    maxDepth = Number.POSITIVE_INFINITY,\n    maxStringLimit = Number.POSITIVE_INFINITY,\n    maxArrayLimit = Number.POSITIVE_INFINITY,\n    maxObjectKeysLimit = Number.POSITIVE_INFINITY,\n  } = limits;\n  const stack: any[] = [];\n  function replacer(this: unknown, _key: string, value: any): any {\n    while (stack.length && this !== stack[0]) {\n      stack.shift();\n    }\n\n    if (typeof value === 'string') {\n      const truncatedString = '...(truncated)...';\n      if (value.length > maxStringLimit + truncatedString.length) {\n        return value.substring(0, maxStringLimit) + truncatedString;\n      }\n      return value;\n    }\n    if (typeof value !== 'object' || value === null) {\n      return value;\n    }\n\n    let retval = value;\n    if (Array.isArray(value)) {\n      if (stack.length >= maxDepth) {\n        retval = `[ ... array with ${value.length} values ... ]`;\n      } else if (value.length > maxArrayLimit) {\n        retval = value\n          .slice(0, maxArrayLimit)\n          .concat([`... extra ${value.length - maxArrayLimit} values truncated ...`]);\n      }\n    } else {\n      // Add refinement after Array.isArray call.\n      if (typeof value !== 'object') {\n        throw new Error('This was already found earlier');\n      }\n      const keys = Object.keys(value);\n      if (stack.length >= maxDepth) {\n        retval = `{ ... object with ${keys.length} keys ... }`;\n      } else if (keys.length > maxObjectKeysLimit) {\n        // Return a sample of the keys.\n        retval = {};\n        for (const k of keys.slice(0, maxObjectKeysLimit)) {\n          retval[k] = value[k];\n        }\n        const truncatedKey = '...(truncated keys)...';\n        retval[truncatedKey] = keys.length - maxObjectKeysLimit;\n      }\n    }\n    stack.unshift(retval);\n    return retval;\n  }\n\n  return function stringifySafe(arg: any): string {\n    if (arg === undefined) {\n      return 'undefined';\n    } else if (arg === null) {\n      return 'null';\n    } else if (typeof arg === 'function') {\n      try {\n        return arg.toString();\n      } catch {\n        return '[function unknown]';\n      }\n    } else if (arg instanceof Error) {\n      return arg.name + ': ' + arg.message;\n    } else {\n      // Perform a try catch, just in case the object has a circular\n      // reference or stringify throws for some other reason.\n      try {\n        const ret = JSON.stringify(arg, replacer);\n        if (ret === undefined) {\n          return '[\"' + typeof arg + '\" failed to stringify]';\n        }\n        return ret;\n      } catch {\n        if (typeof arg.toString === 'function') {\n          try {\n            // $FlowFixMe[incompatible-use] : toString shouldn't take any arguments in general.\n            return arg.toString();\n          } catch {}\n        }\n      }\n    }\n    return '[\"' + typeof arg + '\" failed to stringify]';\n  };\n}\n\nconst stringifySafe = createStringifySafeWithLimits({\n  maxDepth: 10,\n  maxStringLimit: 100,\n  maxArrayLimit: 50,\n  maxObjectKeysLimit: 50,\n});\n\nexport default stringifySafe;\n"]}