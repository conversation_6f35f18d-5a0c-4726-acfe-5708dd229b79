{"version": 3, "file": "useLinkToPathProps.js", "sourceRoot": "", "sources": ["../../src/link/useLinkToPathProps.tsx"], "names": [], "mappings": ";;AACA,+CAA+D;AAE/D,+DAAyD;AACzD,+DAA6D;AAC7D,0CAAyD;AAEzD,SAAS,yBAAyB,CAChC,CAA0E;IAE1E,IAAI,CAAC,EAAE,gBAAgB,EAAE;QACvB,OAAO,KAAK,CAAC;KACd;IAED;IACE,yBAAyB;IACzB,QAAQ,IAAI,CAAC;QACb,mCAAmC;QACnC,CAAC,CAAC,CAAC,OAAO;QACV,CAAC,CAAC,CAAC,MAAM;QACT,CAAC,CAAC,CAAC,OAAO;QACV,CAAC,CAAC,CAAC,QAAQ;QACX,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,0BAA0B;QAClE,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,0CAA0C;MACzG;QACA,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAwB,kBAAkB,CAAC,KAAuC;IAChF,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,4BAAa,GAAE,CAAC;IAEnC,MAAM,OAAO,GAAG,CAAC,CAA2E,EAAE,EAAE;QAC9F,IAAI,YAAY,GAAG,KAAK,CAAC;QAEzB,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC,EAAE;YAC/B,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC;SAC/C;aAAM,IAAI,yBAAyB,CAAC,CAAC,CAAC,EAAE;YACvC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,YAAY,GAAG,IAAI,CAAC;SACrB;QAED,IAAI,YAAY,EAAE;YAChB,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;SACjC;IACH,CAAC,CAAC;IAEF,OAAO;QACL,sHAAsH;QACtH,IAAI,EAAE,IAAA,gCAAa,EAAC,IAAA,qCAA0B,EAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;QAClE,IAAI,EAAE,MAAe;QACrB,OAAO;KACR,CAAC;AACJ,CAAC;AAxBD,qCAwBC", "sourcesContent": ["import * as React from 'react';\nimport { GestureResponderEvent, Platform } from 'react-native';\n\nimport { appendBaseUrl } from '../fork/getPathFromState';\nimport { useExpoRouter } from '../global-state/router-store';\nimport { stripGroupSegmentsFromPath } from '../matchers';\n\nfunction eventShouldPreventDefault(\n  e: React.MouseEvent<HTMLAnchorElement, MouseEvent> | GestureResponderEvent\n): boolean {\n  if (e?.defaultPrevented) {\n    return false;\n  }\n\n  if (\n    // Only check MouseEvents\n    'button' in e &&\n    // ignore clicks with modifier keys\n    !e.metaKey &&\n    !e.altKey &&\n    !e.ctrlKey &&\n    !e.shiftKey &&\n    (e.button == null || e.button === 0) && // Only accept left clicks\n    [undefined, null, '', 'self'].includes(e.currentTarget.target) // let browser handle \"target=_blank\" etc.\n  ) {\n    return true;\n  }\n\n  return false;\n}\n\nexport default function useLinkToPathProps(props: { href: string; event?: string }) {\n  const { linkTo } = useExpoRouter();\n\n  const onPress = (e?: React.MouseEvent<HTMLAnchorElement, MouseEvent> | GestureResponderEvent) => {\n    let shouldHandle = false;\n\n    if (Platform.OS !== 'web' || !e) {\n      shouldHandle = e ? !e.defaultPrevented : true;\n    } else if (eventShouldPreventDefault(e)) {\n      e.preventDefault();\n      shouldHandle = true;\n    }\n\n    if (shouldHandle) {\n      linkTo(props.href, props.event);\n    }\n  };\n\n  return {\n    // Ensure there's always a value for href. Manually append the baseUrl to the href prop that shows in the static HTML.\n    href: appendBaseUrl(stripGroupSegmentsFromPath(props.href) || '/'),\n    role: 'link' as const,\n    onPress,\n  };\n}\n"]}