{"version": 3, "file": "ErrorToastMessage.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/toast/ErrorToastMessage.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,+CAAgD;AAGhD,uDAAoD;AACpD,+DAAiD;AAEjD,SAAgB,iBAAiB,CAAC,EAAE,OAAO,EAA6B;IACtE,OAAO,CACL,8BAAC,mBAAI,IAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,IACvC,OAAO,IAAI,8BAAC,6BAAa,IAAC,SAAS,QAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,gBAAgB,GAAI,CACpF,CACR,CAAC;AACJ,CAAC;AAND,8CAMC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,EAAE;QACJ,UAAU,EAAE,MAAM;QAClB,WAAW,EAAE,CAAC;QACd,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;QAClC,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,EAAE;KACf;IACD,gBAAgB,EAAE;QAChB,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC;KACrC;CACF,CAAC,CAAC", "sourcesContent": ["import React from 'react';\nimport { StyleSheet, Text } from 'react-native';\n\nimport type { Message as MessageType } from '../Data/parseLogBoxLog';\nimport { LogBoxMessage } from '../UI/LogBoxMessage';\nimport * as LogBoxStyle from '../UI/LogBoxStyle';\n\nexport function ErrorToastMessage({ message }: { message?: MessageType }) {\n  return (\n    <Text numberOfLines={1} style={styles.text}>\n      {message && <LogBoxMessage plaintext message={message} style={styles.substitutionText} />}\n    </Text>\n  );\n}\n\nconst styles = StyleSheet.create({\n  text: {\n    userSelect: 'none',\n    paddingLeft: 8,\n    color: LogBoxStyle.getTextColor(1),\n    flex: 1,\n    fontSize: 14,\n    lineHeight: 22,\n  },\n  substitutionText: {\n    color: LogBoxStyle.getTextColor(0.6),\n  },\n});\n"]}