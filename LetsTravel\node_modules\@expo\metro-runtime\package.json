{"name": "@expo/metro-runtime", "version": "3.2.3", "description": "Tools for making advanced Metro bundler features work", "sideEffects": true, "types": "build", "main": "build", "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/metro-runtime", "keywords": [], "author": "650 Industries, Inc.", "license": "MIT", "files": ["build", "src", "symbolicate", "async-require.js", "async-require.d.ts", "error-overlay.js", "error-overlay.d.ts", "assets", "!**/__tests__"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git"}, "peerDependencies": {"react-native": "*"}, "gitHead": "0e91c05bf4a7dcd6837c8031d0d32b446212b53d"}