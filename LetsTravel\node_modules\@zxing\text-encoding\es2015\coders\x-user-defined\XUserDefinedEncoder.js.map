{"version": 3, "file": "XUserDefinedEncoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/x-user-defined/XUserDefinedEncoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAC7E,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAEnD;;;;GAIG;AACH,MAAM,OAAO,mBAAmB;IAI9B,YAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,MAAc,EAAE,UAAkB;QACxC,qDAAqD;QACrD,IAAI,UAAU,KAAK,aAAa;YAC9B,OAAO,QAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,gBAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,gEAAgE;QAChE,2DAA2D;QAC3D,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;YACrC,OAAO,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC;QAEpC,mCAAmC;QACnC,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;CACF"}