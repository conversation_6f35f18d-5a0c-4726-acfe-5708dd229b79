{"version": 3, "file": "generateCode.js", "names": ["_crypto", "data", "_interopRequireDefault", "require", "obj", "__esModule", "default", "getGeneratedSectionIndexes", "src", "tag", "contents", "split", "start", "findIndex", "line", "RegExp", "test", "end", "mergeContents", "newSrc", "anchor", "offset", "comment", "header", "createGeneratedHeaderComment", "includes", "sanitizedTarget", "removeGeneratedContents", "addLines", "didMerge", "<PERSON><PERSON><PERSON><PERSON>", "removeContents", "content", "find", "toAdd", "lines", "lineIndex", "match", "error", "Error", "code", "newLine", "splice", "join", "hash<PERSON><PERSON>", "createHash", "hash", "crypto", "update", "digest"], "sources": ["../../src/utils/generateCode.ts"], "sourcesContent": ["/**\n * Get line indexes for the generated section of a file.\n *\n * @param src\n */\nimport crypto from 'crypto';\n\nfunction getGeneratedSectionIndexes(\n  src: string,\n  tag: string\n): { contents: string[]; start: number; end: number } {\n  const contents = src.split('\\n');\n  const start = contents.findIndex((line) => new RegExp(`@generated begin ${tag} -`).test(line));\n  const end = contents.findIndex((line) => new RegExp(`@generated end ${tag}$`).test(line));\n\n  return { contents, start, end };\n}\n\nexport type MergeResults = {\n  contents: string;\n  didClear: boolean;\n  didMerge: boolean;\n};\n\n/**\n * Merge the contents of two files together and add a generated header.\n *\n * @param src contents of the original file\n * @param newSrc new contents to merge into the original file\n * @param identifier used to update and remove merges\n * @param anchor regex to where the merge should begin\n * @param offset line offset to start merging at (<1 for behind the anchor)\n * @param comment comment style `//` or `#`\n */\nexport function mergeContents({\n  src,\n  newSrc,\n  tag,\n  anchor,\n  offset,\n  comment,\n}: {\n  src: string;\n  newSrc: string;\n  tag: string;\n  anchor: string | RegExp;\n  offset: number;\n  comment: string;\n}): MergeResults {\n  const header = createGeneratedHeaderComment(newSrc, tag, comment);\n  if (!src.includes(header)) {\n    // Ensure the old generated contents are removed.\n    const sanitizedTarget = removeGeneratedContents(src, tag);\n    return {\n      contents: addLines(sanitizedTarget ?? src, anchor, offset, [\n        header,\n        ...newSrc.split('\\n'),\n        `${comment} @generated end ${tag}`,\n      ]),\n      didMerge: true,\n      didClear: !!sanitizedTarget,\n    };\n  }\n  return { contents: src, didClear: false, didMerge: false };\n}\n\nexport function removeContents({ src, tag }: { src: string; tag: string }): MergeResults {\n  // Ensure the old generated contents are removed.\n  const sanitizedTarget = removeGeneratedContents(src, tag);\n  return {\n    contents: sanitizedTarget ?? src,\n    didMerge: false,\n    didClear: !!sanitizedTarget,\n  };\n}\n\nfunction addLines(content: string, find: string | RegExp, offset: number, toAdd: string[]) {\n  const lines = content.split('\\n');\n\n  let lineIndex = lines.findIndex((line) => line.match(find));\n  if (lineIndex < 0) {\n    const error = new Error(`Failed to match \"${find}\" in contents:\\n${content}`);\n    // @ts-ignore\n    error.code = 'ERR_NO_MATCH';\n    throw error;\n  }\n  for (const newLine of toAdd) {\n    lines.splice(lineIndex + offset, 0, newLine);\n    lineIndex++;\n  }\n\n  return lines.join('\\n');\n}\n\n/**\n * Removes the generated section from a file, returns null when nothing can be removed.\n * This sways heavily towards not removing lines unless it's certain that modifications were not made manually.\n *\n * @param src\n */\nexport function removeGeneratedContents(src: string, tag: string): string | null {\n  const { contents, start, end } = getGeneratedSectionIndexes(src, tag);\n  if (start > -1 && end > -1 && start < end) {\n    contents.splice(start, end - start + 1);\n    // TODO: We could in theory check that the contents we're removing match the hash used in the header,\n    // this would ensure that we don't accidentally remove lines that someone added or removed from the generated section.\n    return contents.join('\\n');\n  }\n  return null;\n}\n\nexport function createGeneratedHeaderComment(\n  contents: string,\n  tag: string,\n  comment: string\n): string {\n  const hashKey = createHash(contents);\n\n  // Everything after the `${tag} ` is unversioned and can be freely modified without breaking changes.\n  return `${comment} @generated begin ${tag} - expo prebuild (DO NOT MODIFY) ${hashKey}`;\n}\n\nexport function createHash(src: string): string {\n  // this doesn't need to be secure, the shorter the better.\n  const hash = crypto.createHash('sha1').update(src).digest('hex');\n  return `sync-${hash}`;\n}\n"], "mappings": ";;;;;;;;;;AAKA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAC,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAL5B;AACA;AACA;AACA;AACA;;AAGA,SAASG,0BAA0BA,CACjCC,GAAW,EACXC,GAAW,EACyC;EACpD,MAAMC,QAAQ,GAAGF,GAAG,CAACG,KAAK,CAAC,IAAI,CAAC;EAChC,MAAMC,KAAK,GAAGF,QAAQ,CAACG,SAAS,CAAEC,IAAI,IAAK,IAAIC,MAAM,CAAC,oBAAoBN,GAAG,IAAI,CAAC,CAACO,IAAI,CAACF,IAAI,CAAC,CAAC;EAC9F,MAAMG,GAAG,GAAGP,QAAQ,CAACG,SAAS,CAAEC,IAAI,IAAK,IAAIC,MAAM,CAAC,kBAAkBN,GAAG,GAAG,CAAC,CAACO,IAAI,CAACF,IAAI,CAAC,CAAC;EAEzF,OAAO;IAAEJ,QAAQ;IAAEE,KAAK;IAAEK;EAAI,CAAC;AACjC;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAAC;EAC5BV,GAAG;EACHW,MAAM;EACNV,GAAG;EACHW,MAAM;EACNC,MAAM;EACNC;AAQF,CAAC,EAAgB;EACf,MAAMC,MAAM,GAAGC,4BAA4B,CAACL,MAAM,EAAEV,GAAG,EAAEa,OAAO,CAAC;EACjE,IAAI,CAACd,GAAG,CAACiB,QAAQ,CAACF,MAAM,CAAC,EAAE;IACzB;IACA,MAAMG,eAAe,GAAGC,uBAAuB,CAACnB,GAAG,EAAEC,GAAG,CAAC;IACzD,OAAO;MACLC,QAAQ,EAAEkB,QAAQ,CAACF,eAAe,IAAIlB,GAAG,EAAEY,MAAM,EAAEC,MAAM,EAAE,CACzDE,MAAM,EACN,GAAGJ,MAAM,CAACR,KAAK,CAAC,IAAI,CAAC,EACrB,GAAGW,OAAO,mBAAmBb,GAAG,EAAE,CACnC,CAAC;MACFoB,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CAAC,CAACJ;IACd,CAAC;EACH;EACA,OAAO;IAAEhB,QAAQ,EAAEF,GAAG;IAAEsB,QAAQ,EAAE,KAAK;IAAED,QAAQ,EAAE;EAAM,CAAC;AAC5D;AAEO,SAASE,cAAcA,CAAC;EAAEvB,GAAG;EAAEC;AAAkC,CAAC,EAAgB;EACvF;EACA,MAAMiB,eAAe,GAAGC,uBAAuB,CAACnB,GAAG,EAAEC,GAAG,CAAC;EACzD,OAAO;IACLC,QAAQ,EAAEgB,eAAe,IAAIlB,GAAG;IAChCqB,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,CAAC,CAACJ;EACd,CAAC;AACH;AAEA,SAASE,QAAQA,CAACI,OAAe,EAAEC,IAAqB,EAAEZ,MAAc,EAAEa,KAAe,EAAE;EACzF,MAAMC,KAAK,GAAGH,OAAO,CAACrB,KAAK,CAAC,IAAI,CAAC;EAEjC,IAAIyB,SAAS,GAAGD,KAAK,CAACtB,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACuB,KAAK,CAACJ,IAAI,CAAC,CAAC;EAC3D,IAAIG,SAAS,GAAG,CAAC,EAAE;IACjB,MAAME,KAAK,GAAG,IAAIC,KAAK,CAAC,oBAAoBN,IAAI,mBAAmBD,OAAO,EAAE,CAAC;IAC7E;IACAM,KAAK,CAACE,IAAI,GAAG,cAAc;IAC3B,MAAMF,KAAK;EACb;EACA,KAAK,MAAMG,OAAO,IAAIP,KAAK,EAAE;IAC3BC,KAAK,CAACO,MAAM,CAACN,SAAS,GAAGf,MAAM,EAAE,CAAC,EAAEoB,OAAO,CAAC;IAC5CL,SAAS,EAAE;EACb;EAEA,OAAOD,KAAK,CAACQ,IAAI,CAAC,IAAI,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAShB,uBAAuBA,CAACnB,GAAW,EAAEC,GAAW,EAAiB;EAC/E,MAAM;IAAEC,QAAQ;IAAEE,KAAK;IAAEK;EAAI,CAAC,GAAGV,0BAA0B,CAACC,GAAG,EAAEC,GAAG,CAAC;EACrE,IAAIG,KAAK,GAAG,CAAC,CAAC,IAAIK,GAAG,GAAG,CAAC,CAAC,IAAIL,KAAK,GAAGK,GAAG,EAAE;IACzCP,QAAQ,CAACgC,MAAM,CAAC9B,KAAK,EAAEK,GAAG,GAAGL,KAAK,GAAG,CAAC,CAAC;IACvC;IACA;IACA,OAAOF,QAAQ,CAACiC,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA,OAAO,IAAI;AACb;AAEO,SAASnB,4BAA4BA,CAC1Cd,QAAgB,EAChBD,GAAW,EACXa,OAAe,EACP;EACR,MAAMsB,OAAO,GAAGC,UAAU,CAACnC,QAAQ,CAAC;;EAEpC;EACA,OAAO,GAAGY,OAAO,qBAAqBb,GAAG,oCAAoCmC,OAAO,EAAE;AACxF;AAEO,SAASC,UAAUA,CAACrC,GAAW,EAAU;EAC9C;EACA,MAAMsC,IAAI,GAAGC,iBAAM,CAACF,UAAU,CAAC,MAAM,CAAC,CAACG,MAAM,CAACxC,GAAG,CAAC,CAACyC,MAAM,CAAC,KAAK,CAAC;EAChE,OAAO,QAAQH,IAAI,EAAE;AACvB", "ignoreList": []}