{"version": 3, "file": "withDefaultPlugins.js", "names": ["_configPlugins", "data", "require", "_debug", "_interopRequireDefault", "_withAndroidIcons", "_withIosIcons", "_expoAdsAdmob", "_expoAppleAuthentication", "_expoContacts", "_expoDocumentPicker", "_expoNavigationBar", "_expoNotifications", "_expoSplashScreen", "_expoSystemUi", "_expoUpdates", "_reactNativeMaps", "_getAutolinkedPackages", "obj", "__esModule", "default", "debug", "Debug", "withIosExpoPlugins", "config", "bundleIdentifier", "ios", "with<PERSON><PERSON><PERSON>", "IOSConfig", "BundleIdentifier", "withBundleIdentifier", "Swift", "withSwiftBridgingHeader", "withNoopSwiftFile", "Google", "<PERSON><PERSON><PERSON><PERSON>", "Name", "withDisplayName", "withProductName", "Orientation", "withOrientation", "RequiresFullScreen", "withRequiresFullScreen", "Scheme", "withScheme", "UsesNonExemptEncryption", "withUsesNonExemptEncryption", "Version", "withBuildNumber", "withVersion", "withGoogleServicesFile", "BuildProperties", "withJsEnginePodfileProps", "Entitlements", "withAssociatedDomains", "DeviceFamily", "withDeviceFamily", "Bitcode", "withBitcode", "Locales", "withLocales", "withIosIcons", "PrivacyInfo", "withPrivacyInfo", "exports", "withAndroidExpoPlugins", "props", "android", "package", "AndroidConfig", "withJsEngineGradleProps", "withNameSettingsGradle", "GoogleServices", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withApplyPlugin", "Package", "withPackageGradle", "AllowBackup", "withA<PERSON>Backup", "WindowSoftInputMode", "withWindowSoftInputMode", "IntentFilters", "withAndroidIntentFilters", "Permissions", "withInternalBlockedPermissions", "withPermissions", "with<PERSON><PERSON>", "StatusBar", "withStatusBar", "PrimaryColor", "withPrimaryColor", "withAndroidIcons", "withPackageRefactor", "versionedExpoSDKPackages", "withVersionedExpoSDKPlugins", "withMaps", "withAdMob", "withAppleAuthentication", "withContacts", "withNotifications", "withUpdates", "withDocumentPicker", "withSystemUI", "withSplashScreen", "withNavigationBar", "getAutoPlugins", "concat", "legacyExpoPlugins", "expoManagedVersionedPlugins", "getLegacyExpoPlugins", "withOptionalLegacyPlugins", "plugins", "reduce", "prev", "plugin", "shouldSkipAutoPlugin", "withStaticPlugin", "_isLegacyPlugin", "fallback", "withLegacyExpoPlugins", "Set"], "sources": ["../../src/plugins/withDefaultPlugins.ts"], "sourcesContent": ["/**\n * These are the versioned first-party plugins with some of the future third-party plugins mixed in for legacy support.\n */\nimport {\n  AndroidConfig,\n  ConfigPlugin,\n  IOSConfig,\n  StaticPlugin,\n  withPlugins,\n  withStaticPlugin,\n} from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport Debug from 'debug';\n\nimport { withAndroidIcons } from './icons/withAndroidIcons';\nimport { withIosIcons } from './icons/withIosIcons';\nimport withAdMob from './unversioned/expo-ads-admob/expo-ads-admob';\nimport withAppleAuthentication from './unversioned/expo-apple-authentication';\nimport withContacts from './unversioned/expo-contacts';\nimport withDocumentPicker from './unversioned/expo-document-picker';\nimport withNavigationBar from './unversioned/expo-navigation-bar/expo-navigation-bar';\nimport withNotifications from './unversioned/expo-notifications/expo-notifications';\nimport withSplashScreen from './unversioned/expo-splash-screen/expo-splash-screen';\nimport withSystemUI from './unversioned/expo-system-ui/expo-system-ui';\nimport withUpdates from './unversioned/expo-updates';\nimport withMaps from './unversioned/react-native-maps';\nimport { shouldSkipAutoPlugin } from '../getAutolinkedPackages';\n\nconst debug = Debug('expo:prebuild-config');\n\n/**\n * Config plugin to apply all of the custom Expo iOS config plugins we support by default.\n * TODO: In the future most of this should go into versioned packages like expo-updates, etc...\n */\nexport const withIosExpoPlugins: ConfigPlugin<{\n  bundleIdentifier: string;\n}> = (config, { bundleIdentifier }) => {\n  // Set the bundle ID ahead of time.\n  if (!config.ios) config.ios = {};\n  config.ios.bundleIdentifier = bundleIdentifier;\n\n  return withPlugins(config, [\n    [IOSConfig.BundleIdentifier.withBundleIdentifier, { bundleIdentifier }],\n    IOSConfig.Swift.withSwiftBridgingHeader,\n    IOSConfig.Swift.withNoopSwiftFile,\n    IOSConfig.Google.withGoogle,\n    IOSConfig.Name.withDisplayName,\n    IOSConfig.Name.withProductName,\n    IOSConfig.Orientation.withOrientation,\n    IOSConfig.RequiresFullScreen.withRequiresFullScreen,\n    IOSConfig.Scheme.withScheme,\n    IOSConfig.UsesNonExemptEncryption.withUsesNonExemptEncryption,\n    IOSConfig.Version.withBuildNumber,\n    IOSConfig.Version.withVersion,\n    IOSConfig.Google.withGoogleServicesFile,\n    IOSConfig.BuildProperties.withJsEnginePodfileProps,\n    // Entitlements\n    IOSConfig.Entitlements.withAssociatedDomains,\n    // XcodeProject\n    IOSConfig.DeviceFamily.withDeviceFamily,\n    IOSConfig.Bitcode.withBitcode,\n    IOSConfig.Locales.withLocales,\n    // Dangerous\n    withIosIcons,\n    IOSConfig.PrivacyInfo.withPrivacyInfo,\n  ]);\n};\n\n/**\n * Config plugin to apply all of the custom Expo Android config plugins we support by default.\n * TODO: In the future most of this should go into versioned packages like expo-updates, etc...\n */\nexport const withAndroidExpoPlugins: ConfigPlugin<{\n  package: string;\n}> = (config, props) => {\n  // Set the package name ahead of time.\n  if (!config.android) config.android = {};\n  config.android.package = props.package;\n\n  return withPlugins(config, [\n    // gradle.properties\n    AndroidConfig.BuildProperties.withJsEngineGradleProps,\n\n    // settings.gradle\n    AndroidConfig.Name.withNameSettingsGradle,\n\n    // project build.gradle\n    AndroidConfig.GoogleServices.withClassPath,\n\n    // app/build.gradle\n    AndroidConfig.GoogleServices.withApplyPlugin,\n    AndroidConfig.Package.withPackageGradle,\n    AndroidConfig.Version.withVersion,\n\n    // AndroidManifest.xml\n    AndroidConfig.AllowBackup.withAllowBackup,\n    AndroidConfig.WindowSoftInputMode.withWindowSoftInputMode,\n    // Note: The withAndroidIntentFilters plugin must appear before the withScheme\n    // plugin or withScheme will override the output of withAndroidIntentFilters.\n    AndroidConfig.IntentFilters.withAndroidIntentFilters,\n    AndroidConfig.Scheme.withScheme,\n    AndroidConfig.Orientation.withOrientation,\n    AndroidConfig.Permissions.withInternalBlockedPermissions,\n    AndroidConfig.Permissions.withPermissions,\n\n    // strings.xml\n    AndroidConfig.Name.withName,\n\n    // Dangerous -- these plugins run in reverse order.\n    AndroidConfig.GoogleServices.withGoogleServicesFile,\n\n    // Modify colors.xml and styles.xml\n    AndroidConfig.StatusBar.withStatusBar,\n    AndroidConfig.PrimaryColor.withPrimaryColor,\n\n    withAndroidIcons,\n    // If we renamed the package, we should also move it around and rename it in source files\n    // Added last to ensure this plugin runs first. Out of tree solutions will mistakenly resolve the package incorrectly otherwise.\n    AndroidConfig.Package.withPackageRefactor,\n  ]);\n};\n\n// Must keep in sync with `withVersionedExpoSDKPlugins`\nconst versionedExpoSDKPackages: string[] = [\n  'react-native-maps',\n  'expo-ads-admob',\n  'expo-apple-authentication',\n  'expo-contacts',\n  'expo-notifications',\n  'expo-updates',\n  'expo-navigation-bar',\n  'expo-document-picker',\n  'expo-splash-screen',\n  'expo-system-ui',\n];\n\nexport const withVersionedExpoSDKPlugins: ConfigPlugin = (config) => {\n  return withPlugins(config, [\n    withMaps,\n    withAdMob,\n    withAppleAuthentication,\n    withContacts,\n    withNotifications,\n    withUpdates,\n    withDocumentPicker,\n    // System UI must come before splash screen as they overlap\n    // and splash screen will warn about conflicting rules.\n    withSystemUI,\n    withSplashScreen,\n    withNavigationBar,\n  ]);\n};\n\nexport function getAutoPlugins() {\n  return versionedExpoSDKPackages.concat(legacyExpoPlugins).concat(expoManagedVersionedPlugins);\n}\n\nexport function getLegacyExpoPlugins() {\n  return legacyExpoPlugins;\n}\n\n// Expo managed packages that require extra update.\n// These get applied automatically to create parity with expo build in eas build.\nconst legacyExpoPlugins = [\n  'expo-app-auth',\n  'expo-av',\n  'expo-background-fetch',\n  'expo-barcode-scanner',\n  'expo-brightness',\n  'expo-calendar',\n  'expo-camera',\n  'expo-cellular',\n  'expo-dev-menu',\n  'expo-dev-launcher',\n  'expo-dev-client',\n  'expo-image-picker',\n  'expo-file-system',\n  'expo-location',\n  'expo-media-library',\n  'expo-screen-orientation',\n  'expo-sensors',\n  'expo-task-manager',\n  'expo-local-authentication',\n];\n\n// Plugins that need to be automatically applied, but also get applied by expo-cli if the versioned plugin isn't available.\n// These are split up because the user doesn't need to be prompted to setup these packages.\nconst expoManagedVersionedPlugins = [\n  'expo-firebase-analytics',\n  'expo-firebase-core',\n  'expo-google-sign-in',\n];\n\nconst withOptionalLegacyPlugins: ConfigPlugin<(StaticPlugin | string)[]> = (config, plugins) => {\n  return plugins.reduce((prev, plugin) => {\n    if (shouldSkipAutoPlugin(config, plugin)) {\n      debug('Skipping unlinked auto plugin:', plugin);\n      return prev;\n    }\n\n    return withStaticPlugin(prev, {\n      // hide errors\n      _isLegacyPlugin: true,\n      plugin,\n      // If a plugin doesn't exist, do nothing.\n      fallback: (config) => config,\n    });\n  }, config);\n};\n\nexport function withLegacyExpoPlugins(config: ExpoConfig) {\n  return withOptionalLegacyPlugins(config, [\n    ...new Set(expoManagedVersionedPlugins.concat(legacyExpoPlugins)),\n  ]);\n}\n"], "mappings": ";;;;;;;;;;AAGA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AASA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,kBAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,iBAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,cAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,aAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,cAAA;EAAA,MAAAN,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAK,aAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,yBAAA;EAAA,MAAAP,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAM,wBAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,cAAA;EAAA,MAAAR,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAO,aAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,oBAAA;EAAA,MAAAT,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAQ,mBAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,mBAAA;EAAA,MAAAV,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAS,kBAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAW,mBAAA;EAAA,MAAAX,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAU,kBAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAY,kBAAA;EAAA,MAAAZ,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAW,iBAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAa,cAAA;EAAA,MAAAb,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAY,aAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAc,aAAA;EAAA,MAAAd,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAa,YAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAe,iBAAA;EAAA,MAAAf,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAc,gBAAA,YAAAA,CAAA;IAAA,OAAAf,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAgB,uBAAA;EAAA,MAAAhB,IAAA,GAAAC,OAAA;EAAAe,sBAAA,YAAAA,CAAA;IAAA,OAAAhB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAgE,SAAAG,uBAAAc,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AA1BhE;AACA;AACA;;AA0BA,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,sBAAsB,CAAC;;AAE3C;AACA;AACA;AACA;AACO,MAAMC,kBAEX,GAAGA,CAACC,MAAM,EAAE;EAAEC;AAAiB,CAAC,KAAK;EACrC;EACA,IAAI,CAACD,MAAM,CAACE,GAAG,EAAEF,MAAM,CAACE,GAAG,GAAG,CAAC,CAAC;EAChCF,MAAM,CAACE,GAAG,CAACD,gBAAgB,GAAGA,gBAAgB;EAE9C,OAAO,IAAAE,4BAAW,EAACH,MAAM,EAAE,CACzB,CAACI,0BAAS,CAACC,gBAAgB,CAACC,oBAAoB,EAAE;IAAEL;EAAiB,CAAC,CAAC,EACvEG,0BAAS,CAACG,KAAK,CAACC,uBAAuB,EACvCJ,0BAAS,CAACG,KAAK,CAACE,iBAAiB,EACjCL,0BAAS,CAACM,MAAM,CAACC,UAAU,EAC3BP,0BAAS,CAACQ,IAAI,CAACC,eAAe,EAC9BT,0BAAS,CAACQ,IAAI,CAACE,eAAe,EAC9BV,0BAAS,CAACW,WAAW,CAACC,eAAe,EACrCZ,0BAAS,CAACa,kBAAkB,CAACC,sBAAsB,EACnDd,0BAAS,CAACe,MAAM,CAACC,UAAU,EAC3BhB,0BAAS,CAACiB,uBAAuB,CAACC,2BAA2B,EAC7DlB,0BAAS,CAACmB,OAAO,CAACC,eAAe,EACjCpB,0BAAS,CAACmB,OAAO,CAACE,WAAW,EAC7BrB,0BAAS,CAACM,MAAM,CAACgB,sBAAsB,EACvCtB,0BAAS,CAACuB,eAAe,CAACC,wBAAwB;EAClD;EACAxB,0BAAS,CAACyB,YAAY,CAACC,qBAAqB;EAC5C;EACA1B,0BAAS,CAAC2B,YAAY,CAACC,gBAAgB,EACvC5B,0BAAS,CAAC6B,OAAO,CAACC,WAAW,EAC7B9B,0BAAS,CAAC+B,OAAO,CAACC,WAAW;EAC7B;EACAC,4BAAY,EACZjC,0BAAS,CAACkC,WAAW,CAACC,eAAe,CACtC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AAHAC,OAAA,CAAAzC,kBAAA,GAAAA,kBAAA;AAIO,MAAM0C,sBAEX,GAAGA,CAACzC,MAAM,EAAE0C,KAAK,KAAK;EACtB;EACA,IAAI,CAAC1C,MAAM,CAAC2C,OAAO,EAAE3C,MAAM,CAAC2C,OAAO,GAAG,CAAC,CAAC;EACxC3C,MAAM,CAAC2C,OAAO,CAACC,OAAO,GAAGF,KAAK,CAACE,OAAO;EAEtC,OAAO,IAAAzC,4BAAW,EAACH,MAAM,EAAE;EACzB;EACA6C,8BAAa,CAAClB,eAAe,CAACmB,uBAAuB;EAErD;EACAD,8BAAa,CAACjC,IAAI,CAACmC,sBAAsB;EAEzC;EACAF,8BAAa,CAACG,cAAc,CAACC,aAAa;EAE1C;EACAJ,8BAAa,CAACG,cAAc,CAACE,eAAe,EAC5CL,8BAAa,CAACM,OAAO,CAACC,iBAAiB,EACvCP,8BAAa,CAACtB,OAAO,CAACE,WAAW;EAEjC;EACAoB,8BAAa,CAACQ,WAAW,CAACC,eAAe,EACzCT,8BAAa,CAACU,mBAAmB,CAACC,uBAAuB;EACzD;EACA;EACAX,8BAAa,CAACY,aAAa,CAACC,wBAAwB,EACpDb,8BAAa,CAAC1B,MAAM,CAACC,UAAU,EAC/ByB,8BAAa,CAAC9B,WAAW,CAACC,eAAe,EACzC6B,8BAAa,CAACc,WAAW,CAACC,8BAA8B,EACxDf,8BAAa,CAACc,WAAW,CAACE,eAAe;EAEzC;EACAhB,8BAAa,CAACjC,IAAI,CAACkD,QAAQ;EAE3B;EACAjB,8BAAa,CAACG,cAAc,CAACtB,sBAAsB;EAEnD;EACAmB,8BAAa,CAACkB,SAAS,CAACC,aAAa,EACrCnB,8BAAa,CAACoB,YAAY,CAACC,gBAAgB,EAE3CC,oCAAgB;EAChB;EACA;EACAtB,8BAAa,CAACM,OAAO,CAACiB,mBAAmB,CAC1C,CAAC;AACJ,CAAC;;AAED;AAAA5B,OAAA,CAAAC,sBAAA,GAAAA,sBAAA;AACA,MAAM4B,wBAAkC,GAAG,CACzC,mBAAmB,EACnB,gBAAgB,EAChB,2BAA2B,EAC3B,eAAe,EACf,oBAAoB,EACpB,cAAc,EACd,qBAAqB,EACrB,sBAAsB,EACtB,oBAAoB,EACpB,gBAAgB,CACjB;AAEM,MAAMC,2BAAyC,GAAItE,MAAM,IAAK;EACnE,OAAO,IAAAG,4BAAW,EAACH,MAAM,EAAE,CACzBuE,0BAAQ,EACRC,uBAAS,EACTC,kCAAuB,EACvBC,uBAAY,EACZC,4BAAiB,EACjBC,sBAAW,EACXC,6BAAkB;EAClB;EACA;EACAC,uBAAY,EACZC,2BAAgB,EAChBC,4BAAiB,CAClB,CAAC;AACJ,CAAC;AAACxC,OAAA,CAAA8B,2BAAA,GAAAA,2BAAA;AAEK,SAASW,cAAcA,CAAA,EAAG;EAC/B,OAAOZ,wBAAwB,CAACa,MAAM,CAACC,iBAAiB,CAAC,CAACD,MAAM,CAACE,2BAA2B,CAAC;AAC/F;AAEO,SAASC,oBAAoBA,CAAA,EAAG;EACrC,OAAOF,iBAAiB;AAC1B;;AAEA;AACA;AACA,MAAMA,iBAAiB,GAAG,CACxB,eAAe,EACf,SAAS,EACT,uBAAuB,EACvB,sBAAsB,EACtB,iBAAiB,EACjB,eAAe,EACf,aAAa,EACb,eAAe,EACf,eAAe,EACf,mBAAmB,EACnB,iBAAiB,EACjB,mBAAmB,EACnB,kBAAkB,EAClB,eAAe,EACf,oBAAoB,EACpB,yBAAyB,EACzB,cAAc,EACd,mBAAmB,EACnB,2BAA2B,CAC5B;;AAED;AACA;AACA,MAAMC,2BAA2B,GAAG,CAClC,yBAAyB,EACzB,oBAAoB,EACpB,qBAAqB,CACtB;AAED,MAAME,yBAAkE,GAAGA,CAACtF,MAAM,EAAEuF,OAAO,KAAK;EAC9F,OAAOA,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,MAAM,KAAK;IACtC,IAAI,IAAAC,6CAAoB,EAAC3F,MAAM,EAAE0F,MAAM,CAAC,EAAE;MACxC7F,KAAK,CAAC,gCAAgC,EAAE6F,MAAM,CAAC;MAC/C,OAAOD,IAAI;IACb;IAEA,OAAO,IAAAG,iCAAgB,EAACH,IAAI,EAAE;MAC5B;MACAI,eAAe,EAAE,IAAI;MACrBH,MAAM;MACN;MACAI,QAAQ,EAAG9F,MAAM,IAAKA;IACxB,CAAC,CAAC;EACJ,CAAC,EAAEA,MAAM,CAAC;AACZ,CAAC;AAEM,SAAS+F,qBAAqBA,CAAC/F,MAAkB,EAAE;EACxD,OAAOsF,yBAAyB,CAACtF,MAAM,EAAE,CACvC,GAAG,IAAIgG,GAAG,CAACZ,2BAA2B,CAACF,MAAM,CAACC,iBAAiB,CAAC,CAAC,CAClE,CAAC;AACJ"}