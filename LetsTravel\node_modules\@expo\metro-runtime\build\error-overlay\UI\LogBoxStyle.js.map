{"version": 3, "file": "LogBoxStyle.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/UI/LogBoxStyle.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,SAAgB,kBAAkB,CAAC,OAAgB;IACjD,OAAO,iBAAiB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;IACzD,+DAA+D;AACjE,CAAC;AAHD,gDAGC;AAED,SAAgB,uBAAuB,CAAC,OAAgB;IACtD,OAAO,oBAAoB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAC9D,CAAC;AAFD,0DAEC;AAED,SAAgB,sBAAsB,CAAC,OAAgB;IACrD,OAAO,oBAAoB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAC9D,CAAC;AAFD,wDAEC;AAED,SAAgB,eAAe,CAAC,OAAgB;IAC9C,OAAO,sBAAsB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAChE,CAAC;AAFD,0CAEC;AAED,SAAgB,mBAAmB,CAAC,OAAgB;IAClD,OAAO,qBAAqB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAC/D,CAAC;AAFD,kDAEC;AAED,SAAgB,aAAa,CAAC,OAAgB;IAC5C,OAAO,sBAAsB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAChE,CAAC;AAFD,sCAEC;AAED,SAAgB,iBAAiB,CAAC,OAAgB;IAChD,OAAO,qBAAqB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAC/D,CAAC;AAFD,8CAEC;AAED,SAAgB,aAAa,CAAC,OAAgB;IAC5C,OAAO,sBAAsB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAChE,CAAC;AAFD,sCAEC;AAED,SAAgB,iBAAiB,CAAC,OAAgB;IAChD,OAAO,qBAAqB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAC/D,CAAC;AAFD,8CAEC;AAED,SAAgB,WAAW,CAAC,OAAgB;IAC1C,OAAO,uBAAuB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AACjE,CAAC;AAFD,kCAEC;AAED,SAAgB,wBAAwB,CAAC,OAAgB;IACvD,OAAO,sBAAsB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAChE,CAAC;AAFD,4DAEC;AAED,SAAgB,eAAe,CAAC,OAAgB;IAC9C,OAAO,uBAAuB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AACjE,CAAC;AAFD,0CAEC;AAED,SAAgB,iBAAiB,CAAC,OAAgB;IAChD,OAAO,sBAAsB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AAChE,CAAC;AAFD,8CAEC;AAED,SAAgB,YAAY,CAAC,OAAgB;IAC3C,OAAO,uBAAuB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC;AACjE,CAAC;AAFD,oCAEC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nexport function getBackgroundColor(opacity?: number): string {\n  return `rgba(0, 0, 0, ${opacity == null ? 1 : opacity})`;\n  // return `rgba(51, 51, 51, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getBackgroundLightColor(opacity?: number): string {\n  return `rgba(69, 69, 69, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getBackgroundDarkColor(opacity?: number): string {\n  return `rgba(34, 34, 34, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getWarningColor(opacity?: number): string {\n  return `rgba(250, 186, 48, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getWarningDarkColor(opacity?: number): string {\n  return `rgba(224, 167, 8, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getFatalColor(opacity?: number): string {\n  return `rgba(243, 83, 105, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getFatalDarkColor(opacity?: number): string {\n  return `rgba(208, 75, 95, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getErrorColor(opacity?: number): string {\n  return `rgba(243, 83, 105, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getErrorDarkColor(opacity?: number): string {\n  return `rgba(208, 75, 95, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getLogColor(opacity?: number): string {\n  return `rgba(119, 119, 119, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getWarningHighlightColor(opacity?: number): string {\n  return `rgba(252, 176, 29, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getDividerColor(opacity?: number): string {\n  return `rgba(255, 255, 255, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getHighlightColor(opacity?: number): string {\n  return `rgba(252, 176, 29, ${opacity == null ? 1 : opacity})`;\n}\n\nexport function getTextColor(opacity?: number): string {\n  return `rgba(255, 255, 255, ${opacity == null ? 1 : opacity})`;\n}\n"]}