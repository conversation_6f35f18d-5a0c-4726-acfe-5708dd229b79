{"version": 3, "names": ["useIsKeyboardShown", "isKeyboardShown", "setIsKeyboardShown", "React", "useState", "useEffect", "handleKeyboardShow", "handleKeyboardHide", "subscriptions", "Platform", "OS", "Keyboard", "addListener", "for<PERSON>ach", "s", "remove"], "sourceRoot": "../../../src", "sources": ["utils/useIsKeyboardShown.tsx"], "mappings": ";;;;;;AAAA;AACA;AAAuE;AAAA;AAExD,SAASA,kBAAkB,GAAG;EAC3C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAAC,KAAK,CAAC;EAEnED,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMC,kBAAkB,GAAG,MAAMJ,kBAAkB,CAAC,IAAI,CAAC;IACzD,MAAMK,kBAAkB,GAAG,MAAML,kBAAkB,CAAC,KAAK,CAAC;IAE1D,IAAIM,aAAoC;IAExC,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACzBF,aAAa,GAAG,CACdG,qBAAQ,CAACC,WAAW,CAAC,kBAAkB,EAAEN,kBAAkB,CAAC,EAC5DK,qBAAQ,CAACC,WAAW,CAAC,kBAAkB,EAAEL,kBAAkB,CAAC,CAC7D;IACH,CAAC,MAAM;MACLC,aAAa,GAAG,CACdG,qBAAQ,CAACC,WAAW,CAAC,iBAAiB,EAAEN,kBAAkB,CAAC,EAC3DK,qBAAQ,CAACC,WAAW,CAAC,iBAAiB,EAAEL,kBAAkB,CAAC,CAC5D;IACH;IAEA,OAAO,MAAM;MACXC,aAAa,CAACK,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,EAAE,CAAC;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOd,eAAe;AACxB"}