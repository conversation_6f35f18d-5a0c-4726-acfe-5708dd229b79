import React, { useState } from 'react';
import { <PERSON><PERSON>, Sc<PERSON>View, useColorScheme, TouchableOpacity } from 'react-native';
import {
  Box,
  Text,
  VStack,
  HStack,
  Switch,
  Button,
  Avatar,
  Divider,
  useToast,
} from '@chakra-ui/react';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

// Import design tokens and UI components
import { Colors, Spacing, BorderRadius, Typography } from '../../constants/DesignTokens';

export default function Settings() {
  const toast = useToast();
  const colorScheme = useColorScheme();

  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);

  const backgroundColor = colorScheme === 'dark' ? '#121212' : Colors.backgroundDefault;
  const cardBackground = colorScheme === 'dark' ? '#1E1E1E' : 'white';
  const textColor = colorScheme === 'dark' ? 'white' : Colors.textPrimary;

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out of your account?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => {
            // Show logout toast
            toast({
              title: 'Signed Out',
              description: 'You have been successfully signed out.',
              status: 'info',
              duration: 3000,
              isClosable: true,
            });

            // 在实际应用中，这里应该清除本地存储的token
            // await AsyncStorage.removeItem('userToken');
            router.replace('/login');
          }
        }
      ]
    );
  };

  const handleNavigation = (title: string) => {
    toast({
      title: 'Coming Soon',
      description: `${title} settings will be available soon.`,
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <Box flex={1} bg={backgroundColor}>
      <ScrollView>
        {/* 🎨 Trekmate 4.0 - 标题 */}
        <VStack p={6} pt={16}>
          <Text
            fontSize="32px"
            fontWeight="700"
            color={textColor}
            textAlign="center"
            mb={2}
          >
            Settings
          </Text>
          <Text
            fontSize="16px"
            color={textColor}
            opacity={0.8}
            textAlign="center"
            mb={8}
          >
            Customize your Trekmate experience
          </Text>
        </VStack>

        {/* 🎨 Trekmate 4.0 - 用户资料 */}
        <Box
          mx={6}
          mb={6}
          p={6}
          bg={cardBackground}
          borderRadius="lg"
          shadow="sm"
        >
          <HStack alignItems="center" spacing={4}>
            <Avatar size="lg" bg={Colors.primary}>
              <Text color="white" fontSize="lg" fontWeight="600">
                TU
              </Text>
            </Avatar>
            <VStack flex={1}>
              <Text fontSize="lg" fontWeight="600" color={textColor}>
                Test User
              </Text>
              <Text fontSize="sm" color={textColor} opacity={0.7}>
                <EMAIL>
              </Text>
            </VStack>
          </HStack>
        </Box>

        {/* 🎨 Trekmate 4.0 - 设置项 */}
        <VStack spacing={4} px={6}>
          {/* 通知设置 */}
          <Box bg={cardBackground} borderRadius="lg" p={4}>
            <HStack justifyContent="space-between" alignItems="center">
              <HStack alignItems="center" spacing={3}>
                <Ionicons name="notifications-outline" size={24} color={Colors.primary} />
                <Text fontSize="md" fontWeight="500" color={textColor}>
                  Push Notifications
                </Text>
              </HStack>
              <Switch
                isChecked={notifications}
                onChange={setNotifications}
                colorScheme="brand"
              />
            </HStack>
          </Box>

          {/* 深色模式 */}
          <Box bg={cardBackground} borderRadius="lg" p={4}>
            <HStack justifyContent="space-between" alignItems="center">
              <HStack alignItems="center" spacing={3}>
                <Ionicons name="moon-outline" size={24} color={Colors.primary} />
                <Text fontSize="md" fontWeight="500" color={textColor}>
                  Dark Mode
                </Text>
              </HStack>
              <Switch
                isChecked={darkMode}
                onChange={setDarkMode}
                colorScheme="brand"
              />
            </HStack>
          </Box>

          {/* 其他设置 */}
          <TouchableOpacity onPress={() => handleNavigation('Currency')}>
            <Box bg={cardBackground} borderRadius="lg" p={4}>
              <HStack justifyContent="space-between" alignItems="center">
                <HStack alignItems="center" spacing={3}>
                  <Ionicons name="cash-outline" size={24} color={Colors.primary} />
                  <Text fontSize="md" fontWeight="500" color={textColor}>
                    Default Currency
                  </Text>
                </HStack>
                <Ionicons name="chevron-forward" size={20} color={textColor} />
              </HStack>
            </Box>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => handleNavigation('Language')}>
            <Box bg={cardBackground} borderRadius="lg" p={4}>
              <HStack justifyContent="space-between" alignItems="center">
                <HStack alignItems="center" spacing={3}>
                  <Ionicons name="language-outline" size={24} color={Colors.primary} />
                  <Text fontSize="md" fontWeight="500" color={textColor}>
                    Language & Region
                  </Text>
                </HStack>
                <Ionicons name="chevron-forward" size={20} color={textColor} />
              </HStack>
            </Box>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => handleNavigation('Privacy')}>
            <Box bg={cardBackground} borderRadius="lg" p={4}>
              <HStack justifyContent="space-between" alignItems="center">
                <HStack alignItems="center" spacing={3}>
                  <Ionicons name="shield-checkmark-outline" size={24} color={Colors.primary} />
                  <Text fontSize="md" fontWeight="500" color={textColor}>
                    Privacy & Security
                  </Text>
                </HStack>
                <Ionicons name="chevron-forward" size={20} color={textColor} />
              </HStack>
            </Box>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => handleNavigation('About')}>
            <Box bg={cardBackground} borderRadius="lg" p={4}>
              <HStack justifyContent="space-between" alignItems="center">
                <HStack alignItems="center" spacing={3}>
                  <Ionicons name="information-circle-outline" size={24} color={Colors.primary} />
                  <Text fontSize="md" fontWeight="500" color={textColor}>
                    About Trekmate
                  </Text>
                </HStack>
                <Ionicons name="chevron-forward" size={20} color={textColor} />
              </HStack>
            </Box>
          </TouchableOpacity>

          {/* 登出按钮 */}
          <Button
            onClick={handleLogout}
            colorScheme="red"
            variant="outline"
            size="lg"
            mt={4}
            leftIcon={<Ionicons name="log-out-outline" size={20} color="red" />}
          >
            Sign Out
          </Button>
        </VStack>

        {/* 版本信息 */}
        <VStack alignItems="center" p={6} mt={6}>
          <Text fontSize="sm" color={textColor} opacity={0.6}>
            Trekmate Version 4.0.0
          </Text>
          <Text fontSize="xs" color={textColor} opacity={0.6} mt={1}>
            Built with ❤️ for travelers
          </Text>
        </VStack>
      </ScrollView>
    </Box>
  );
}