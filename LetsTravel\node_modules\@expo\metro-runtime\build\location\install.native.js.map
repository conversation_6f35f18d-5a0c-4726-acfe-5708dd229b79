{"version": 3, "file": "install.native.js", "sourceRoot": "", "sources": ["../../src/location/install.native.ts"], "names": [], "mappings": ";;;;;AAAA,wFAAwF;AACxF,sDAAoD;AAEpD,oEAAuC;AAEvC,yCAAsD;AACtD,mEAA2C;AAE3C,IAAI,SAAS,GAAG,KAAK,CAAC;AAEtB,MAAM,QAAQ,GAAG,wBAAS,CAAC,UAAwC,CAAC;AAEpE,mEAAmE;AACnE,uEAAuE;AACvE,2EAA2E;AAC3E,SAAS,iCAAiC,CAAC,UAAkB;IAC3D,IAAI,SAAS,EAAE;QACb,OAAO;KACR;IACD,SAAS,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;QACpC,OAAO,CAAC,IAAI,CACV,+BAA+B,UAAU,+RAA+R,CACzU,CAAC;KACH;AACH,CAAC;AAED,sGAAsG;AACtG,+DAA+D;AAC/D,SAAS,UAAU;IACjB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;QACzC,8BAA8B;QAC9B,OAAO,IAAA,sBAAY,GAAE,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;KAC/C;IAED,kDAAkD;IAClD,MAAM,iBAAiB,GAAG,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IAE1D,IAAI,CAAC,iBAAiB,EAAE;QACtB,OAAO,IAAI,CAAC;KACb;IAED,2BAA2B;IAC3B,OAAO,iBAAiB,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,2BAA2B,CAAC,KAA0D;IAC7F,IAAI,KAAK,CAAC,0BAA0B,EAAE;QACpC,OAAO,KAAK,CAAC;KACd;IAED,MAAM,MAAM,GAAG,CAAC,GAAG,KAAY,EAAE,EAAE;QACjC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACxE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;gBACzC,iCAAiC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7C;YAED,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;SAClE;aAAM,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YACnD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACpF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;oBACzC,iCAAiC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC7C;gBAED,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;aAC1E;SACF;QACD,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;IACzB,CAAC,CAAC;IAEF,MAAM,CAAC,0BAA0B,GAAG,IAAI,CAAC;IAEzC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,IAAI,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,KAAK,KAAK,EAAE;IAC7C,+CAA+C;IAC/C,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;QACrD,MAAM,GAAG,GAAG,UAAU,EAAE,CAAC;QACzB,IAAI,GAAG,EAAE;YACP,IAAA,0BAAe,EAAC,GAAG,CAAC,CAAC;YACrB,IAAA,kBAAO,GAAE,CAAC;SACX;KACF;IACD,iDAAiD;IACjD,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE;QACrC,KAAK,EAAE,2BAA2B,CAAC,KAAK,CAAC;KAC1C,CAAC,CAAC;CACJ", "sourcesContent": ["// This MUST be first to ensure that `fetch` is defined in the React Native environment.\nimport 'react-native/Libraries/Core/InitializeCore';\n\nimport Constants from 'expo-constants';\n\nimport { install, setLocationHref } from './Location';\nimport getDevServer from '../getDevServer';\n\nlet hasWarned = false;\n\nconst manifest = Constants.expoConfig as Record<string, any> | null;\n\n// Add a development warning for fetch requests with relative paths\n// to ensure developers are aware of the need to configure a production\n// base URL in the Expo config (app.json) under `expo.extra.router.origin`.\nfunction warnProductionOriginNotConfigured(requestUrl: string) {\n  if (hasWarned) {\n    return;\n  }\n  hasWarned = true;\n  if (!manifest?.extra?.router?.origin) {\n    console.warn(\n      `The relative fetch request \"${requestUrl}\" will not work in production until the Expo Router Config Plugin (app.json) is configured with the \\`origin\\` prop set to the base URL of your web server, e.g. \\`{ plugins: [[\"expo-router\", { origin: \"...\" }]] }\\`. [Learn more](https://expo.github.io/router/docs/lab/runtime-location)`\n    );\n  }\n}\n\n// TODO: This would be better if native and tied as close to the JS engine as possible, i.e. it should\n// reflect the exact location of the JS file that was executed.\nfunction getBaseUrl() {\n  if (process.env.NODE_ENV !== 'production') {\n    // e.g. http://localhost:19006\n    return getDevServer().url?.replace(/\\/$/, '');\n  }\n\n  // TODO: Make it official by moving out of `extra`\n  const productionBaseUrl = manifest?.extra?.router?.origin;\n\n  if (!productionBaseUrl) {\n    return null;\n  }\n\n  // Ensure no trailing slash\n  return productionBaseUrl?.replace(/\\/$/, '');\n}\n\nfunction wrapFetchWithWindowLocation(fetch: Function & { __EXPO_BASE_URL_POLYFILLED?: boolean }) {\n  if (fetch.__EXPO_BASE_URL_POLYFILLED) {\n    return fetch;\n  }\n\n  const _fetch = (...props: any[]) => {\n    if (props[0] && typeof props[0] === 'string' && props[0].startsWith('/')) {\n      if (process.env.NODE_ENV !== 'production') {\n        warnProductionOriginNotConfigured(props[0]);\n      }\n\n      props[0] = new URL(props[0], window.location?.origin).toString();\n    } else if (props[0] && typeof props[0] === 'object') {\n      if (props[0].url && typeof props[0].url === 'string' && props[0].url.startsWith('/')) {\n        if (process.env.NODE_ENV !== 'production') {\n          warnProductionOriginNotConfigured(props[0]);\n        }\n\n        props[0].url = new URL(props[0].url, window.location?.origin).toString();\n      }\n    }\n    return fetch(...props);\n  };\n\n  _fetch.__EXPO_BASE_URL_POLYFILLED = true;\n\n  return _fetch;\n}\n\nif (manifest?.extra?.router?.origin !== false) {\n  // Polyfill window.location in native runtimes.\n  if (typeof window !== 'undefined' && !window.location) {\n    const url = getBaseUrl();\n    if (url) {\n      setLocationHref(url);\n      install();\n    }\n  }\n  // Polyfill native fetch to support relative URLs\n  Object.defineProperty(global, 'fetch', {\n    value: wrapFetchWithWindowLocation(fetch),\n  });\n}\n"]}