import React from 'react';
import { View, StyleSheet } from 'react-native';
import ARCoreTextTranslation from '../components/ARCoreTextTranslation';
import { router } from 'expo-router';

const ARScreen = () => {
  const handleClose = () => {
    router.back();
  };
  
  return (
    <View style={styles.container}>
      <ARCoreTextTranslation 
        isVisible={true}
        onClose={handleClose}
        enableTranslation={true}
        translationTarget="zh-CN"
        enableCurrency={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
});

export default ARScreen; 