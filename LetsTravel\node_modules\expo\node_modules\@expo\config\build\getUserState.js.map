{"version": 3, "file": "getUserState.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_getenv", "_os", "path", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "getExpoHomeDirectory", "home", "homedir", "process", "env", "__UNSAFE_EXPO_HOME_DIRECTORY", "boolish", "join", "getUserStatePath", "getUserState", "JsonFile", "jsonParseE<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantReadFileDefault"], "sources": ["../src/getUserState.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport { boolish } from 'getenv';\nimport { homedir } from 'os';\nimport * as path from 'path';\n\nexport type UserSettingsData = {\n  developmentCodeSigningId?: string;\n  appleId?: string;\n  accessToken?: string;\n  auth?: UserData | null;\n  ignoreBundledBinaries?: string[];\n  openDevToolsAtStartup?: boolean;\n  PATH?: string;\n  sendTo?: string;\n  uuid?: string;\n};\n\nexport type UserData = {\n  appleId?: string;\n  userId?: string;\n  username?: string;\n  currentConnection?: ConnectionType;\n  sessionSecret?: string;\n};\n\nexport type ConnectionType =\n  | 'Access-Token-Authentication'\n  | 'Username-Password-Authentication'\n  | 'facebook'\n  | 'google-oauth2'\n  | 'github';\n\n// The ~/.expo directory is used to store authentication sessions,\n// which are shared between EAS CLI and Expo CLI.\nexport function getExpoHomeDirectory() {\n  const home = homedir();\n\n  if (process.env.__UNSAFE_EXPO_HOME_DIRECTORY) {\n    return process.env.__UNSAFE_EXPO_HOME_DIRECTORY;\n  } else if (boolish('EXPO_STAGING', false)) {\n    return path.join(home, '.expo-staging');\n  } else if (boolish('EXPO_LOCAL', false)) {\n    return path.join(home, '.expo-local');\n  }\n  return path.join(home, '.expo');\n}\n\nexport function getUserStatePath() {\n  return path.join(getExpoHomeDirectory(), 'state.json');\n}\n\nexport function getUserState() {\n  return new JsonFile<UserSettingsData>(getUserStatePath(), {\n    jsonParseErrorDefault: {},\n    // This will ensure that an error isn't thrown if the file doesn't exist.\n    cantReadFileDefault: {},\n  });\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,KAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6B,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAf,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AA6B7B;AACA;AACO,SAASmB,oBAAoBA,CAAA,EAAG;EACrC,MAAMC,IAAI,GAAG,IAAAC,aAAO,EAAC,CAAC;EAEtB,IAAIC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;IAC5C,OAAOF,OAAO,CAACC,GAAG,CAACC,4BAA4B;EACjD,CAAC,MAAM,IAAI,IAAAC,iBAAO,EAAC,cAAc,EAAE,KAAK,CAAC,EAAE;IACzC,OAAO5B,IAAI,CAAD,CAAC,CAAC6B,IAAI,CAACN,IAAI,EAAE,eAAe,CAAC;EACzC,CAAC,MAAM,IAAI,IAAAK,iBAAO,EAAC,YAAY,EAAE,KAAK,CAAC,EAAE;IACvC,OAAO5B,IAAI,CAAD,CAAC,CAAC6B,IAAI,CAACN,IAAI,EAAE,aAAa,CAAC;EACvC;EACA,OAAOvB,IAAI,CAAD,CAAC,CAAC6B,IAAI,CAACN,IAAI,EAAE,OAAO,CAAC;AACjC;AAEO,SAASO,gBAAgBA,CAAA,EAAG;EACjC,OAAO9B,IAAI,CAAD,CAAC,CAAC6B,IAAI,CAACP,oBAAoB,CAAC,CAAC,EAAE,YAAY,CAAC;AACxD;AAEO,SAASS,YAAYA,CAAA,EAAG;EAC7B,OAAO,KAAIC,mBAAQ,EAAmBF,gBAAgB,CAAC,CAAC,EAAE;IACxDG,qBAAqB,EAAE,CAAC,CAAC;IACzB;IACAC,mBAAmB,EAAE,CAAC;EACxB,CAAC,CAAC;AACJ", "ignoreList": []}