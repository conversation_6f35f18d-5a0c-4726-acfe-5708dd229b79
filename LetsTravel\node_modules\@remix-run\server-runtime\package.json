{"name": "@remix-run/server-runtime", "version": "2.16.8", "description": "Server runtime for Remix", "bugs": {"url": "https://github.com/remix-run/remix/issues"}, "repository": {"type": "git", "url": "https://github.com/remix-run/remix", "directory": "packages/remix-server-runtime"}, "license": "MIT", "sideEffects": false, "main": "dist/index.js", "typings": "dist/index.d.ts", "module": "dist/esm/index.js", "dependencies": {"@remix-run/router": "1.23.0", "@types/cookie": "^0.6.0", "@web3-storage/multipart-parser": "^1.0.0", "cookie": "^0.7.2", "set-cookie-parser": "^2.4.8", "source-map": "^0.7.3", "turbo-stream": "2.4.1"}, "devDependencies": {"@types/set-cookie-parser": "^2.4.1", "typescript": "^5.1.6"}, "peerDependencies": {"typescript": "^5.1.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "engines": {"node": ">=18.0.0"}, "files": ["dist/", "CHANGELOG.md", "LICENSE.md", "README.md"], "scripts": {"tsc": "tsc"}}