{"version": 3, "file": "router-store.d.ts", "sourceRoot": "", "sources": ["../../src/global-state/router-store.tsx"], "names": [], "mappings": "AAAA,OAAO,EACL,iCAAiC,EAGlC,MAAM,0BAA0B,CAAC;AAGlC,OAAO,EAAiC,aAAa,EAAY,MAAM,OAAO,CAAC;AAgB/E,OAAO,EAAE,SAAS,EAAyB,MAAM,qBAAqB,CAAC;AACvE,OAAO,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAErC,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,kBAAkB,EAAE,oBAAoB,EAAoB,MAAM,qBAAqB,CAAC;AAEjG,OAAO,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAG1C;;;;GAIG;AACH,qBAAa,WAAW;IACtB,SAAS,EAAG,SAAS,GAAG,IAAI,CAAC;IAC7B,aAAa,EAAG,aAAa,CAAC;IAC9B,OAAO,CAAC,EAAE,kBAAkB,CAAC;IAC7B,OAAO,CAAC,wBAAwB,CAAkB;IAElD,YAAY,CAAC,EAAE,WAAW,CAAC;IAC3B,SAAS,CAAC,EAAE,WAAW,CAAC;IACxB,SAAS,CAAC,EAAE,WAAW,CAAC;IACxB,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,0BAA0B,CAAC,EAAE,MAAM,CAAC;IAEpC,aAAa,EAAG,iCAAiC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;IACjF,yBAAyB,EAAG,MAAM,IAAI,CAAC;IAEvC,oBAAoB,YAAiB,IAAI,EAAI;IAC7C,gBAAgB,YAAiB,IAAI,EAAI;IAEzC,MAAM,MAAqB;IAC3B,eAAe,MAA8B;IAC7C,MAAM,MAAqB;IAC3B,SAAS,MAAwB;IACjC,IAAI,MAAmB;IACvB,OAAO,MAAsB;IAC7B,OAAO,MAAsB;IAC7B,UAAU,MAAyB;IACnC,UAAU,MAAyB;IACnC,SAAS,MAAwB;IACjC,QAAQ,MAAuB;IAE/B,UAAU,CACR,OAAO,EAAE,cAAc,EACvB,aAAa,EAAE,iCAAiC,CAAC,eAAe,CAAC,aAAa,CAAC,EAC/E,oBAAoB,GAAE,oBAAyB;IAqGjD,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,cAAQ;IAWjD,YAAY,CAAC,KAAK,EAAE,WAAW;IAgB/B,kBAAkB;IAIlB,uEAAuE;IACvE,oBAAoB,eAAgB,MAAM,IAAI,mBAG5C;IACF,gBAAgB,eAAgB,MAAM,IAAI,mBAGxC;IACF,QAAQ,aAEN;IACF,iBAAiB,oBAEf;IACF,iBAAiB,kBAEf;IAEF,OAAO;CAKR;AAED,eAAO,MAAM,KAAK,aAAoB,CAAC;AAEvC,wBAAgB,aAAa,gBAE5B;AAYD,wBAAgB,iBAAiB,gBAOhC;AAED,wBAAgB,iBAAiB,cAOhC;AAED,wBAAgB,uBAAuB,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,oBAAoB,eAK7F"}