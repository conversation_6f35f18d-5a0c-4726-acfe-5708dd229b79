{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/async-require/index.ts"], "names": [], "mappings": ";;AAAA;;;;;GAKG;AACH,2DAAwD;AAExD,aAAa;AACb,MAAM,CAAC,GAAG,MAAM,CAAC,uBAAuB,IAAI,EAAE,mBAAmB,CAAC,GAAG,IAAA,qCAAiB,GAAE,CAAC", "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { buildAsyncRequire } from './buildAsyncRequire';\n\n// @ts-ignore\nglobal[`${global.__METRO_GLOBAL_PREFIX__ ?? ''}__loadBundleAsync`] = buildAsyncRequire();\n"]}