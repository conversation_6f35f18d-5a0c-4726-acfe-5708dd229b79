{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/error-overlay/index.tsx"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAE1B,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;IACxB,MAAM,CAAC,YAAY,GAAG,UAAU,EAAE;QAChC,OAAO,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC;CACH;AAED,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE;IAC3E,6CAA6C;IAC7C,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;CACvC;AAED,SAAgB,gBAAgB,CAAC,IAA8B;IAC7D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;QACzC,OAAO,IAAI,CAAC;KACb;IAED,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,GACpC,OAAO,CAAC,6BAA6B,CAAiD,CAAC;IAEzF,OAAO,SAAS,YAAY,CAAC,KAAU;QACrC,OAAO,CACL,8BAAC,mBAAmB;YAClB,8BAAC,IAAI,OAAK,KAAK,GAAI,CACC,CACvB,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAfD,4CAeC", "sourcesContent": ["import React from 'react';\n\nif (!global.setImmediate) {\n  global.setImmediate = function (fn) {\n    return setTimeout(fn, 0);\n  };\n}\n\nif (process.env.NODE_ENV === 'development' && process.env.EXPO_OS === 'web') {\n  // Stack traces are big with React Navigation\n  require('./LogBox').default.install();\n}\n\nexport function withErrorOverlay(Comp: React.ComponentType<any>) {\n  if (process.env.NODE_ENV === 'production') {\n    return Comp;\n  }\n\n  const { default: ErrorToastContainer } =\n    require('./toast/ErrorToastContainer') as typeof import('./toast/ErrorToastContainer');\n\n  return function ErrorOverlay(props: any) {\n    return (\n      <ErrorToastContainer>\n        <Comp {...props} />\n      </ErrorToastContainer>\n    );\n  };\n}\n"]}