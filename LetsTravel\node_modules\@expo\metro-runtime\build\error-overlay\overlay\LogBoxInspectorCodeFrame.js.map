{"version": 3, "file": "LogBoxInspectorCodeFrame.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/overlay/LogBoxInspectorCodeFrame.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,kDAA0B;AAC1B,+CAAkE;AAElE,qEAAkE;AAElE,uDAA2C;AAC3C,qDAAkD;AAClD,+DAAiD;AACjD,+CAA4C;AAC5C,oEAAiE;AACjE,mFAA2D;AAI3D,SAAgB,wBAAwB,CAAC,EAAE,SAAS,EAA6B;IAC/E,IAAI,SAAS,IAAI,IAAI,EAAE;QACrB,OAAO,IAAI,CAAC;KACb;IAED,SAAS,WAAW;QAClB,OAAO,IAAA,6CAAqB,EAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IACnF,CAAC;IAED,SAAS,WAAW;QAClB,MAAM,QAAQ,GAAG,SAAS,EAAE,QAAQ,CAAC;QACrC,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,OAAO,KAAK,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,yCAAyC,GAAG,CAAC;SAC9F;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,8BAAC,+CAAsB,IAAC,OAAO,EAAC,QAAQ;QACtC,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,GAAG;YACrB,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACvB,8BAAC,yBAAU,IAAC,UAAU;oBACpB,8BAAC,oBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,OAAO,GAAI,CAC7C,CACR;YACP,8BAAC,2BAAY,IACX,eAAe,EAAE;oBACf,OAAO,EAAE,aAAa;oBACtB,OAAO,EAAE,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC;iBAC/C,EACD,KAAK,EAAE,MAAM,CAAC,MAAM,EACpB,OAAO,EAAE,GAAG,EAAE;oBACZ,IAAA,0BAAgB,EAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;gBACrE,CAAC;gBACD,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,QAAQ;oBACzB,WAAW,EAAE;oBACb,WAAW,EAAE,CACT,CACM,CACV,CACgB,CAC1B,CAAC;AACJ,CAAC;AA3CD,4DA2CC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,GAAG,EAAE;QACH,eAAe,EAAE,WAAW,CAAC,kBAAkB,EAAE;QACjD,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,SAAS;QACtB,UAAU,EAAE,EAAE;QACd,WAAW,EAAE,EAAE;QACf,SAAS,EAAE,CAAC;QACZ,YAAY,EAAE,CAAC;KAChB;IACD,KAAK,EAAE;QACL,OAAO,EAAE,EAAE;QACX,iBAAiB,EAAE,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC;QAChD,iBAAiB,EAAE,CAAC;KACrB;IACD,MAAM,EAAE;QACN,UAAU,EAAE,EAAE;QACd,aAAa,EAAE,EAAE;KAClB;IACD,OAAO,EAAE;QACP,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;QAClC,QAAQ,EAAE,EAAE;QACZ,kBAAkB,EAAE,KAAK;QACzB,UAAU,EAAE,EAAE;QACd,UAAU,EAAE,qBAAS;KACtB;IACD,QAAQ,EAAE;QACR,UAAU,EAAE,MAAM;QAClB,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC;QACpC,SAAS,EAAE,QAAQ;QACnB,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,EAAE;QACZ,kBAAkB,EAAE,KAAK;QACzB,UAAU,EAAE,qBAAS;KACtB;CACF,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React from 'react';\nimport { ScrollView, StyleSheet, Text, View } from 'react-native';\n\nimport { LogBoxInspectorSection } from './LogBoxInspectorSection';\nimport type { CodeFrame } from '../Data/parseLogBoxLog';\nimport { Ansi } from '../UI/AnsiHighlight';\nimport { LogBoxButton } from '../UI/LogBoxButton';\nimport * as LogBoxStyle from '../UI/LogBoxStyle';\nimport { CODE_FONT } from '../UI/constants';\nimport { formatProjectFilePath } from '../formatProjectFilePath';\nimport openFileInEditor from '../modules/openFileInEditor';\n\ndeclare const process: any;\n\nexport function LogBoxInspectorCodeFrame({ codeFrame }: { codeFrame?: CodeFrame }) {\n  if (codeFrame == null) {\n    return null;\n  }\n\n  function getFileName() {\n    return formatProjectFilePath(process.env.EXPO_PROJECT_ROOT, codeFrame?.fileName);\n  }\n\n  function getLocation() {\n    const location = codeFrame?.location;\n    if (location != null) {\n      return ` (${location.row}:${location.column + 1 /* Code frame columns are zero indexed */})`;\n    }\n\n    return null;\n  }\n\n  return (\n    <LogBoxInspectorSection heading=\"Source\">\n      <View style={styles.box}>\n        <View style={styles.frame}>\n          <ScrollView horizontal>\n            <Ansi style={styles.content} text={codeFrame.content} />\n          </ScrollView>\n        </View>\n        <LogBoxButton\n          backgroundColor={{\n            default: 'transparent',\n            pressed: LogBoxStyle.getBackgroundDarkColor(1),\n          }}\n          style={styles.button}\n          onPress={() => {\n            openFileInEditor(codeFrame.fileName, codeFrame.location?.row ?? 0);\n          }}>\n          <Text style={styles.fileText}>\n            {getFileName()}\n            {getLocation()}\n          </Text>\n        </LogBoxButton>\n      </View>\n    </LogBoxInspectorSection>\n  );\n}\n\nconst styles = StyleSheet.create({\n  box: {\n    backgroundColor: LogBoxStyle.getBackgroundColor(),\n    borderWidth: 1,\n    borderColor: '#323232',\n    marginLeft: 10,\n    marginRight: 10,\n    marginTop: 5,\n    borderRadius: 3,\n  },\n  frame: {\n    padding: 10,\n    borderBottomColor: LogBoxStyle.getTextColor(0.1),\n    borderBottomWidth: 1,\n  },\n  button: {\n    paddingTop: 10,\n    paddingBottom: 10,\n  },\n  content: {\n    color: LogBoxStyle.getTextColor(1),\n    fontSize: 12,\n    includeFontPadding: false,\n    lineHeight: 20,\n    fontFamily: CODE_FONT,\n  },\n  fileText: {\n    userSelect: 'none',\n    color: LogBoxStyle.getTextColor(0.5),\n    textAlign: 'center',\n    flex: 1,\n    fontSize: 16,\n    includeFontPadding: false,\n    fontFamily: CODE_FONT,\n  },\n});\n"]}