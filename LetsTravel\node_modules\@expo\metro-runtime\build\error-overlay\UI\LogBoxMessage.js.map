{"version": 3, "file": "LogBoxMessage.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/UI/LogBoxMessage.tsx"], "names": [], "mappings": ";;;;;;AAAA;;;;;;GAMG;AACH,kDAA0B;AAC1B,+CAA0D;AAW1D,MAAM,YAAY,GAAG,CAAC,OAAe,EAAE,EAAE,CACvC,OAAO,CAAC,OAAO,CAAC,mDAAmD,EAAE,EAAE,CAAC,CAAC;AAE3E,SAAgB,aAAa,CAAC,KAAY;IACxC,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAY,KAAK,CAAC,OAAO,CAAC;IAE1D,IAAI,KAAK,CAAC,SAAS,KAAK,IAAI,EAAE;QAC5B,OAAO,8BAAC,mBAAI,QAAE,YAAY,CAAC,OAAO,CAAC,CAAQ,CAAC;KAC7C;IAED,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;IACvE,MAAM,iBAAiB,GAAyB,KAAK,CAAC,KAAK,CAAC;IAC5D,MAAM,QAAQ,GAAkB,EAAE,CAAC;IACnC,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,MAAM,iBAAiB,GAAG,CAAC,GAAkB,EAAE,OAAe,EAAE,KAA4B,EAAE,EAAE;QAC9F,IAAI,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,EAAE;YAC3B,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC;SAChE;QAED,IAAI,MAAM,GAAG,SAAS,EAAE;YACtB,QAAQ,CAAC,IAAI,CACX,8BAAC,mBAAI,IAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,IACzB,YAAY,CACR,CACR,CAAC;SACH;QAED,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;QAC1E,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,YAAY,CAAC,MAAM,GAAG,UAAU,EAAE;YACpC,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;YAE9E,iBAAiB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;SAClC;QAED,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;QAElF,iBAAiB,CAAC,GAAG,GAAG,IAAI,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;QACnE,OAAO,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IACnD,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,IAAI,UAAU,GAAG,OAAO,CAAC,MAAM,EAAE;QAC/B,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC5C,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACnC;IAED,OAAO,8DAAG,QAAQ,CAAI,CAAC;AACzB,CAAC;AAlDD,sCAkDC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React from 'react';\nimport { StyleProp, Text, TextStyle } from 'react-native';\n\nimport type { Message } from '../Data/parseLogBoxLog';\n\ntype Props = {\n  message: Message;\n  style: StyleProp<TextStyle>;\n  plaintext?: boolean;\n  maxLength?: number;\n};\n\nconst cleanContent = (content: string) =>\n  content.replace(/^(TransformError |Warning: (Warning: )?|Error: )/g, '');\n\nexport function LogBoxMessage(props: Props): JSX.Element {\n  const { content, substitutions }: Message = props.message;\n\n  if (props.plaintext === true) {\n    return <Text>{cleanContent(content)}</Text>;\n  }\n\n  const maxLength = props.maxLength != null ? props.maxLength : Infinity;\n  const substitutionStyle: StyleProp<TextStyle> = props.style;\n  const elements: JSX.Element[] = [];\n  let length = 0;\n  const createUnderLength = (key: string | '-1', message: string, style?: StyleProp<TextStyle>) => {\n    let cleanMessage = cleanContent(message);\n\n    if (props.maxLength != null) {\n      cleanMessage = cleanMessage.slice(0, props.maxLength - length);\n    }\n\n    if (length < maxLength) {\n      elements.push(\n        <Text key={key} style={style}>\n          {cleanMessage}\n        </Text>\n      );\n    }\n\n    length += cleanMessage.length;\n  };\n\n  const lastOffset = substitutions.reduce((prevOffset, substitution, index) => {\n    const key = String(index);\n\n    if (substitution.offset > prevOffset) {\n      const prevPart = content.substr(prevOffset, substitution.offset - prevOffset);\n\n      createUnderLength(key, prevPart);\n    }\n\n    const substititionPart = content.substr(substitution.offset, substitution.length);\n\n    createUnderLength(key + '.5', substititionPart, substitutionStyle);\n    return substitution.offset + substitution.length;\n  }, 0);\n\n  if (lastOffset < content.length) {\n    const lastPart = content.substr(lastOffset);\n    createUnderLength('-1', lastPart);\n  }\n\n  return <>{elements}</>;\n}\n"]}