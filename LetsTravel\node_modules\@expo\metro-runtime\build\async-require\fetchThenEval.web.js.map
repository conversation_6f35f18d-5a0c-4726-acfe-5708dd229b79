{"version": 3, "file": "fetchThenEval.web.js", "sourceRoot": "", "sources": ["../../src/async-require/fetchThenEval.web.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,MAAM,UAAU,GACd,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,CAAC,aAAa;IACvD,CAAC,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,IAAI;IACzE,CAAC,CAAC,IAAI,CAAC;AAEX,qCAAqC;AACrC,SAAgB,kBAAkB,CAChC,GAAW,EACX,EACE,UAAU,EACV,KAAK,EACL,WAAW,MACsD,EAAE;IAErE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACjC,OAAO,OAAO,CAAC,mBAAmB,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;KAC7D;IACD,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC3C,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,UAAU;YAAE,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC;QACzC,IAAI,KAAK;YAAE,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC/C,+CAA+C;QAC/C,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;QAEjB,IAAI,WAAW,IAAI,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;YACzE,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;SAClC;QAED,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;YACnB,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC3D,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;QACF,kEAAkE;QAClE,MAAM,KAAK,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAEtC,iCAAiC;QACjC,MAAM,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE;YACtB,IAAI,KAAY,CAAC;YACjB,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;gBAC1B,KAAK,GAAG;oBACN,IAAI,EAAE,OAAO;oBACb,MAAM,EAAE;wBACN,mBAAmB;wBACnB,GAAG,EAAE,KAAK;qBACX;iBACF,CAAC;aACH;iBAAM;gBACL,KAAK,GAAG,EAAE,CAAC;aACZ;YAED,MAAM,SAAS,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC5E,mBAAmB;YACnB,MAAM,OAAO,GAAG,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC;YACnC,KAAK,CAAC,OAAO,GAAG,iBAAiB,GAAG,GAAG,GAAG,aAAa,GAAG,SAAS,GAAG,IAAI,GAAG,OAAO,GAAG,GAAG,CAAC;YAC3F,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;YACvB,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;YAExB,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC3D,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC,CAAC;QAEF,IAAI,MAAM,CAAC,GAAG,KAAK,UAAU,EAAE;YAC7B,qFAAqF;YACrF,sFAAsF;YACtF,oBAAoB;YACpB,OAAO,EAAE,CAAC;SACX;aAAM;YACL,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SACnC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAhED,gDAgEC;AAED,MAAM,iBAAkB,SAAQ,KAAK;IAC1B,IAAI,GAAG,mBAAmB,CAAC;IACpC,IAAI,CAAU;IACd,OAAO,CAAU;CAClB", "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nconst currentSrc =\n  typeof document !== 'undefined' && document.currentScript\n    ? ('src' in document.currentScript && document.currentScript.src) || null\n    : null;\n\n// Basically `__webpack_require__.l`.\nexport function fetchThenEvalAsync(\n  url: string,\n  {\n    scriptType,\n    nonce,\n    crossOrigin,\n  }: { scriptType?: string; nonce?: string; crossOrigin?: string } = {}\n): Promise<void> {\n  if (typeof window === 'undefined') {\n    return require('./fetchThenEvalJs').fetchThenEvalAsync(url);\n  }\n  return new Promise<void>((resolve, reject) => {\n    const script = document.createElement('script');\n    if (scriptType) script.type = scriptType;\n    if (nonce) script.setAttribute('nonce', nonce);\n    // script.setAttribute('data-expo-metro', ...);\n    script.src = url;\n\n    if (crossOrigin && script.src.indexOf(window.location.origin + '/') !== 0) {\n      script.crossOrigin = crossOrigin;\n    }\n\n    script.onload = () => {\n      script.parentNode && script.parentNode.removeChild(script);\n      resolve();\n    };\n    // Create a new error object to preserve the original stack trace.\n    const error = new AsyncRequireError();\n\n    // Server error or network error.\n    script.onerror = (ev) => {\n      let event: Event;\n      if (typeof ev === 'string') {\n        event = {\n          type: 'error',\n          target: {\n            // @ts-expect-error\n            src: event,\n          },\n        };\n      } else {\n        event = ev;\n      }\n\n      const errorType = event && (event.type === 'load' ? 'missing' : event.type);\n      // @ts-expect-error\n      const realSrc = event?.target?.src;\n      error.message = 'Loading module ' + url + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n      error.type = errorType;\n      error.request = realSrc;\n\n      script.parentNode && script.parentNode.removeChild(script);\n      reject(error);\n    };\n\n    if (script.src === currentSrc) {\n      // NOTE(kitten): We always prevent `fetchThenEval` from loading the \"current script\".\n      // This points at our entrypoint bundle, and we should never reload and reevaluate the\n      // entrypoint bundle\n      resolve();\n    } else {\n      document.head.appendChild(script);\n    }\n  });\n}\n\nclass AsyncRequireError extends Error {\n  readonly name = 'AsyncRequireError';\n  type?: string;\n  request?: string;\n}\n"]}