{"version": 3, "file": "plugin-resolver.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_findUp", "path", "_interopRequireWildcard", "_resolveFrom", "_errors", "_modules", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "obj", "pluginFileName", "exports", "findUpPackageJson", "root", "packageJson", "findUp", "sync", "cwd", "assert", "resolvePluginForModule", "projectRoot", "modulePath", "resolved", "resolveFrom", "silent", "PluginError", "moduleNameIsDirectFileReference", "isPluginFile", "filePath", "findUpPlugin", "pathIsFilePath", "name", "match", "slashCount", "split", "sep", "length", "startsWith", "resolveExpoPluginFile", "pluginModuleFile", "fileExists", "moduleRoot", "dirname", "pluginFile", "normalizeStaticPlugin", "plugin", "Array", "isArray", "undefined", "assertInternalProjectRoot", "resolveConfigPluginFunction", "pluginReference", "resolveConfigPluginFunctionWithInfo", "result", "requirePluginFile", "error", "SyntaxError", "learnMoreLink", "pluginError", "message", "stack", "resolveConfigPluginExport"], "sources": ["../../src/utils/plugin-resolver.ts"], "sourcesContent": ["import assert from 'assert';\nimport findUp from 'find-up';\nimport * as path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { PluginError } from './errors';\nimport { fileExists } from './modules';\nimport { ConfigPlugin, StaticPlugin } from '../Plugin.types';\n\n// Default plugin entry file name.\nexport const pluginFileName = 'app.plugin.js';\n\nfunction findUpPackageJson(root: string): string {\n  const packageJson = findUp.sync('package.json', { cwd: root });\n  assert(packageJson, `No package.json found for module \"${root}\"`);\n  return packageJson;\n}\n\nexport function resolvePluginForModule(projectRoot: string, modulePath: string) {\n  const resolved = resolveFrom.silent(projectRoot, modulePath);\n  if (!resolved) {\n    throw new PluginError(\n      `Failed to resolve plugin for module \"${modulePath}\" relative to \"${projectRoot}\"`,\n      'PLUGIN_NOT_FOUND'\n    );\n  }\n  // If the modulePath is something like `@bacon/package/index.js` or `expo-foo/build/app`\n  // then skip resolving the module `app.plugin.js`\n  if (moduleNameIsDirectFileReference(modulePath)) {\n    return { isPluginFile: false, filePath: resolved };\n  }\n  return findUpPlugin(resolved);\n}\n\n// TODO: Test windows\nfunction pathIsFilePath(name: string): boolean {\n  // Matches lines starting with: . / ~/\n  return !!name.match(/^(\\.|~\\/|\\/)/g);\n}\n\nexport function moduleNameIsDirectFileReference(name: string): boolean {\n  if (pathIsFilePath(name)) {\n    return true;\n  }\n\n  const slashCount = name.split(path.sep)?.length;\n  // Orgs (like @expo/config ) should have more than one slash to be a direct file.\n  if (name.startsWith('@')) {\n    return slashCount > 2;\n  }\n\n  // Regular packages should be considered direct reference if they have more than one slash.\n  return slashCount > 1;\n}\n\nfunction resolveExpoPluginFile(root: string): string | null {\n  // Find the expo plugin root file\n  const pluginModuleFile = resolveFrom.silent(\n    root,\n    // use ./ so it isn't resolved as a node module\n    `./${pluginFileName}`\n  );\n\n  // If the default expo plugin file exists use it.\n  if (pluginModuleFile && fileExists(pluginModuleFile)) {\n    return pluginModuleFile;\n  }\n  return null;\n}\n\nfunction findUpPlugin(root: string): { filePath: string; isPluginFile: boolean } {\n  // Get the closest package.json to the node module\n  const packageJson = findUpPackageJson(root);\n  // resolve the root folder for the node module\n  const moduleRoot = path.dirname(packageJson);\n  // use whatever the initial resolved file was ex: `node_modules/my-package/index.js` or `./something.js`\n  const pluginFile = resolveExpoPluginFile(moduleRoot);\n  return { filePath: pluginFile ?? root, isPluginFile: !!pluginFile };\n}\n\nexport function normalizeStaticPlugin(plugin: StaticPlugin | ConfigPlugin | string): StaticPlugin {\n  if (Array.isArray(plugin)) {\n    assert(\n      plugin.length > 0 && plugin.length < 3,\n      `Wrong number of arguments provided for static config plugin, expected either 1 or 2, got ${plugin.length}`\n    );\n    return plugin;\n  }\n  return [plugin, undefined];\n}\n\nexport function assertInternalProjectRoot(projectRoot?: string): asserts projectRoot {\n  assert(\n    projectRoot,\n    `Unexpected: Config \\`_internal.projectRoot\\` isn't defined by expo-cli, this is a bug.`\n  );\n}\n\n// Resolve the module function and assert type\nexport function resolveConfigPluginFunction(projectRoot: string, pluginReference: string) {\n  const { plugin } = resolveConfigPluginFunctionWithInfo(projectRoot, pluginReference);\n  return plugin;\n}\n\n// Resolve the module function and assert type\nexport function resolveConfigPluginFunctionWithInfo(projectRoot: string, pluginReference: string) {\n  const { filePath: pluginFile, isPluginFile } = resolvePluginForModule(\n    projectRoot,\n    pluginReference\n  );\n  let result: any;\n  try {\n    result = requirePluginFile(pluginFile);\n  } catch (error) {\n    if (error instanceof SyntaxError) {\n      const learnMoreLink = `Learn more: https://docs.expo.dev/guides/config-plugins/#creating-a-plugin`;\n      // If the plugin reference is a node module, and that node module has a syntax error, then it probably doesn't have an official config plugin.\n      if (!isPluginFile && !moduleNameIsDirectFileReference(pluginReference)) {\n        const pluginError = new PluginError(\n          `Package \"${pluginReference}\" does not contain a valid config plugin.\\n${learnMoreLink}\\n\\n${error.message}`,\n          'INVALID_PLUGIN_IMPORT'\n        );\n        pluginError.stack = error.stack;\n        throw pluginError;\n      }\n    }\n    throw error;\n  }\n\n  const plugin = resolveConfigPluginExport({\n    plugin: result,\n    pluginFile,\n    pluginReference,\n    isPluginFile,\n  });\n  return { plugin, pluginFile, pluginReference, isPluginFile };\n}\n\n/**\n * - Resolve the exported contents of an Expo config (be it default or module.exports)\n * - Assert no promise exports\n * - Return config type\n * - Serialize config\n *\n * @param props.plugin plugin results\n * @param props.pluginFile plugin file path\n * @param props.pluginReference the string used to reference the plugin\n * @param props.isPluginFile is file path from the app.plugin.js module root\n */\nexport function resolveConfigPluginExport({\n  plugin,\n  pluginFile,\n  pluginReference,\n  isPluginFile,\n}: {\n  plugin: any;\n  pluginFile: string;\n  pluginReference: string;\n  isPluginFile: boolean;\n}): ConfigPlugin<unknown> {\n  if (plugin.default != null) {\n    plugin = plugin.default;\n  }\n  if (typeof plugin !== 'function') {\n    const learnMoreLink = `Learn more: https://docs.expo.dev/guides/config-plugins/#creating-a-plugin`;\n    // If the plugin reference is a node module, and that node module does not export a function then it probably doesn't have a config plugin.\n    if (!isPluginFile && !moduleNameIsDirectFileReference(pluginReference)) {\n      throw new PluginError(\n        `Package \"${pluginReference}\" does not contain a valid config plugin. Module must export a function from file: ${pluginFile}\\n${learnMoreLink}`,\n        'INVALID_PLUGIN_TYPE'\n      );\n    }\n    throw new PluginError(\n      `Plugin \"${pluginReference}\" must export a function from file: ${pluginFile}. ${learnMoreLink}`,\n      'INVALID_PLUGIN_TYPE'\n    );\n  }\n\n  return plugin;\n}\n\nfunction requirePluginFile(filePath: string): any {\n  try {\n    return require(filePath);\n  } catch (error) {\n    // TODO: Improve error messages\n    throw error;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,KAAA;EAAA,MAAAJ,IAAA,GAAAK,uBAAA,CAAAH,OAAA;EAAAE,IAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,aAAA;EAAA,MAAAN,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAI,YAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,SAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,QAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAS,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAjB,uBAAA6B,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAhB,UAAA,GAAAgB,GAAA,KAAAf,OAAA,EAAAe,GAAA;AAGvC;AACO,MAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,eAAe;AAE7C,SAASE,iBAAiBA,CAACC,IAAY,EAAU;EAC/C,MAAMC,WAAW,GAAGC,iBAAM,CAACC,IAAI,CAAC,cAAc,EAAE;IAAEC,GAAG,EAAEJ;EAAK,CAAC,CAAC;EAC9D,IAAAK,iBAAM,EAACJ,WAAW,EAAE,qCAAqCD,IAAI,GAAG,CAAC;EACjE,OAAOC,WAAW;AACpB;AAEO,SAASK,sBAAsBA,CAACC,WAAmB,EAAEC,UAAkB,EAAE;EAC9E,MAAMC,QAAQ,GAAGC,sBAAW,CAACC,MAAM,CAACJ,WAAW,EAAEC,UAAU,CAAC;EAC5D,IAAI,CAACC,QAAQ,EAAE;IACb,MAAM,KAAIG,qBAAW,EACnB,wCAAwCJ,UAAU,kBAAkBD,WAAW,GAAG,EAClF,kBACF,CAAC;EACH;EACA;EACA;EACA,IAAIM,+BAA+B,CAACL,UAAU,CAAC,EAAE;IAC/C,OAAO;MAAEM,YAAY,EAAE,KAAK;MAAEC,QAAQ,EAAEN;IAAS,CAAC;EACpD;EACA,OAAOO,YAAY,CAACP,QAAQ,CAAC;AAC/B;;AAEA;AACA,SAASQ,cAAcA,CAACC,IAAY,EAAW;EAC7C;EACA,OAAO,CAAC,CAACA,IAAI,CAACC,KAAK,CAAC,eAAe,CAAC;AACtC;AAEO,SAASN,+BAA+BA,CAACK,IAAY,EAAW;EACrE,IAAID,cAAc,CAACC,IAAI,CAAC,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,MAAME,UAAU,GAAGF,IAAI,CAACG,KAAK,CAACnD,IAAI,CAAD,CAAC,CAACoD,GAAG,CAAC,EAAEC,MAAM;EAC/C;EACA,IAAIL,IAAI,CAACM,UAAU,CAAC,GAAG,CAAC,EAAE;IACxB,OAAOJ,UAAU,GAAG,CAAC;EACvB;;EAEA;EACA,OAAOA,UAAU,GAAG,CAAC;AACvB;AAEA,SAASK,qBAAqBA,CAACzB,IAAY,EAAiB;EAC1D;EACA,MAAM0B,gBAAgB,GAAGhB,sBAAW,CAACC,MAAM,CACzCX,IAAI;EACJ;EACA,KAAKH,cAAc,EACrB,CAAC;;EAED;EACA,IAAI6B,gBAAgB,IAAI,IAAAC,qBAAU,EAACD,gBAAgB,CAAC,EAAE;IACpD,OAAOA,gBAAgB;EACzB;EACA,OAAO,IAAI;AACb;AAEA,SAASV,YAAYA,CAAChB,IAAY,EAA+C;EAC/E;EACA,MAAMC,WAAW,GAAGF,iBAAiB,CAACC,IAAI,CAAC;EAC3C;EACA,MAAM4B,UAAU,GAAG1D,IAAI,CAAD,CAAC,CAAC2D,OAAO,CAAC5B,WAAW,CAAC;EAC5C;EACA,MAAM6B,UAAU,GAAGL,qBAAqB,CAACG,UAAU,CAAC;EACpD,OAAO;IAAEb,QAAQ,EAAEe,UAAU,IAAI9B,IAAI;IAAEc,YAAY,EAAE,CAAC,CAACgB;EAAW,CAAC;AACrE;AAEO,SAASC,qBAAqBA,CAACC,MAA4C,EAAgB;EAChG,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACzB,IAAA3B,iBAAM,EACJ2B,MAAM,CAACT,MAAM,GAAG,CAAC,IAAIS,MAAM,CAACT,MAAM,GAAG,CAAC,EACtC,4FAA4FS,MAAM,CAACT,MAAM,EAC3G,CAAC;IACD,OAAOS,MAAM;EACf;EACA,OAAO,CAACA,MAAM,EAAEG,SAAS,CAAC;AAC5B;AAEO,SAASC,yBAAyBA,CAAC7B,WAAoB,EAAuB;EACnF,IAAAF,iBAAM,EACJE,WAAW,EACX,wFACF,CAAC;AACH;;AAEA;AACO,SAAS8B,2BAA2BA,CAAC9B,WAAmB,EAAE+B,eAAuB,EAAE;EACxF,MAAM;IAAEN;EAAO,CAAC,GAAGO,mCAAmC,CAAChC,WAAW,EAAE+B,eAAe,CAAC;EACpF,OAAON,MAAM;AACf;;AAEA;AACO,SAASO,mCAAmCA,CAAChC,WAAmB,EAAE+B,eAAuB,EAAE;EAChG,MAAM;IAAEvB,QAAQ,EAAEe,UAAU;IAAEhB;EAAa,CAAC,GAAGR,sBAAsB,CACnEC,WAAW,EACX+B,eACF,CAAC;EACD,IAAIE,MAAW;EACf,IAAI;IACFA,MAAM,GAAGC,iBAAiB,CAACX,UAAU,CAAC;EACxC,CAAC,CAAC,OAAOY,KAAK,EAAE;IACd,IAAIA,KAAK,YAAYC,WAAW,EAAE;MAChC,MAAMC,aAAa,GAAG,4EAA4E;MAClG;MACA,IAAI,CAAC9B,YAAY,IAAI,CAACD,+BAA+B,CAACyB,eAAe,CAAC,EAAE;QACtE,MAAMO,WAAW,GAAG,KAAIjC,qBAAW,EACjC,YAAY0B,eAAe,8CAA8CM,aAAa,OAAOF,KAAK,CAACI,OAAO,EAAE,EAC5G,uBACF,CAAC;QACDD,WAAW,CAACE,KAAK,GAAGL,KAAK,CAACK,KAAK;QAC/B,MAAMF,WAAW;MACnB;IACF;IACA,MAAMH,KAAK;EACb;EAEA,MAAMV,MAAM,GAAGgB,yBAAyB,CAAC;IACvChB,MAAM,EAAEQ,MAAM;IACdV,UAAU;IACVQ,eAAe;IACfxB;EACF,CAAC,CAAC;EACF,OAAO;IAAEkB,MAAM;IAAEF,UAAU;IAAEQ,eAAe;IAAExB;EAAa,CAAC;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASkC,yBAAyBA,CAAC;EACxChB,MAAM;EACNF,UAAU;EACVQ,eAAe;EACfxB;AAMF,CAAC,EAAyB;EACxB,IAAIkB,MAAM,CAACnD,OAAO,IAAI,IAAI,EAAE;IAC1BmD,MAAM,GAAGA,MAAM,CAACnD,OAAO;EACzB;EACA,IAAI,OAAOmD,MAAM,KAAK,UAAU,EAAE;IAChC,MAAMY,aAAa,GAAG,4EAA4E;IAClG;IACA,IAAI,CAAC9B,YAAY,IAAI,CAACD,+BAA+B,CAACyB,eAAe,CAAC,EAAE;MACtE,MAAM,KAAI1B,qBAAW,EACnB,YAAY0B,eAAe,sFAAsFR,UAAU,KAAKc,aAAa,EAAE,EAC/I,qBACF,CAAC;IACH;IACA,MAAM,KAAIhC,qBAAW,EACnB,WAAW0B,eAAe,uCAAuCR,UAAU,KAAKc,aAAa,EAAE,EAC/F,qBACF,CAAC;EACH;EAEA,OAAOZ,MAAM;AACf;AAEA,SAASS,iBAAiBA,CAAC1B,QAAgB,EAAO;EAChD,IAAI;IACF,OAAO/C,OAAO,CAAC+C,QAAQ,CAAC;EAC1B,CAAC,CAAC,OAAO2B,KAAK,EAAE;IACd;IACA,MAAMA,KAAK;EACb;AACF", "ignoreList": []}