{"version": 3, "file": "routing.js", "sourceRoot": "", "sources": ["../../src/global-state/routing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAA4F;AAC5F,sDAAwC;AACxC,kDAA2C;AAI3C,uCAA2C;AAC3C,uCAAuC;AACvC,0CAA+C;AAE/C,sCAAoD;AAEpD,SAAS,aAAa,CAAC,KAAkB;IACvC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE;QAClC,MAAM,IAAI,KAAK,CACb,gKAAgK,CACjK,CAAC;KACH;AACH,CAAC;AAED,SAAgB,QAAQ,CAAoB,GAAS;IACnD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;AACnD,CAAC;AAFD,4BAEC;AAED,SAAgB,IAAI,CAAoB,GAAS;IAC/C,OAAO,IAAI,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/C,CAAC;AAFD,oBAEC;AAED,SAAgB,OAAO,CAAoB,KAAc;IACvD,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,qBAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACxD,CAAC;AAFD,0BAEC;AAED,SAAgB,OAAO,CAAoB,GAAS;IAClD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC;AAClD,CAAC;AAFD,0BAEC;AAED,SAAgB,UAAU;IACxB,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,qBAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;AACxD,CAAC;AAFD,gCAEC;AAED,SAAgB,MAAM;IACpB,aAAa,CAAC,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AACxC,CAAC;AAHD,wBAGC;AAED,SAAgB,SAAS;IACvB,oEAAoE;IACpE,2EAA2E;IAC3E,8FAA8F;IAC9F,yEAAyE;IACzE,uCAAuC;IACvC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE;QACjC,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,KAAK,CAAC;AAC3D,CAAC;AAVD,8BAUC;AAED,SAAgB,UAAU;IACxB,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;IAE3B,sFAAsF;IACtF,OAAO,KAAK,EAAE;QACZ,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,OAAO,IAAI,CAAC;SACb;QACD,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QAE5C,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAY,CAAC;KACnD;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAdD,gCAcC;AAED,SAAgB,SAAS,CAAoB,SAA0C,EAAE;IACvF,aAAa,CAAC,IAAI,CAAC,CAAC;IACpB,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,SAAiB,CAAA,CAAC,MAAM,CAAC,CAAC;AACjE,CAAC;AAHD,8BAGC;AAED,SAAgB,MAAM,CAAoB,IAAY,EAAE,KAAc;IACpE,IAAI,IAAA,0BAAoB,EAAC,IAAI,CAAC,EAAE;QAC9B,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO;KACR;IAED,aAAa,CAAC,IAAI,CAAC,CAAC;IACpB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;IAEjD,IAAI,aAAa,IAAI,IAAI,EAAE;QACzB,MAAM,IAAI,KAAK,CACb,kFAAkF,CACnF,CAAC;KACH;IAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACjB,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;KAC1E;IAED,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;QACnC,aAAa,CAAC,MAAM,EAAE,CAAC;QACvB,OAAO;KACR;IAED,MAAM,SAAS,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;IAE/C,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACxB,oEAAoE;QACpE,IAAI,IAAI,GACN,IAAI,CAAC,SAAS,EAAE,QAAQ;YACtB,EAAE,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC;gBAAE,OAAO,OAAO,CAAC;YAE7C,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBAC9B,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC;gBACjD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACzB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACzB;qBAAM;oBACL,OAAO,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;iBAC5C;aACF;iBAAM;gBACL,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC/B,OAAO,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC;aAC1C;QACH,CAAC,CAAC;aACD,MAAM,CAAC,OAAO,CAAC;aACf,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;YAC5B,IAAI,IAAI,KAAK,CAAC;SACf;QAED,IAAI,GAAG,IAAA,cAAO,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KAC5B;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAExE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvC,OAAO,CAAC,KAAK,CAAC,kEAAkE,GAAG,IAAI,CAAC,CAAC;QACzF,OAAO;KACR;IAED,OAAO,aAAa,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;AAC5E,CAAC;AAhED,wBAgEC;AAED,SAAS,iBAAiB,CACxB,WAAwB,EACxB,eAAgC,EAChC,IAAI,GAAG,UAAU;IAEjB;;;;;;;;;;;;;OAaG;IACH,IAAI,gBAA+C,CAAC;IAEpD,4GAA4G;IAC5G,OAAO,WAAW,IAAI,eAAe,EAAE;QACrC,MAAM,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAEjE,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAErE,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC;QAC1C,MAAM,mBAAmB,GAAG,UAAU,CAAC,KAAK,CAAC;QAE7C,MAAM,WAAW,GAAG,IAAA,2BAAgB,EAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAE5D,MAAM,+BAA+B,GACnC,gBAAgB,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI;YACzC,CAAC,UAAU;YACX,CAAC,mBAAmB;YACpB,CAAC,WAAW,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;QAE/F,IAAI,+BAA+B,EAAE;YACnC,MAAM;SACP;QAED,WAAW,GAAG,UAAU,CAAC;QACzB,eAAe,GAAG,mBAAsC,CAAC;KAC1D;IAED;;;OAGG;IACH,MAAM,WAAW,GAAwB,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACxD,IAAI,OAAO,GAAG,WAAW,CAAC;IAC1B,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAE5B,+EAA+E;IAC/E,OAAO,gBAAgB,EAAE;QACvB,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QACzE,wCAAwC;QACxC,OAAO,CAAC,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC;QACvC,yDAAyD;QACzD,OAAO,CAAC,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAC/B,8DAA8D;QAC9D,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEhC,iCAAiC;QACjC,uFAAuF;QACvF,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QACzB,MAAM,GAAG,OAAO,CAAC;QAEjB,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC9F;IAED,mGAAmG;IACnG,IAAI,IAAI,KAAK,MAAM,EAAE;QACnB,8FAA8F;QAC9F,IAAI,GAAG,UAAU,CAAC;QAElB;;;;;;;;;;;;;WAaG;QACH,IAAI,eAAe,CAAC,IAAI,KAAK,OAAO,EAAE;YACpC,WAAW,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC,IAAI,IAAI,IAAA,mBAAM,GAAE,EAAE,CAAC,CAAC,yJAAyJ;SAC/M;KACF;IAED,IAAI,IAAI,KAAK,SAAS,IAAI,eAAe,CAAC,IAAI,KAAK,KAAK,EAAE;QACxD,IAAI,GAAG,SAAS,CAAC;KAClB;IAED,OAAO;QACL,IAAI;QACJ,MAAM,EAAE,eAAe,CAAC,GAAG;QAC3B,OAAO,EAAE;YACP,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,IAAI,EAAE,WAAW,CAAC,MAAM;YACxB,MAAM,EAAE,WAAW,CAAC,MAAM;SAC3B;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import { StackActions, type NavigationState, PartialRoute } from '@react-navigation/native';\nimport * as Linking from 'expo-linking';\nimport { nanoid } from 'nanoid/non-secure';\n\nimport { type RouterStore } from './router-store';\nimport { ResultState } from '../fork/getStateFromPath';\nimport { resolveHref } from '../link/href';\nimport { resolve } from '../link/path';\nimport { matchDynamicName } from '../matchers';\nimport { Href } from '../types';\nimport { shouldLinkExternally } from '../utils/url';\n\nfunction assertIsReady(store: RouterStore) {\n  if (!store.navigationRef.isReady()) {\n    throw new Error(\n      'Attempted to navigate before mounting the Root Layout component. Ensure the Root Layout component is rendering a Slot, or other navigator on the first render.'\n    );\n  }\n}\n\nexport function navigate(this: RouterStore, url: Href) {\n  return this.linkTo(resolveHref(url), 'NAVIGATE');\n}\n\nexport function push(this: RouterStore, url: Href) {\n  return this.linkTo(resolveHref(url), 'PUSH');\n}\n\nexport function dismiss(this: RouterStore, count?: number) {\n  this.navigationRef?.dispatch(StackActions.pop(count));\n}\n\nexport function replace(this: RouterStore, url: Href) {\n  return this.linkTo(resolveHref(url), 'REPLACE');\n}\n\nexport function dismissAll(this: RouterStore) {\n  this.navigationRef?.dispatch(StackActions.popToTop());\n}\n\nexport function goBack(this: RouterStore) {\n  assertIsReady(this);\n  this.navigationRef?.current?.goBack();\n}\n\nexport function canGoBack(this: RouterStore): boolean {\n  // Return a default value here if the navigation hasn't mounted yet.\n  // This can happen if the user calls `canGoBack` from the Root Layout route\n  // before mounting a navigator. This behavior exists due to React Navigation being dynamically\n  // constructed at runtime. We can get rid of this in the future if we use\n  // the static configuration internally.\n  if (!this.navigationRef.isReady()) {\n    return false;\n  }\n  return this.navigationRef?.current?.canGoBack() ?? false;\n}\n\nexport function canDismiss(this: RouterStore): boolean {\n  let state = this.rootState;\n\n  // Keep traversing down the state tree until we find a stack navigator that we can pop\n  while (state) {\n    if (state.type === 'stack' && state.routes.length > 1) {\n      return true;\n    }\n    if (state.index === undefined) return false;\n\n    state = state.routes?.[state.index]?.state as any;\n  }\n\n  return false;\n}\n\nexport function setParams(this: RouterStore, params: Record<string, string | number> = {}) {\n  assertIsReady(this);\n  return (this.navigationRef?.current?.setParams as any)(params);\n}\n\nexport function linkTo(this: RouterStore, href: string, event?: string) {\n  if (shouldLinkExternally(href)) {\n    Linking.openURL(href);\n    return;\n  }\n\n  assertIsReady(this);\n  const navigationRef = this.navigationRef.current;\n\n  if (navigationRef == null) {\n    throw new Error(\n      \"Couldn't find a navigation object. Is your component inside NavigationContainer?\"\n    );\n  }\n\n  if (!this.linking) {\n    throw new Error('Attempted to link to route when no routes are present');\n  }\n\n  if (href === '..' || href === '../') {\n    navigationRef.goBack();\n    return;\n  }\n\n  const rootState = navigationRef.getRootState();\n\n  if (href.startsWith('.')) {\n    // Resolve base path by merging the current segments with the params\n    let base =\n      this.routeInfo?.segments\n        ?.map((segment) => {\n          if (!segment.startsWith('[')) return segment;\n\n          if (segment.startsWith('[...')) {\n            segment = segment.slice(4, -1);\n            const params = this.routeInfo?.params?.[segment];\n            if (Array.isArray(params)) {\n              return params.join('/');\n            } else {\n              return params?.split(',')?.join('/') ?? '';\n            }\n          } else {\n            segment = segment.slice(1, -1);\n            return this.routeInfo?.params?.[segment];\n          }\n        })\n        .filter(Boolean)\n        .join('/') ?? '/';\n\n    if (!this.routeInfo?.isIndex) {\n      base += '/..';\n    }\n\n    href = resolve(base, href);\n  }\n\n  const state = this.linking.getStateFromPath!(href, this.linking.config);\n\n  if (!state || state.routes.length === 0) {\n    console.error('Could not generate a valid navigation state for the given path: ' + href);\n    return;\n  }\n\n  return navigationRef.dispatch(getNavigateAction(state, rootState, event));\n}\n\nfunction getNavigateAction(\n  actionState: ResultState,\n  navigationState: NavigationState,\n  type = 'NAVIGATE'\n) {\n  /**\n   * We need to find the deepest navigator where the action and current state diverge, If they do not diverge, the\n   * lowest navigator is the target.\n   *\n   * By default React Navigation will target the current navigator, but this doesn't work for all actions\n   * For example:\n   *  - /deeply/nested/route -> /top-level-route the target needs to be the top-level navigator\n   *  - /stack/nestedStack/page -> /stack1/nestedStack/other-page needs to target the nestedStack navigator\n   *\n   * This matching needs to done by comparing the route names and the dynamic path, for example\n   * - /1/page -> /2/anotherPage needs to target the /[id] navigator\n   *\n   * Other parameters such as search params and hash are not evaluated.\n   */\n  let actionStateRoute: PartialRoute<any> | undefined;\n\n  // Traverse the state tree comparing the current state and the action state until we find where they diverge\n  while (actionState && navigationState) {\n    const stateRoute = navigationState.routes[navigationState.index];\n\n    actionStateRoute = actionState.routes[actionState.routes.length - 1];\n\n    const childState = actionStateRoute.state;\n    const nextNavigationState = stateRoute.state;\n\n    const dynamicName = matchDynamicName(actionStateRoute.name);\n\n    const didActionAndCurrentStateDiverge =\n      actionStateRoute.name !== stateRoute.name ||\n      !childState ||\n      !nextNavigationState ||\n      (dynamicName && actionStateRoute.params?.[dynamicName] !== stateRoute.params?.[dynamicName]);\n\n    if (didActionAndCurrentStateDiverge) {\n      break;\n    }\n\n    actionState = childState;\n    navigationState = nextNavigationState as NavigationState;\n  }\n\n  /*\n   * We found the target navigator, but the payload is in the incorrect format\n   * We need to convert the action state to a payload that can be dispatched\n   */\n  const rootPayload: Record<string, any> = { params: {} };\n  let payload = rootPayload;\n  let params = payload.params;\n\n  // The root level of payload is a bit weird, its params are in the child object\n  while (actionStateRoute) {\n    Object.assign(params, { ...payload.params, ...actionStateRoute.params });\n    // Assign the screen name to the payload\n    payload.screen = actionStateRoute.name;\n    // Merge the params, ensuring that we create a new object\n    payload.params = { ...params };\n    // Params don't include the screen, thats a separate attribute\n    delete payload.params['screen'];\n\n    // Continue down the payload tree\n    // Initially these values are separate, but React Nav merges them after the first layer\n    payload = payload.params;\n    params = payload;\n\n    actionStateRoute = actionStateRoute.state?.routes[actionStateRoute.state?.routes.length - 1];\n  }\n\n  // Expo Router uses only three actions, but these don't directly translate to all navigator actions\n  if (type === 'PUSH') {\n    // Only stack navigators have a push action, and even then we want to use NAVIGATE (see below)\n    type = 'NAVIGATE';\n\n    /*\n     * The StackAction.PUSH does not work correctly with Expo Router.\n     *\n     * Expo Router provides a getId() function for every route, altering how React Navigation handles stack routing.\n     * Ordinarily, PUSH always adds a new screen to the stack. However, with getId() present, it navigates to the screen with the matching ID instead (by moving the screen to the top of the stack)\n     * When you try and push to a screen with the same ID, no navigation will occur\n     * Refer to: https://github.com/react-navigation/react-navigation/blob/13d4aa270b301faf07960b4cd861ffc91e9b2c46/packages/routers/src/StackRouter.tsx#L279-L290\n     *\n     * Expo Router needs to retain the default behavior of PUSH, consistently adding new screens to the stack, even if their IDs are identical.\n     *\n     * To resolve this issue, we switch to using a NAVIGATE action with a new key. In the navigate action, screens are matched by either key or getId() function.\n     * By generating a unique new key, we ensure that the screen is always pushed onto the stack.\n     *\n     */\n    if (navigationState.type === 'stack') {\n      rootPayload.key = `${rootPayload.name}-${nanoid()}`; // @see https://github.com/react-navigation/react-navigation/blob/13d4aa270b301faf07960b4cd861ffc91e9b2c46/packages/routers/src/StackRouter.tsx#L406-L407\n    }\n  }\n\n  if (type === 'REPLACE' && navigationState.type === 'tab') {\n    type = 'JUMP_TO';\n  }\n\n  return {\n    type,\n    target: navigationState.key,\n    payload: {\n      key: rootPayload.key,\n      name: rootPayload.screen,\n      params: rootPayload.params,\n    },\n  };\n}\n"]}