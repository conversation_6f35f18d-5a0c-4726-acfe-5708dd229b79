{"version": 3, "file": "buildUrlForBundle.native.js", "sourceRoot": "", "sources": ["../../src/async-require/buildUrlForBundle.native.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,SAAgB,iBAAiB,CAAC,UAAkB;IAClD,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;QACpC,OAAO,UAAU,CAAC;KACnB;IAED,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,OAAO,cAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;SACpD;QACD,MAAM,IAAI,KAAK,CACb,6IAA6I,CAC9I,CAAC;KACH;SAAM;QACL,MAAM,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC;aAC5C,OAAmD,CAAC;QAEvD,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,YAAY,EAAE,CAAC;QAE1C,OAAO,cAAc,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;KAC9C;AACH,CAAC;AApBD,8CAoBC;AAED,SAAS,cAAc,CAAC,MAAc,EAAE,MAAc;IACpD,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AACvE,CAAC", "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nexport function buildUrlForBundle(bundlePath: string): string {\n  if (bundlePath.match(/^https?:\\/\\//)) {\n    return bundlePath;\n  }\n\n  if (process.env.NODE_ENV === 'production') {\n    if (typeof location !== 'undefined') {\n      return joinComponents(location.origin, bundlePath);\n    }\n    throw new Error(\n      'Unable to determine the production URL where additional JavaScript chunks are hosted because the global \"location\" variable is not defined.'\n    );\n  } else {\n    const getDevServer = require('../getDevServer')\n      .default as typeof import('../getDevServer').default;\n\n    const { url: serverUrl } = getDevServer();\n\n    return joinComponents(serverUrl, bundlePath);\n  }\n}\n\nfunction joinComponents(prefix: string, suffix: string): string {\n  return prefix.replace(/\\/+$/, '') + '/' + suffix.replace(/^\\/+/, '');\n}\n"]}