{"version": 3, "file": "formatProjectFilePath.js", "sourceRoot": "", "sources": ["../../src/error-overlay/formatProjectFilePath.ts"], "names": [], "mappings": ";;;AAIA,SAAgB,qBAAqB,CAAC,WAAmB,EAAE,IAAoB;IAC7E,IAAI,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,WAAW,CAAC;KACpB;IAED,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAC1F,OAAO,EACP,EAAE,CACH,CAAC;AACJ,CAAC;AATD,sDASC;AAED,SAAS,kBAAkB,CAAC,IAAY,EAAE,UAAkB,EAAE,GAAG,GAAG,GAAG;IACrE,MAAM,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,eAAe,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE;QACzD,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE;YACvC,MAAM;SACP;QACD,CAAC,EAAE,CAAC;KACL;IACD,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAED,SAAgB,yBAAyB,CAAC,WAAmB,EAAE,KAAsB;IACnF,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1E,MAAM,QAAQ,GACZ,qBAAqB,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC;QAC9C,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI;YACvB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/E,CAAC,CAAC,EAAE,CAAC,CAAC;IAEV,OAAO,QAAQ,CAAC;AAClB,CAAC;AATD,8DASC", "sourcesContent": ["import type { StackFrame } from 'stacktrace-parser';\n\nexport type MetroStackFrame = StackFrame & { collapse?: boolean };\n\nexport function formatProjectFilePath(projectRoot: string, file?: string | null): string {\n  if (file == null) {\n    return '<unknown>';\n  }\n\n  return pathRelativeToPath(file.replace(/\\\\/g, '/'), projectRoot.replace(/\\\\/g, '/')).replace(\n    /\\?.*$/,\n    ''\n  );\n}\n\nfunction pathRelativeToPath(path: string, relativeTo: string, sep = '/') {\n  const relativeToParts = relativeTo.split(sep);\n  const pathParts = path.split(sep);\n  let i = 0;\n  while (i < relativeToParts.length && i < pathParts.length) {\n    if (relativeToParts[i] !== pathParts[i]) {\n      break;\n    }\n    i++;\n  }\n  return pathParts.slice(i).join(sep);\n}\n\nexport function getStackFormattedLocation(projectRoot: string, frame: MetroStackFrame) {\n  const column = frame.column != null && parseInt(String(frame.column), 10);\n  const location =\n    formatProjectFilePath(projectRoot, frame.file) +\n    (frame.lineNumber != null\n      ? ':' + frame.lineNumber + (column && !isNaN(column) ? ':' + (column + 1) : '')\n      : '');\n\n  return location;\n}\n"]}