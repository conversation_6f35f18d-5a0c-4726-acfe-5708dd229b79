{"version": 3, "file": "SingleByteEncoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/single-byte/SingleByteEncoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAE7E;;;;;GAKG;AACH,MAAM,OAAO,iBAAiB;IAI5B,YAAoB,KAAwB,EAAE,OAA4B;QAAtD,UAAK,GAAL,KAAK,CAAmB;QAC1C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,aAAa;YAC9B,OAAO,QAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,gBAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,8DAA8D;QAC9D,eAAe;QACf,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAExD,uDAAuD;QACvD,IAAI,OAAO,KAAK,IAAI;YAClB,YAAY,CAAC,UAAU,CAAC,CAAC;QAE3B,kDAAkD;QAClD,OAAO,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;CACF"}