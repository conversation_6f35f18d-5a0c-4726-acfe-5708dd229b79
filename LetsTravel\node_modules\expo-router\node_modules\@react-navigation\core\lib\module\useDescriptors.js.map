{"version": 3, "names": ["React", "NavigationBuilderContext", "NavigationContext", "NavigationRouteContext", "SceneView", "useNavigationCache", "useRouteCache", "useDescriptors", "state", "screens", "navigation", "screenOptions", "defaultScreenOptions", "onAction", "getState", "setState", "addListener", "addKeyedListener", "onRouteFocus", "router", "emitter", "options", "setOptions", "useState", "onDispatchAction", "onOptionsChange", "stackRef", "useContext", "context", "useMemo", "navigations", "routes", "reduce", "acc", "route", "i", "config", "name", "screen", "props", "key", "optionsList", "filter", "Boolean", "customOptions", "curr", "Object", "assign", "mergedOptions", "clearOptions", "o", "_", "rest", "render"], "sourceRoot": "../../src", "sources": ["useDescriptors.tsx"], "mappings": "AAMA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB,MAGxB,4BAA4B;AACnC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,SAAS,MAAM,aAAa;AAUnC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,aAAa,MAAM,iBAAiB;AAgD3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAc,OAmBM;EAAA,IAd1C;IACAC,KAAK;IACLC,OAAO;IACPC,UAAU;IACVC,aAAa;IACbC,oBAAoB;IACpBC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,WAAW;IACXC,gBAAgB;IAChBC,YAAY;IACZC,MAAM;IACNC;EACuC,CAAC;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,KAAK,CAACuB,QAAQ,CAAyB,CAAC,CAAC,CAAC;EACxE,MAAM;IAAEC,gBAAgB;IAAEC,eAAe;IAAEC;EAAS,CAAC,GAAG1B,KAAK,CAAC2B,UAAU,CACtE1B,wBAAwB,CACzB;EAED,MAAM2B,OAAO,GAAG5B,KAAK,CAAC6B,OAAO,CAC3B,OAAO;IACLnB,UAAU;IACVG,QAAQ;IACRG,WAAW;IACXC,gBAAgB;IAChBC,YAAY;IACZM,gBAAgB;IAChBC,eAAe;IACfC;EACF,CAAC,CAAC,EACF,CACEhB,UAAU,EACVG,QAAQ,EACRG,WAAW,EACXC,gBAAgB,EAChBC,YAAY,EACZM,gBAAgB,EAChBC,eAAe,EACfC,QAAQ,CACT,CACF;EAED,MAAMI,WAAW,GAAGzB,kBAAkB,CAAiC;IACrEG,KAAK;IACLM,QAAQ;IACRJ,UAAU;IACVY,UAAU;IACVH,MAAM;IACNC;EACF,CAAC,CAAC;EAEF,MAAMW,MAAM,GAAGzB,aAAa,CAACE,KAAK,CAACuB,MAAM,CAAC;EAE1C,OAAOA,MAAM,CAACC,MAAM,CAiBlB,CAACC,GAAG,EAAEC,KAAK,EAAEC,CAAC,KAAK;IACnB,MAAMC,MAAM,GAAG3B,OAAO,CAACyB,KAAK,CAACG,IAAI,CAAC;IAClC,MAAMC,MAAM,GAAGF,MAAM,CAACG,KAAK;IAC3B,MAAM7B,UAAU,GAAGoB,WAAW,CAACI,KAAK,CAACM,GAAG,CAAC;IAEzC,MAAMC,WAAW,GAAG;IAClB;IACA9B,aAAa;IACb;IACA,IAAKyB,MAAM,CAACf,OAAO,GACfe,MAAM,CAACf,OAAO,CAACqB,MAAM,CAACC,OAAO,CAAC,GAC9B,EAAE,CAA8C;IACpD;IACAL,MAAM,CAACjB,OAAO;IACd;IACAA,OAAO,CAACa,KAAK,CAACM,GAAG,CAAC,CACnB;IAED,MAAMI,aAAa,GAAGH,WAAW,CAACT,MAAM,CACtC,CAACC,GAAG,EAAEY,IAAI,KACRC,MAAM,CAACC,MAAM,CACXd,GAAG;IACH;IACA,OAAOY,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGA,IAAI,CAAC;MAAEX,KAAK;MAAExB;IAAW,CAAC,CAAC,CAChE,EACH,CAAC,CAAC,CACH;IAED,MAAMsC,aAAa,GAAG;MACpB,IAAI,OAAOpC,oBAAoB,KAAK,UAAU;MAC1C;MACAA,oBAAoB,CAAC;QACnBsB,KAAK;QACLxB,UAAU;QACVW,OAAO,EAAEuB;MACX,CAAC,CAAC,GACFhC,oBAAoB,CAAC;MACzB,GAAGgC;IACL,CAAC;IAED,MAAMK,YAAY,GAAG,MACnB3B,UAAU,CAAE4B,CAAC,IAAK;MAChB,IAAIhB,KAAK,CAACM,GAAG,IAAIU,CAAC,EAAE;QAClB;QACA,MAAM;UAAE,CAAChB,KAAK,CAACM,GAAG,GAAGW,CAAC;UAAE,GAAGC;QAAK,CAAC,GAAGF,CAAC;QACrC,OAAOE,IAAI;MACb;MAEA,OAAOF,CAAC;IACV,CAAC,CAAC;IAEJjB,GAAG,CAACC,KAAK,CAACM,GAAG,CAAC,GAAG;MACfN,KAAK;MACL;MACAxB,UAAU;MACV2C,MAAM,GAAG;QACP,oBACE,oBAAC,wBAAwB,CAAC,QAAQ;UAAC,GAAG,EAAEnB,KAAK,CAACM,GAAI;UAAC,KAAK,EAAEZ;QAAQ,gBAChE,oBAAC,iBAAiB,CAAC,QAAQ;UAAC,KAAK,EAAElB;QAAW,gBAC5C,oBAAC,sBAAsB,CAAC,QAAQ;UAAC,KAAK,EAAEwB;QAAM,gBAC5C,oBAAC,SAAS;UACR,UAAU,EAAExB,UAAW;UACvB,KAAK,EAAEwB,KAAM;UACb,MAAM,EAAEI,MAAO;UACf,UAAU,EAAE9B,KAAK,CAACuB,MAAM,CAACI,CAAC,CAAC,CAAC3B,KAAM;UAClC,QAAQ,EAAEM,QAAS;UACnB,QAAQ,EAAEC,QAAS;UACnB,OAAO,EAAEiC,aAAc;UACvB,YAAY,EAAEC;QAAa,EAC3B,CAC8B,CACP,CACK;MAExC,CAAC;MACD5B,OAAO,EAAE2B;IACX,CAAC;IAED,OAAOf,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR"}