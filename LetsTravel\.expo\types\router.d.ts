/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/../../app/test`; params?: Router.UnknownInputParams; } | { pathname: `/../../app/iguided_backup`; params?: Router.UnknownInputParams; } | { pathname: `/../../app/iguided`; params?: Router.UnknownInputParams; } | { pathname: `/../../app/minimal-test`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/components/CameraTextRecognition`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/../../app/test`; params?: Router.UnknownOutputParams; } | { pathname: `/../../app/iguided_backup`; params?: Router.UnknownOutputParams; } | { pathname: `/../../app/iguided`; params?: Router.UnknownOutputParams; } | { pathname: `/../../app/minimal-test`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/components/CameraTextRecognition`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/../../app/test${`?${string}` | `#${string}` | ''}` | `/../../app/iguided_backup${`?${string}` | `#${string}` | ''}` | `/../../app/iguided${`?${string}` | `#${string}` | ''}` | `/../../app/minimal-test${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/components/CameraTextRecognition${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/../../app/test`; params?: Router.UnknownInputParams; } | { pathname: `/../../app/iguided_backup`; params?: Router.UnknownInputParams; } | { pathname: `/../../app/iguided`; params?: Router.UnknownInputParams; } | { pathname: `/../../app/minimal-test`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/components/CameraTextRecognition`; params?: Router.UnknownInputParams; };
    }
  }
}
