import React, { useState } from 'react';
import { View, Text, ScrollView, useColorScheme, TouchableOpacity, SafeAreaView } from 'react-native';
import { Box, VStack, HStack, Button, Input } from '@chakra-ui/react';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/DesignTokens';

// 🎨 Trekmate 4.0 - 简化的 iGuided AI 页面 (Tab版本)
export default function IGuidedTab() {
  const colorScheme = useColorScheme();
  const backgroundColor = colorScheme === 'dark' ? '#121212' : Colors.backgroundDefault;
  const cardBackground = colorScheme === 'dark' ? '#1E1E1E' : 'white';
  const textColor = colorScheme === 'dark' ? 'white' : Colors.textPrimary;

  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);

  const suggestions = [
    "Plan a 3-day trip to Tokyo",
    "Best restaurants in Paris",
    "Budget travel tips for Europe",
    "What to pack for a beach vacation"
  ];

  const handleSuggestionPress = (suggestion: string) => {
    setInputText(suggestion);
  };

  const handleSendMessage = () => {
    if (!inputText.trim()) return;

    const newMessage = {
      id: Date.now(),
      text: inputText,
      sender: 'user',
      timestamp: Date.now()
    };

    setMessages([...messages, newMessage]);
    setInputText('');
    setLoading(true);

    // 模拟 AI 响应
    setTimeout(() => {
      const aiResponse = {
        id: Date.now() + 1,
        text: `Thank you for your question: "${inputText}". This is a simulated AI response. The full iGuided AI functionality will be available soon.`,
        sender: 'ai',
        timestamp: Date.now()
      };
      setMessages(prev => [...prev, aiResponse]);
      setLoading(false);
    }, 2000);
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor }}>
      <Box flex={1} bg={backgroundColor}>
        {/* 头部 */}
        <Box
          bg={cardBackground}
          p={4}
          pt={6}
          shadow="sm"
        >
          <HStack alignItems="center" spacing={3}>
            <Ionicons name="navigate" size={24} color={Colors.primary} />
            <Text
              style={{
                fontSize: 20,
                fontWeight: '600',
                color: textColor,
              }}
            >
              iGuided AI
            </Text>
          </HStack>
        </Box>

        {/* 消息区域 */}
        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 16 }}>
          {messages.length === 0 ? (
            <VStack spacing={6} alignItems="center" mt={8}>
              <VStack spacing={3} alignItems="center">
                <Text
                  style={{
                    fontSize: 24,
                    fontWeight: '700',
                    color: textColor,
                    textAlign: 'center',
                  }}
                >
                  iGuided AI
                </Text>
                <Text
                  style={{
                    fontSize: 16,
                    color: textColor,
                    opacity: 0.7,
                    textAlign: 'center',
                    lineHeight: 22,
                  }}
                >
                  Your intelligent travel assistant for planning and advice
                </Text>
              </VStack>

              <VStack spacing={3} w="100%">
                {suggestions.map((suggestion, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleSuggestionPress(suggestion)}
                    style={{
                      backgroundColor: cardBackground,
                      padding: 16,
                      borderRadius: 12,
                      borderWidth: 1,
                      borderColor: Colors.primary + '20',
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 14,
                        color: textColor,
                        textAlign: 'center',
                      }}
                    >
                      {suggestion}
                    </Text>
                  </TouchableOpacity>
                ))}
              </VStack>
            </VStack>
          ) : (
            <VStack spacing={4}>
              {messages.map((message) => (
                <Box
                  key={message.id}
                  alignSelf={message.sender === 'user' ? 'flex-end' : 'flex-start'}
                  maxW="80%"
                  bg={message.sender === 'user' ? Colors.primary : cardBackground}
                  p={3}
                  borderRadius="lg"
                >
                  <Text
                    style={{
                      color: message.sender === 'user' ? 'white' : textColor,
                      fontSize: 14,
                    }}
                  >
                    {message.text}
                  </Text>
                </Box>
              ))}

              {loading && (
                <Box alignSelf="flex-start" maxW="80%" bg={cardBackground} p={3} borderRadius="lg">
                  <Text style={{ color: textColor, fontSize: 14 }}>
                    AI is thinking...
                  </Text>
                </Box>
              )}
            </VStack>
          )}
        </ScrollView>

        {/* 输入区域 */}
        <Box
          bg={cardBackground}
          p={4}
          borderTopWidth={1}
          borderTopColor={Colors.primary + '20'}
        >
          <HStack spacing={3} alignItems="center">
            <Input
              flex={1}
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Ask me anything about travel..."
              bg={backgroundColor}
              borderColor={Colors.primary + '40'}
              color={textColor}
              fontSize="14px"
            />
            <Button
              onClick={handleSendMessage}
              colorScheme="brand"
              size="md"
              isDisabled={!inputText.trim() || loading}
            >
              <Ionicons name="send" size={16} color="white" />
            </Button>
          </HStack>
        </Box>
      </Box>
    </SafeAreaView>
  );
}
