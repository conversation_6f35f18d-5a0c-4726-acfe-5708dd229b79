{"version": 3, "file": "fetchAsync.native.js", "sourceRoot": "", "sources": ["../../src/async-require/fetchAsync.native.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;AAEH,mBAAmB;AACnB,iGAAsE;AAItE,SAAgB,UAAU,CACxB,GAAW;IAEX,IAAI,EAAE,GAAkB,IAAI,CAAC;IAC7B,IAAI,UAAU,GAAkB,IAAI,CAAC;IACrC,IAAI,YAAY,GAAkB,IAAI,CAAC;IACvC,IAAI,OAAO,GAA2B,EAAE,CAAC;IACzC,IAAI,YAAY,GAAsB,IAAI,CAAC;IAC3C,IAAI,gBAAgB,GAAsB,IAAI,CAAC;IAC/C,IAAI,gBAAgB,GAAsB,IAAI,CAAC;IAC/C,OAAO,IAAI,OAAO,CAChB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAClB,MAAM,WAAW,GAAG,uBAAU,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAU,CAG3C,CAAC;QAChB,YAAY,GAAG,WAAW,CAAC,uBAAuB,EAAE,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,EAAE;YAC5E,IAAI,SAAS,KAAK,EAAE,EAAE;gBACpB,YAAY,GAAG,QAAQ,CAAC;aACzB;QACH,CAAC,CAAC,CAAC;QACH,gBAAgB,GAAG,WAAW,CAC5B,2BAA2B,EAC3B,CAAC,CAAC,SAAS,EAAE,MAAM,EAAE,eAAe,CAAC,EAAE,EAAE;YACvC,IAAI,SAAS,KAAK,EAAE,EAAE;gBACpB,UAAU,GAAG,MAAM,CAAC;gBACpB,OAAO,GAAG,eAAe,CAAC;aAC3B;QACH,CAAC,CACF,CAAC;QACF,gBAAgB,GAAG,WAAW,CAAC,4BAA4B,EAAE,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,EAAE;YAClF,IAAI,SAAS,KAAK,EAAE,EAAE;gBACpB,IAAI,KAAK,EAAE;oBACT,MAAM,CAAC,KAAK,CAAC,CAAC;iBACf;qBAAM;oBACL,OAAO,CAAC,EAAE,IAAI,EAAE,YAAa,EAAE,MAAM,EAAE,UAAW,EAAE,OAAO,EAAE,CAAC,CAAC;iBAChE;aACF;QACH,CAAC,CAAC,CAAC;QACF,uBAAU,CAAC,WAAmB,CAC7B,KAAK,EACL,cAAc,EACd,GAAG,EACH;YACE,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;SACrC,EACD,EAAE,EACF,MAAM,EACN,KAAK,EACL,CAAC,EACD,CAAC,SAAiB,EAAE,EAAE;YACpB,EAAE,GAAG,SAAS,CAAC;QACjB,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC,CACF,CAAC,OAAO,CAAC,GAAG,EAAE;QACb,YAAY,EAAE,MAAM,EAAE,CAAC;QACvB,gBAAgB,EAAE,MAAM,EAAE,CAAC;QAC3B,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AA7DD,gCA6DC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n// @ts-expect-error\nimport Networking from 'react-native/Libraries/Network/RCTNetworking';\n\ntype Subscriber = { remove: () => void };\n\nexport function fetchAsync(\n  url: string\n): Promise<{ body: string; status: number; headers: Record<string, string> }> {\n  let id: string | null = null;\n  let statusCode: number | null = null;\n  let responseText: string | null = null;\n  let headers: Record<string, string> = {};\n  let dataListener: Subscriber | null = null;\n  let completeListener: Subscriber | null = null;\n  let responseListener: Subscriber | null = null;\n  return new Promise<{ body: string; status: number; headers: Record<string, string> }>(\n    (resolve, reject) => {\n      const addListener = Networking.addListener.bind(Networking) as (\n        event: string,\n        callback: (props: [string, any, any]) => any\n      ) => Subscriber;\n      dataListener = addListener('didReceiveNetworkData', ([requestId, response]) => {\n        if (requestId === id) {\n          responseText = response;\n        }\n      });\n      responseListener = addListener(\n        'didReceiveNetworkResponse',\n        ([requestId, status, responseHeaders]) => {\n          if (requestId === id) {\n            statusCode = status;\n            headers = responseHeaders;\n          }\n        }\n      );\n      completeListener = addListener('didCompleteNetworkResponse', ([requestId, error]) => {\n        if (requestId === id) {\n          if (error) {\n            reject(error);\n          } else {\n            resolve({ body: responseText!, status: statusCode!, headers });\n          }\n        }\n      });\n      (Networking.sendRequest as any)(\n        'GET',\n        'asyncRequest',\n        url,\n        {\n          'expo-platform': process.env.EXPO_OS,\n        },\n        '',\n        'text',\n        false,\n        0,\n        (requestId: string) => {\n          id = requestId;\n        },\n        true\n      );\n    }\n  ).finally(() => {\n    dataListener?.remove();\n    completeListener?.remove();\n    responseListener?.remove();\n  });\n}\n"]}