"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const openFileInEditor_1 = __importDefault(require("react-native/Libraries/Core/Devtools/openFileInEditor"));
exports.default = openFileInEditor_1.default;
//# sourceMappingURL=index.native.js.map