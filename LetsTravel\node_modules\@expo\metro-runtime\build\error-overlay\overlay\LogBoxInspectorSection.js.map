{"version": 3, "file": "LogBoxInspectorSection.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/overlay/LogBoxInspectorSection.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,kDAA0B;AAC1B,+CAAsD;AAEtD,+DAAiD;AAQjD,SAAgB,sBAAsB,CAAC,KAAY;IACjD,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,OAAO;QACzB,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,OAAO;YACzB,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,WAAW,IAAG,KAAK,CAAC,OAAO,CAAQ;YACtD,KAAK,CAAC,MAAM,CACR;QACP,8BAAC,mBAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,IAAG,KAAK,CAAC,QAAQ,CAAQ,CAC5C,CACR,CAAC;AACJ,CAAC;AAVD,wDAUC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,OAAO,EAAE;QACP,SAAS,EAAE,EAAE;KACd;IACD,OAAO,EAAE;QACP,UAAU,EAAE,QAAQ;QACpB,aAAa,EAAE,KAAK;QACpB,iBAAiB,EAAE,EAAE;QACrB,YAAY,EAAE,EAAE;KACjB;IACD,WAAW,EAAE;QACX,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;QAClC,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,KAAK;QACjB,kBAAkB,EAAE,KAAK;QACzB,UAAU,EAAE,EAAE;KACf;IACD,IAAI,EAAE;QACJ,aAAa,EAAE,EAAE;KAClB;CACF,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React from 'react';\nimport { StyleSheet, Text, View } from 'react-native';\n\nimport * as LogBoxStyle from '../UI/LogBoxStyle';\n\ntype Props = {\n  heading: string;\n  children: React.ReactNode;\n  action?: any;\n};\n\nexport function LogBoxInspectorSection(props: Props) {\n  return (\n    <View style={styles.section}>\n      <View style={styles.heading}>\n        <Text style={styles.headingText}>{props.heading}</Text>\n        {props.action}\n      </View>\n      <View style={styles.body}>{props.children}</View>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  section: {\n    marginTop: 15,\n  },\n  heading: {\n    alignItems: 'center',\n    flexDirection: 'row',\n    paddingHorizontal: 12,\n    marginBottom: 10,\n  },\n  headingText: {\n    color: LogBoxStyle.getTextColor(1),\n    flex: 1,\n    fontSize: 18,\n    fontWeight: '600',\n    includeFontPadding: false,\n    lineHeight: 20,\n  },\n  body: {\n    paddingBottom: 10,\n  },\n});\n"]}