{"$schema": "https://json.schemastore.org/package.json", "name": "lets-travel", "devDependencies": {"@babel/plugin-transform-export-namespace-from": "7.25.9", "@expo/config-plugins": "9.0.17", "@react-native-community/cli": "^18.0.0", "@react-native-community/cli-platform-android": "^18.0.0", "@react-native-community/cli-platform-ios": "^18.0.0", "@react-native/metro-config": "^0.76.9", "@storybook/react-native": "^7.6.20", "@types/expo__vector-icons": "9.0.1", "@types/jest": "29.5.14", "@types/node": "22.13.13", "@types/react": "~18.3.12", "@types/react-test-renderer": "19.0.0", "babel-plugin-module-resolver": "5.0.2", "babel-plugin-transform-remove-console": "6.9.4", "detox": "^20.26.2", "expo-doctor": "latest", "expo-module-scripts": "4.0.5", "jest": "^29.7.0", "metro": "^0.81.0", "metro-core": "^0.81.0", "metro-runtime": "^0.80.0", "patch-package": "8.0.0", "postinstall-postinstall": "2.1.0", "react-native-dotenv": "3.4.11", "react-native-svg-transformer": "1.5.0", "style-dictionary": "^4.1.3", "ts-prune": "^0.10.3", "typescript": "5.3.3"}, "dependencies": {"@babel/preset-typescript": "7.27.0", "@chakra-ui/icons": "2.1.1", "@chakra-ui/react": "2.8.1", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@expo/webpack-config": "0.12.52", "@hookform/resolvers": "^3.9.1", "@ismaelmoreiraa/vision-camera-ocr": "3.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-clipboard/clipboard": "1.16.2", "@react-native-community/datetimepicker": "8.2.0", "@react-native-ml-kit/text-recognition": "1.5.2", "@react-native-picker/picker": "2.9.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "7.2.10", "@react-three/drei": "10.0.7", "@react-three/fiber": "9.1.2", "@supabase/supabase-js": "2.49.4", "@tanstack/react-query": "5.81.5", "@tanstack/react-query-devtools": "5.81.5", "ar-bridge": "file:../ARBridge", "axios": "1.8.4", "expo": "~52.0.47", "expo-av": "~15.0.2", "expo-blur": "14.0.3", "expo-camera": "16.0.18", "expo-clipboard": "7.0.1", "expo-constants": "17.0.8", "expo-dev-client": "~5.0.20", "expo-file-system": "~18.0.12", "expo-font": "13.0.4", "expo-gl": "~15.0.5", "expo-haptics": "14.0.1", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "16.0.6", "expo-linking": "7.0.5", "expo-location": "~18.0.10", "expo-media-library": "17.0.6", "expo-router": "~4.0.21", "expo-speech": "~13.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "2.0.1", "expo-symbols": "0.2.2", "expo-system-ui": "~4.0.9", "framer-motion": "10.16.4", "fuse.js": "7.1.0", "metro-resolver": "0.81.5", "moti": "^0.29.0", "nativewind": "^2.0.11", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.53.2", "react-native": "0.76.9", "react-native-gesture-handler": "2.20.2", "react-native-maps": "1.18.0", "react-native-markdown-display": "7.0.2", "react-native-paper": "5.13.1", "react-native-reanimated": "3.16.7", "react-native-safe-area-context": "4.12.0", "react-native-screens": "4.4.0", "react-native-svg": "15.8.0", "react-native-vision-camera": "4.6.4", "react-native-vision-camera-text-recognition": "3.1.1", "react-native-web": "~0.19.10", "react-native-webview": "13.12.5", "react-native-worklets-core": "1.5.0", "react-query": "3.39.3", "tesseract.js": "6.0.1", "three": "0.176.0", "zod": "^3.23.8"}, "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "check-deps": "npx expo-doctor", "postinstall": "patch-package", "clean": "expo start --clear", "clean-install": "rm -rf node_modules && npm install", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "test": "jest", "build": "expo build", "optimize": "npm run clean && npm run type-check", "build:tokens": "node scripts/generateTokens.js", "prebuild": "npm run build:tokens", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "private": true, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1", "@types/react": "18.2.45", "@react-native-async-storage/async-storage": "1.23.1"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-maps", "@expo/webpack-config", "i18next"]}}}, "version": "1.0.0"}