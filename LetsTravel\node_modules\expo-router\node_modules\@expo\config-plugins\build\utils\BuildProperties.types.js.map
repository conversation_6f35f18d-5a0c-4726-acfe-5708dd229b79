{"version": 3, "file": "BuildProperties.types.js", "names": [], "sources": ["../../src/utils/BuildProperties.types.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config-types';\n\n/**\n * Rule to transform from config to build properties\n *\n * @example\n * ```ts\n * {\n *   propName: 'expo.jsEngine',\n *   propValueGetter: (config) => config.ios?.jsEngine ?? config.jsEngine ?? 'hermes',\n * }\n * ```\n * Will lookup a value through the `propValueGetter`, and update to `hermesEnabled` key-value in **android/gradle.properties**\n * or `expo.jsEngine` key-value in **ios/Podfile.properties.json**.\n *\n */\n\n/**\n * Source config can be either expo config or generic config\n */\nexport type BuildPropertiesConfig = ExpoConfig | Record<string, any>;\n\nexport interface ConfigToPropertyRuleType<SourceConfigType extends BuildPropertiesConfig> {\n  /** Property name in `android/gradle.properties` or `ios/Podfile.properties.json` */\n  propName: string;\n\n  /** Passing config and get the property value */\n  propValueGetter: (config: SourceConfigType) => string | null | undefined;\n}\n"], "mappings": "", "ignoreList": []}