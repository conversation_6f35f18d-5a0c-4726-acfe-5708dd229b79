{"version": 3, "file": "IntentFilters.js", "names": ["_Manifest", "data", "require", "_androidPlugins", "GENERATED_TAG", "withAndroidIntentFilters", "exports", "createAndroidManifestPlugin", "setAndroidIntentFilters", "getIntentFilters", "config", "android", "intentFilters", "androidManifest", "mainActivity", "getMainActivityOrThrow", "length", "filter", "value", "$", "concat", "renderIntentFilters", "map", "<PERSON><PERSON><PERSON><PERSON>", "autoVerify", "undefined", "action", "renderIntentFilterData", "category", "renderIntentFilterCategory", "Array", "isArray", "Boolean", "datum", "Object", "entries", "reduce", "prev", "key", "cat"], "sources": ["../../src/android/IntentFilters.ts"], "sourcesContent": ["import { Android, AndroidIntentFiltersData, ExpoConfig } from '@expo/config-types';\n\nimport { AndroidManifest, getMainActivityOrThrow, ManifestIntentFilter } from './Manifest';\nimport { createAndroidManifestPlugin } from '../plugins/android-plugins';\n\ntype AndroidIntentFilters = NonNullable<Android['intentFilters']>;\n\nconst GENERATED_TAG = 'data-generated';\n\nexport const withAndroidIntentFilters = createAndroidManifestPlugin(\n  setAndroidIntentFilters,\n  'withAndroidIntentFilters'\n);\n\nexport function getIntentFilters(config: Pick<ExpoConfig, 'android'>): AndroidIntentFilters {\n  return config.android?.intentFilters ?? [];\n}\n\nexport function setAndroidIntentFilters(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidManifest\n): AndroidManifest {\n  // Always ensure old tags are removed.\n  const mainActivity = getMainActivityOrThrow(androidManifest);\n  // Remove all generated tags from previous runs...\n  if (mainActivity['intent-filter']?.length) {\n    mainActivity['intent-filter'] = mainActivity['intent-filter'].filter(\n      (value) => value.$?.[GENERATED_TAG] !== 'true'\n    );\n  }\n\n  const intentFilters = getIntentFilters(config);\n  if (!intentFilters.length) {\n    return androidManifest;\n  }\n\n  mainActivity['intent-filter'] = mainActivity['intent-filter']?.concat(\n    renderIntentFilters(intentFilters)\n  );\n\n  return androidManifest;\n}\n\nexport default function renderIntentFilters(\n  intentFilters: AndroidIntentFilters\n): ManifestIntentFilter[] {\n  return intentFilters.map((intentFilter) => {\n    // <intent-filter>\n    return {\n      $: {\n        'android:autoVerify': intentFilter.autoVerify ? 'true' : undefined,\n        // Add a custom \"generated\" tag that we can query later to remove.\n        [GENERATED_TAG]: 'true',\n      },\n      action: [\n        // <action android:name=\"android.intent.action.VIEW\"/>\n        {\n          $: {\n            'android:name': `android.intent.action.${intentFilter.action}`,\n          },\n        },\n      ],\n      data: renderIntentFilterData(intentFilter.data),\n      category: renderIntentFilterCategory(intentFilter.category),\n    };\n  });\n}\n\n/** Like `<data android:scheme=\"exp\"/>` */\nfunction renderIntentFilterData(data?: AndroidIntentFiltersData | AndroidIntentFiltersData[]) {\n  return (Array.isArray(data) ? data : [data]).filter(Boolean).map((datum) => ({\n    $: Object.entries(datum ?? {}).reduce(\n      (prev, [key, value]) => ({ ...prev, [`android:${key}`]: value }),\n      {}\n    ),\n  }));\n}\n\n/** Like `<category android:name=\"android.intent.category.DEFAULT\"/>` */\nfunction renderIntentFilterCategory(category?: string | string[]) {\n  return (Array.isArray(category) ? category : [category]).filter(Boolean).map((cat) => ({\n    $: {\n      'android:name': `android.intent.category.${cat}`,\n    },\n  }));\n}\n"], "mappings": ";;;;;;;;;AAEA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,gBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,eAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,MAAMG,aAAa,GAAG,gBAAgB;AAE/B,MAAMC,wBAAwB,GAAAC,OAAA,CAAAD,wBAAA,GAAG,IAAAE,6CAA2B,EACjEC,uBAAuB,EACvB,0BACF,CAAC;AAEM,SAASC,gBAAgBA,CAACC,MAAmC,EAAwB;EAC1F,OAAOA,MAAM,CAACC,OAAO,EAAEC,aAAa,IAAI,EAAE;AAC5C;AAEO,SAASJ,uBAAuBA,CACrCE,MAAmC,EACnCG,eAAgC,EACf;EACjB;EACA,MAAMC,YAAY,GAAG,IAAAC,kCAAsB,EAACF,eAAe,CAAC;EAC5D;EACA,IAAIC,YAAY,CAAC,eAAe,CAAC,EAAEE,MAAM,EAAE;IACzCF,YAAY,CAAC,eAAe,CAAC,GAAGA,YAAY,CAAC,eAAe,CAAC,CAACG,MAAM,CACjEC,KAAK,IAAKA,KAAK,CAACC,CAAC,GAAGf,aAAa,CAAC,KAAK,MAC1C,CAAC;EACH;EAEA,MAAMQ,aAAa,GAAGH,gBAAgB,CAACC,MAAM,CAAC;EAC9C,IAAI,CAACE,aAAa,CAACI,MAAM,EAAE;IACzB,OAAOH,eAAe;EACxB;EAEAC,YAAY,CAAC,eAAe,CAAC,GAAGA,YAAY,CAAC,eAAe,CAAC,EAAEM,MAAM,CACnEC,mBAAmB,CAACT,aAAa,CACnC,CAAC;EAED,OAAOC,eAAe;AACxB;AAEe,SAASQ,mBAAmBA,CACzCT,aAAmC,EACX;EACxB,OAAOA,aAAa,CAACU,GAAG,CAAEC,YAAY,IAAK;IACzC;IACA,OAAO;MACLJ,CAAC,EAAE;QACD,oBAAoB,EAAEI,YAAY,CAACC,UAAU,GAAG,MAAM,GAAGC,SAAS;QAClE;QACA,CAACrB,aAAa,GAAG;MACnB,CAAC;MACDsB,MAAM,EAAE;MACN;MACA;QACEP,CAAC,EAAE;UACD,cAAc,EAAE,yBAAyBI,YAAY,CAACG,MAAM;QAC9D;MACF,CAAC,CACF;MACDzB,IAAI,EAAE0B,sBAAsB,CAACJ,YAAY,CAACtB,IAAI,CAAC;MAC/C2B,QAAQ,EAAEC,0BAA0B,CAACN,YAAY,CAACK,QAAQ;IAC5D,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA,SAASD,sBAAsBA,CAAC1B,IAA4D,EAAE;EAC5F,OAAO,CAAC6B,KAAK,CAACC,OAAO,CAAC9B,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC,EAAEgB,MAAM,CAACe,OAAO,CAAC,CAACV,GAAG,CAAEW,KAAK,KAAM;IAC3Ed,CAAC,EAAEe,MAAM,CAACC,OAAO,CAACF,KAAK,IAAI,CAAC,CAAC,CAAC,CAACG,MAAM,CACnC,CAACC,IAAI,EAAE,CAACC,GAAG,EAAEpB,KAAK,CAAC,MAAM;MAAE,GAAGmB,IAAI;MAAE,CAAC,WAAWC,GAAG,EAAE,GAAGpB;IAAM,CAAC,CAAC,EAChE,CAAC,CACH;EACF,CAAC,CAAC,CAAC;AACL;;AAEA;AACA,SAASW,0BAA0BA,CAACD,QAA4B,EAAE;EAChE,OAAO,CAACE,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC,EAAEX,MAAM,CAACe,OAAO,CAAC,CAACV,GAAG,CAAEiB,GAAG,KAAM;IACrFpB,CAAC,EAAE;MACD,cAAc,EAAE,2BAA2BoB,GAAG;IAChD;EACF,CAAC,CAAC,CAAC;AACL", "ignoreList": []}