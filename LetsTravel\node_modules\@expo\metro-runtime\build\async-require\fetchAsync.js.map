{"version": 3, "file": "fetchAsync.js", "sourceRoot": "", "sources": ["../../src/async-require/fetchAsync.ts"], "names": [], "mappings": ";;;AAAA;;;;;GAKG;AACI,KAAK,UAAU,UAAU,CAC9B,GAAW;IAEX,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;QAChC,MAAM,EAAE,KAAK;QACb,OAAO,EAAE;YACP,wEAAwE;YACxE,eAAe,EAAE,KAAK;SACvB;KACF,CAAC,CAAC;IACH,OAAO;QACL,IAAI,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE;QAC3B,MAAM,EAAE,QAAQ,CAAC,MAAM;QACvB,OAAO,EAAE,QAAQ,CAAC,OAAO;KAC1B,CAAC;AACJ,CAAC;AAfD,gCAeC", "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nexport async function fetchAsync(\n  url: string\n): Promise<{ body: string; status: number; headers: Headers }> {\n  const response = await fetch(url, {\n    method: 'GET',\n    headers: {\n      // No real reason for this but we try to use this format for everything.\n      'expo-platform': 'web',\n    },\n  });\n  return {\n    body: await response.text(),\n    status: response.status,\n    headers: response.headers,\n  };\n}\n"]}