{"version": 3, "file": "LogBoxInspectorStackFrames.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/overlay/LogBoxInspectorStackFrames.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,+CAAwC;AACxC,+CAAsD;AAEtD,qEAAkE;AAClE,qFAAkF;AAClF,2EAAwE;AAGxE,mDAAoD;AACpD,qDAAkD;AAClD,+DAAiD;AACjD,mFAA2D;AAO3D,SAAgB,kBAAkB,CAAC,WAAkB,EAAE,SAAkB;IACvE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,OAAO,mBAAmB,CAAC;KAC5B;IAED,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE;QAChE,IAAI,QAAQ,KAAK,IAAI,EAAE;YACrB,OAAO,KAAK,GAAG,CAAC,CAAC;SAClB;QAED,OAAO,KAAK,CAAC;IACf,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,IAAI,cAAc,KAAK,CAAC,EAAE;QACxB,OAAO,oBAAoB,CAAC;KAC7B;IAED,MAAM,WAAW,GAAG,QAAQ,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5D,IAAI,cAAc,KAAK,WAAW,CAAC,MAAM,EAAE;QACzC,OAAO,SAAS;YACd,CAAC,CAAC,MAAM,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,cAAc,cAAc,WAAW,EAAE;YACtF,CAAC,CAAC,WAAW,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,cAAc,IAAI,WAAW,EAAE,CAAC;KACrF;SAAM;QACL,OAAO,SAAS;YACd,CAAC,CAAC,OAAO,cAAc,SAAS,WAAW,EAAE;YAC7C,CAAC,CAAC,YAAY,cAAc,IAAI,WAAW,EAAE,CAAC;KACjD;AACH,CAAC;AA3BD,gDA2BC;AAED,SAAgB,0BAA0B,CAAC,EAAE,OAAO,EAAE,IAAI,EAAS;IACjE,MAAM,GAAG,GAAG,IAAA,2BAAc,GAAE,CAAC;IAE7B,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAC,GAAG,EAAE;QAC9C,mEAAmE;QACnE,OAAO,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,SAAS,YAAY;QACnB,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,OAAO,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;SACzE;aAAM;YACL,OAAO,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;SACpC;IACH,CAAC;IAED,IAAI,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,MAAM,KAAK,CAAC,EAAE;QAC7C,OAAO,IAAI,CAAC;KACb;IAED,OAAO,CACL,8BAAC,+CAAsB,IACrB,OAAO,EAAE,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,YAAY,EAChE,MAAM,EACJ,8BAAC,+DAA8B,IAC7B,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EACpE,MAAM,EAAE,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,GACrC;QAEH,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,UAAU,IAAI,CAC/C,8BAAC,mBAAI,IAAC,KAAK,EAAE,WAAW,CAAC,OAAO;YAC9B,8BAAC,mBAAI,IAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,qIAG1B,CACF,CACR;QACD,8BAAC,cAAc,IAAC,IAAI,EAAE,YAAY,EAAG,EAAE,MAAM,EAAE,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,GAAI;QAChF,8BAAC,gBAAgB,IACf,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,EACvC,OAAO,EAAE,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAE,EAAE,CAAC,CAAC,SAAS,CAAC,GACtE,CACqB,CAC1B,CAAC;AACJ,CAAC;AA5CD,gEA4CC;AAED,SAAS,cAAc,CAAC,EACtB,IAAI,EACJ,MAAM,GAIP;IACC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QAC/B,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;QACnC,OAAO,CACL,8BAAC,qDAAyB,IACxB,GAAG,EAAE,KAAK,EACV,KAAK,EAAE,KAAK,EACZ,OAAO,EACL,MAAM,KAAK,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,UAAU,IAAI,IAAI;gBACzD,CAAC,CAAC,GAAG,EAAE,CAAC,IAAA,0BAAgB,EAAC,IAAI,EAAE,UAAU,CAAC;gBAC1C,CAAC,CAAC,SAAS,GAEf,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,gBAAgB,CAAC,EAAE,OAAO,EAAE,OAAO,EAA4C;IACtF,OAAO,CACL,8BAAC,mBAAI,IAAC,KAAK,EAAE,WAAW,CAAC,iBAAiB;QACxC,8BAAC,2BAAY,IACX,eAAe,EAAE;gBACf,OAAO,EAAE,aAAa;gBACtB,OAAO,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC;aAC3C,EACD,OAAO,EAAE,OAAO,EAChB,KAAK,EAAE,WAAW,CAAC,cAAc;YACjC,8BAAC,mBAAI,IAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,IAAG,OAAO,CAAQ,CACtC,CACV,CACR,CAAC;AACJ,CAAC;AAED,MAAM,WAAW,GAAG,yBAAU,CAAC,MAAM,CAAC;IACpC,OAAO,EAAE;QACP,SAAS,EAAE,EAAE;KACd;IACD,OAAO,EAAE;QACP,UAAU,EAAE,QAAQ;QACpB,aAAa,EAAE,KAAK;QACpB,iBAAiB,EAAE,EAAE;QACrB,YAAY,EAAE,EAAE;KACjB;IACD,WAAW,EAAE;QACX,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;QAClC,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,KAAK;QACjB,kBAAkB,EAAE,KAAK;QACzB,UAAU,EAAE,EAAE;KACf;IACD,IAAI,EAAE;QACJ,aAAa,EAAE,EAAE;KAClB;IACD,QAAQ,EAAE;QACR,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;QAClC,QAAQ,EAAE,EAAE;QACZ,kBAAkB,EAAE,KAAK;QACzB,UAAU,EAAE,EAAE;QACd,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE,EAAE;KACtB;IACD,QAAQ,EAAE;QACR,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC;QACpC,QAAQ,EAAE,EAAE;QACZ,kBAAkB,EAAE,KAAK;QACzB,UAAU,EAAE,EAAE;QACd,UAAU,EAAE,KAAK;QACjB,gBAAgB,EAAE,EAAE;KACrB;IACD,OAAO,EAAE;QACP,eAAe,EAAE,WAAW,CAAC,kBAAkB,EAAE;QACjD,gBAAgB,EAAE,EAAE;QACpB,iBAAiB,EAAE,CAAC;QACpB,eAAe,EAAE,EAAE;QACnB,YAAY,EAAE,CAAC;QACf,YAAY,EAAE,CAAC;KAChB;IACD,iBAAiB,EAAE;QACjB,UAAU,EAAE,EAAE;QACd,aAAa,EAAE,KAAK;KACrB;IACD,cAAc,EAAE;QACd,YAAY,EAAE,CAAC;KAChB;IACD,QAAQ,EAAE;QACR,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC;QACpC,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,KAAK;QACjB,UAAU,EAAE,EAAE;QACd,SAAS,EAAE,CAAC;QACZ,iBAAiB,EAAE,EAAE;QACrB,eAAe,EAAE,CAAC;KACnB;CACF,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React, { useState } from 'react';\nimport { StyleSheet, Text, View } from 'react-native';\n\nimport { LogBoxInspectorSection } from './LogBoxInspectorSection';\nimport { LogBoxInspectorSourceMapStatus } from './LogBoxInspectorSourceMapStatus';\nimport { LogBoxInspectorStackFrame } from './LogBoxInspectorStackFrame';\nimport type { StackType } from '../Data/LogBoxLog';\nimport type { Stack } from '../Data/LogBoxSymbolication';\nimport { useSelectedLog } from '../Data/LogContext';\nimport { LogBoxButton } from '../UI/LogBoxButton';\nimport * as LogBoxStyle from '../UI/LogBoxStyle';\nimport openFileInEditor from '../modules/openFileInEditor';\n\ntype Props = {\n  type: StackType;\n  onRetry: () => void;\n};\n\nexport function getCollapseMessage(stackFrames: Stack, collapsed: boolean): string {\n  if (stackFrames.length === 0) {\n    return 'No frames to show';\n  }\n\n  const collapsedCount = stackFrames.reduce((count, { collapse }) => {\n    if (collapse === true) {\n      return count + 1;\n    }\n\n    return count;\n  }, 0);\n\n  if (collapsedCount === 0) {\n    return 'Showing all frames';\n  }\n\n  const framePlural = `frame${collapsedCount > 1 ? 's' : ''}`;\n  if (collapsedCount === stackFrames.length) {\n    return collapsed\n      ? `See${collapsedCount > 1 ? ' all ' : ' '}${collapsedCount} collapsed ${framePlural}`\n      : `Collapse${collapsedCount > 1 ? ' all ' : ' '}${collapsedCount} ${framePlural}`;\n  } else {\n    return collapsed\n      ? `See ${collapsedCount} more ${framePlural}`\n      : `Collapse ${collapsedCount} ${framePlural}`;\n  }\n}\n\nexport function LogBoxInspectorStackFrames({ onRetry, type }: Props) {\n  const log = useSelectedLog();\n\n  const [collapsed, setCollapsed] = useState(() => {\n    // Only collapse frames initially if some frames are not collapsed.\n    return log.getAvailableStack(type)?.some(({ collapse }) => !collapse);\n  });\n\n  function getStackList() {\n    if (collapsed === true) {\n      return log.getAvailableStack(type)?.filter(({ collapse }) => !collapse);\n    } else {\n      return log.getAvailableStack(type);\n    }\n  }\n\n  if (log.getAvailableStack(type)?.length === 0) {\n    return null;\n  }\n\n  return (\n    <LogBoxInspectorSection\n      heading={type === 'component' ? 'Component Stack' : 'Call Stack'}\n      action={\n        <LogBoxInspectorSourceMapStatus\n          onPress={log.symbolicated[type].status === 'FAILED' ? onRetry : null}\n          status={log.symbolicated[type].status}\n        />\n      }>\n      {log.symbolicated[type].status !== 'COMPLETE' && (\n        <View style={stackStyles.hintBox}>\n          <Text style={stackStyles.hintText}>\n            This call stack is not symbolicated. Some features are unavailable such as viewing the\n            function name or tapping to open files.\n          </Text>\n        </View>\n      )}\n      <StackFrameList list={getStackList()!} status={log.symbolicated[type].status} />\n      <StackFrameFooter\n        onPress={() => setCollapsed(!collapsed)}\n        message={getCollapseMessage(log.getAvailableStack(type)!, !!collapsed)}\n      />\n    </LogBoxInspectorSection>\n  );\n}\n\nfunction StackFrameList({\n  list,\n  status,\n}: {\n  list: Stack;\n  status: 'NONE' | 'PENDING' | 'COMPLETE' | 'FAILED';\n}): any {\n  return list.map((frame, index) => {\n    const { file, lineNumber } = frame;\n    return (\n      <LogBoxInspectorStackFrame\n        key={index}\n        frame={frame}\n        onPress={\n          status === 'COMPLETE' && file != null && lineNumber != null\n            ? () => openFileInEditor(file, lineNumber)\n            : undefined\n        }\n      />\n    );\n  });\n}\n\nfunction StackFrameFooter({ message, onPress }: { message: string; onPress: () => void }) {\n  return (\n    <View style={stackStyles.collapseContainer}>\n      <LogBoxButton\n        backgroundColor={{\n          default: 'transparent',\n          pressed: LogBoxStyle.getBackgroundColor(1),\n        }}\n        onPress={onPress}\n        style={stackStyles.collapseButton}>\n        <Text style={stackStyles.collapse}>{message}</Text>\n      </LogBoxButton>\n    </View>\n  );\n}\n\nconst stackStyles = StyleSheet.create({\n  section: {\n    marginTop: 15,\n  },\n  heading: {\n    alignItems: 'center',\n    flexDirection: 'row',\n    paddingHorizontal: 12,\n    marginBottom: 10,\n  },\n  headingText: {\n    color: LogBoxStyle.getTextColor(1),\n    flex: 1,\n    fontSize: 20,\n    fontWeight: '600',\n    includeFontPadding: false,\n    lineHeight: 20,\n  },\n  body: {\n    paddingBottom: 10,\n  },\n  bodyText: {\n    color: LogBoxStyle.getTextColor(1),\n    fontSize: 14,\n    includeFontPadding: false,\n    lineHeight: 18,\n    fontWeight: '500',\n    paddingHorizontal: 27,\n  },\n  hintText: {\n    color: LogBoxStyle.getTextColor(0.7),\n    fontSize: 13,\n    includeFontPadding: false,\n    lineHeight: 18,\n    fontWeight: '400',\n    marginHorizontal: 10,\n  },\n  hintBox: {\n    backgroundColor: LogBoxStyle.getBackgroundColor(),\n    marginHorizontal: 10,\n    paddingHorizontal: 5,\n    paddingVertical: 10,\n    borderRadius: 5,\n    marginBottom: 5,\n  },\n  collapseContainer: {\n    marginLeft: 15,\n    flexDirection: 'row',\n  },\n  collapseButton: {\n    borderRadius: 5,\n  },\n  collapse: {\n    color: LogBoxStyle.getTextColor(0.7),\n    fontSize: 12,\n    fontWeight: '300',\n    lineHeight: 20,\n    marginTop: 0,\n    paddingHorizontal: 10,\n    paddingVertical: 5,\n  },\n});\n"]}