{"version": 3, "file": "Properties.js", "names": ["parsePropertiesFile", "contents", "propertiesList", "lines", "split", "i", "length", "line", "trim", "push", "type", "startsWith", "value", "substring", "trimStart", "eok", "indexOf", "key", "slice", "propertiesListToString", "props", "output", "prop", "Error"], "sources": ["../../src/android/Properties.ts"], "sourcesContent": ["export type PropertiesItem =\n  | {\n      type: 'comment';\n      value: string;\n    }\n  | {\n      type: 'empty';\n    }\n  | {\n      type: 'property';\n      key: string;\n      value: string;\n    };\n\nexport function parsePropertiesFile(contents: string): PropertiesItem[] {\n  const propertiesList: PropertiesItem[] = [];\n  const lines = contents.split('\\n');\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i].trim();\n    if (!line) {\n      propertiesList.push({ type: 'empty' });\n    } else if (line.startsWith('#')) {\n      propertiesList.push({ type: 'comment', value: line.substring(1).trimStart() });\n    } else {\n      const eok = line.indexOf('=');\n      const key = line.slice(0, eok);\n      const value = line.slice(eok + 1, line.length);\n      propertiesList.push({ type: 'property', key, value });\n    }\n  }\n\n  return propertiesList;\n}\n\nexport function propertiesListToString(props: PropertiesItem[]): string {\n  let output = '';\n  for (let i = 0; i < props.length; i++) {\n    const prop = props[i];\n    if (prop.type === 'empty') {\n      output += '';\n    } else if (prop.type === 'comment') {\n      output += '# ' + prop.value;\n    } else if (prop.type === 'property') {\n      output += `${prop.key}=${prop.value}`;\n    } else {\n      // @ts-ignore: assertion\n      throw new Error(`Invalid properties type \"${prop.type}\"`);\n    }\n    if (i < props.length - 1) {\n      output += '\\n';\n    }\n  }\n  return output;\n}\n"], "mappings": ";;;;;;;AAcO,SAASA,mBAAmBA,CAACC,QAAgB,EAAoB;EACtE,MAAMC,cAAgC,GAAG,EAAE;EAC3C,MAAMC,KAAK,GAAGF,QAAQ,CAACG,KAAK,CAAC,IAAI,CAAC;EAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAME,IAAI,GAAGJ,KAAK,CAACE,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACD,IAAI,EAAE;MACTL,cAAc,CAACO,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAQ,CAAC,CAAC;IACxC,CAAC,MAAM,IAAIH,IAAI,CAACI,UAAU,CAAC,GAAG,CAAC,EAAE;MAC/BT,cAAc,CAACO,IAAI,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEE,KAAK,EAAEL,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC,CAACC,SAAS,CAAC;MAAE,CAAC,CAAC;IAChF,CAAC,MAAM;MACL,MAAMC,GAAG,GAAGR,IAAI,CAACS,OAAO,CAAC,GAAG,CAAC;MAC7B,MAAMC,GAAG,GAAGV,IAAI,CAACW,KAAK,CAAC,CAAC,EAAEH,GAAG,CAAC;MAC9B,MAAMH,KAAK,GAAGL,IAAI,CAACW,KAAK,CAACH,GAAG,GAAG,CAAC,EAAER,IAAI,CAACD,MAAM,CAAC;MAC9CJ,cAAc,CAACO,IAAI,CAAC;QAAEC,IAAI,EAAE,UAAU;QAAEO,GAAG;QAAEL;MAAM,CAAC,CAAC;IACvD;EACF;EAEA,OAAOV,cAAc;AACvB;AAEO,SAASiB,sBAAsBA,CAACC,KAAuB,EAAU;EACtE,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACd,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMiB,IAAI,GAAGF,KAAK,CAACf,CAAC,CAAC;IACrB,IAAIiB,IAAI,CAACZ,IAAI,KAAK,OAAO,EAAE;MACzBW,MAAM,IAAI,EAAE;IACd,CAAC,MAAM,IAAIC,IAAI,CAACZ,IAAI,KAAK,SAAS,EAAE;MAClCW,MAAM,IAAI,IAAI,GAAGC,IAAI,CAACV,KAAK;IAC7B,CAAC,MAAM,IAAIU,IAAI,CAACZ,IAAI,KAAK,UAAU,EAAE;MACnCW,MAAM,IAAI,GAAGC,IAAI,CAACL,GAAG,IAAIK,IAAI,CAACV,KAAK,EAAE;IACvC,CAAC,MAAM;MACL;MACA,MAAM,IAAIW,KAAK,CAAC,4BAA4BD,IAAI,CAACZ,IAAI,GAAG,CAAC;IAC3D;IACA,IAAIL,CAAC,GAAGe,KAAK,CAACd,MAAM,GAAG,CAAC,EAAE;MACxBe,MAAM,IAAI,IAAI;IAChB;EACF;EACA,OAAOA,MAAM;AACf", "ignoreList": []}