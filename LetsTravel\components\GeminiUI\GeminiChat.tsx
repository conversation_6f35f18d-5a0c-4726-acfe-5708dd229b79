import React, { useState, useRef, useEffect } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  Animated,
  Image,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  Modal,
  TouchableWithoutFeedback,
  Alert,
  StyleSheet,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// 直接在组件文件中定义样式，避免导入问题
const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#ffffff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#202124',
    marginLeft: 12,
  },
  headerLogo: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesList: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 8,
  },
  welcomeContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 24,
  },
  welcomeTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#202124',
    marginBottom: 8,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#5f6368',
    marginBottom: 40,
    textAlign: 'center',
    lineHeight: 22,
  },
  suggestionContainer: {
    width: '100%',
    maxWidth: 500,
    alignSelf: 'center',
  },
  suggestionButton: {
    backgroundColor: '#f1f3f4',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  suggestionButtonActive: {
    backgroundColor: '#e8f0fe',
  },
  suggestionText: {
    color: '#202124',
    fontSize: 15,
  },
  messageBubble: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 6,
    maxWidth: width > 500 ? '70%' : '88%',
  },
  userBubble: {
    backgroundColor: '#e8f0fe',
    alignSelf: 'flex-end',
    borderTopRightRadius: 4,
  },
  geminiBubble: {
    backgroundColor: '#f1f3f4',
    alignSelf: 'flex-start',
    borderTopLeftRadius: 4,
  },
  messageText: {
    color: '#202124',
    fontSize: 15,
    lineHeight: 22,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f1f3f4',
    borderRadius: 12,
    alignSelf: 'flex-start',
    maxWidth: '70%',
  },
  typingDot: {
    height: 6,
    width: 6,
    borderRadius: 3,
    backgroundColor: '#4285f4',
    marginHorizontal: 2,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 12,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  input: {
    flex: 1,
    backgroundColor: '#f1f3f4',
    color: '#202124',
    padding: 12,
    paddingTop: 12,
    borderRadius: 24,
    maxHeight: 120,
    fontSize: 15,
    marginRight: 8,
  },
  sendButton: {
    marginLeft: 12,
    backgroundColor: '#4285f4',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#dadce0',
  },
  sendIcon: {
    marginLeft: 2,
  },
  // 语音按钮样式
  voiceButton: {
    marginLeft: 8,
    backgroundColor: '#FF5722',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  voiceButtonRecording: {
    backgroundColor: '#EA4335', // Google红色，表示正在录制
    transform: [{ scale: 1.1 }],
  },
  dateDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#e0e0e0',
  },
  dateText: {
    color: '#5f6368',
    fontSize: 13,
    marginHorizontal: 12,
  },
  avatarContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#4285f4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  botAvatarContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#4285f4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  botAvatarText: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  userAvatarText: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  messageContent: {
    flex: 1,
  },
  // Attachment button styles
  attachmentButton: {
    width: 40, 
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f1f3f4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  attachmentButtonIcon: {
    color: '#5f6368',
  },
  attachmentOptionsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 12,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  attachmentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  attachmentOptionText: {
    marginLeft: 12,
    color: '#202124',
    fontSize: 14,
  },
  imagePreviewContainer: {
    width: '100%',
    padding: 8,
    backgroundColor: '#f1f3f4',
    borderRadius: 8,
    marginVertical: 8,
  },
  imagePreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#e0e0e0',
  },
  imageMessageContainer: {
    width: '100%',
    maxWidth: 300,
  },
  imageMessage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#202124',
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalOptionText: {
    marginLeft: 16,
    fontSize: 15,
    color: '#202124',
  },
});

// 用try-catch导入图片选择器模块，防止模块无法加载导致崩溃
let ImagePicker: any = null;
try {
  ImagePicker = require('expo-image-picker');
} catch (error) {
  console.warn('Warning: expo-image-picker could not be loaded', error);
}

// 安全导入FileSystem模块
let FileSystem: any = null;
try {
  FileSystem = require('expo-file-system');
} catch (error) {
  console.warn('Warning: expo-file-system could not be loaded', error);
}

// 尝试导入语音识别模块
let Speech: any = null;
try {
  Speech = require('expo-speech');
} catch (error) {
  console.warn('Warning: expo-speech could not be loaded', error);
}

// 尝试导入音频录制模块
let Audio: any = null;
try {
  Audio = require('expo-av');
} catch (error) {
  console.warn('Warning: expo-av could not be loaded', error);
}

const mockResponse = (query: string, imageAttached?: boolean) => {
  // Enhanced response simulation logic
  if (imageAttached) {
    return "I can see the image you've shared. Based on what I observe:\n\n**Analysis of your image:**\n- This appears to be a travel-related scene\n- The location seems to be in an interesting area\n- The lighting and composition are good\n\nWould you like recommendations related to this location or information about similar destinations? I can help with identifying landmarks, suggesting nearby attractions, or finding similar places to visit.";
  }

  const responses: Record<string, string> = {
    'Recommend travel destinations': 'Here are some travel destinations worth considering:\n\n1. **Bali - Indonesia**\nBeautiful beaches, unique culture, and rich natural landscapes make it ideal for relaxation and exploration.\n\n2. **Kyoto - Japan**\nHome to over 1,600 temples and shrines, it\'s a treasure of Japanese traditional culture.\n\n3. **Barcelona - Spain**\nGaudí\'s architectural masterpieces, Mediterranean cuisine, and vibrant city atmosphere.\n\n4. **Maldives**\nOverwater villas and stunning marine life, perfect for luxury and tranquility.\n\n5. **Santorini - Greece**\nIconic white and blue buildings, plus incredible Aegean Sea sunsets.\n\nDo you have a specific region or type of travel in mind? I can provide more targeted recommendations.',
    
    'Travel tips': 'Here are some essential travel tips for a smooth journey:\n\n**Before You Travel**\n- Check passport validity (6+ months remaining)\n- Research visa requirements and local customs\n- Get travel insurance and vaccinations if needed\n- Notify banks of travel plans\n\n**Packing Smart**\n- Pack light with versatile clothing\n- Bring copies of important documents\n- Keep essentials in carry-on bag\n- Check airline baggage restrictions\n\n**During Your Trip**\n- Stay hydrated and eat safely\n- Keep emergency contacts handy\n- Respect local customs and dress codes\n- Use reliable transportation options\n\nWould you like specific tips for any particular destination or type of travel?',
    
    'Local cuisine': 'Exploring local cuisine is one of the best ways to understand a culture. Here are specialty foods from several travel destinations:\n\n**Southeast Asia**\n- Thailand: Tom Yum Soup, Pad Thai, and Mango Sticky Rice\n- Vietnam: Pho, Spring Rolls, and Banh Mi sandwiches\n- Malaysia: Satay, Nyonya cuisine, and Bak Kut Teh\n\n**Europe**\n- Italy: Unique pasta from each region, pizza, and tiramisu\n- Spain: Paella, tapas, and Iberian ham\n- France: Croissants, steak, cheese, and elegant desserts\n\n**Asia**\n- Japan: Sushi, ramen, and tempura\n- China: Specialty cuisines from various provinces such as Sichuan, Cantonese, Shandong, etc.\n- India: Various curries, naan bread, and spice-rich dishes\n\nTrying street food is often an excellent way to experience the most authentic flavors. Which region\'s cuisine are you particularly interested in?'
  };
  
  // More intelligent keyword matching
  for (const key in responses) {
    // Check if query contains keywords
    const lowerQuery = (query || '').toLowerCase();
    const lowerKey = (key || '').toLowerCase();
    if (lowerQuery.includes(lowerKey) || 
        (lowerKey.includes('destination') && lowerQuery.includes('recommend')) ||
        (lowerKey.includes('travel') && lowerQuery.includes('tips')) ||
        (lowerKey.includes('cuisine') && lowerQuery.includes('food'))) {
      return responses[key];
    }
  }
  
  // Generic response is more friendly and informative
  return `About "${query}", I can provide the following information:\n\nAs your travel assistant, I can help you with:\n\n• Recommending popular or off-the-beaten-path destinations worldwide\n• Planning itineraries and optimizing your travel time\n• Introducing local cuisine and cultural specialties\n• Providing accommodation and transportation advice\n• Sharing budget travel tips\n\nPlease tell me which specific aspect you're interested in, and I'll provide more relevant information.`;
};

// Simulate current date
const getCurrentDate = () => {
  const now = new Date();
  return `${now.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}`;
};

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'gemini';
  timestamp: number;
  imageUri?: string;
}

const TypingAnimation = () => {
  const [dot1] = useState(new Animated.Value(0));
  const [dot2] = useState(new Animated.Value(0));
  const [dot3] = useState(new Animated.Value(0));

  useEffect(() => {
    const animateDot = (dot: Animated.Value, delay: number) => {
      Animated.sequence([
        Animated.timing(dot, {
          toValue: 1,
          duration: 400,
          delay,
          useNativeDriver: true
        }),
        Animated.timing(dot, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true
        })
      ]).start();
    };

    const startAnimation = () => {
      animateDot(dot1, 0);
      animateDot(dot2, 200);
      animateDot(dot3, 400);
      
      setTimeout(startAnimation, 1200);
    };

    startAnimation();
    
    return () => {
      // Cleanup animation
      dot1.stopAnimation();
      dot2.stopAnimation();
      dot3.stopAnimation();
    };
  }, [dot1, dot2, dot3]);

  return (
    <View style={styles.typingIndicator}>
      <Animated.View 
        style={[
          styles.typingDot,
          {
            transform: [
              {
                translateY: dot1.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -4]
                })
              }
            ]
          }
        ]}
      />
      <Animated.View 
        style={[
          styles.typingDot,
          {
            transform: [
              {
                translateY: dot2.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -4]
                })
              }
            ]
          }
        ]}
      />
      <Animated.View 
        style={[
          styles.typingDot,
          {
            transform: [
              {
                translateY: dot3.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -4]
                })
              }
            ]
          }
        ]}
      />
    </View>
  );
};

const GeminiChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [suggestions] = useState([
    'Recommend travel destinations', 
    'Local cuisine',
    'Travel tips'
  ]);
  const [selectedSuggestion, setSelectedSuggestion] = useState<number | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const date = getCurrentDate();
  
  // Image attachment states
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [showImageAttachmentModal, setShowImageAttachmentModal] = useState(false);
  const [imagePickerAvailable, setImagePickerAvailable] = useState(true);

  // 添加语音聊天状态
  const [isRecording, setIsRecording] = useState(false);
  const [recordingObject, setRecordingObject] = useState<any>(null);
  const [speechAvailable, setSpeechAvailable] = useState(false);

  // Check for permissions and module availability when component mounts
  useEffect(() => {
    (async () => {
      // 检查图片选择器模块是否可用
      if (!ImagePicker) {
        setImagePickerAvailable(false);
        return;
      }
      
      if (Platform.OS !== 'web') {
        try {
          const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
          const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
          if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
            Alert.alert(
              'Permissions Required',
              'Please grant camera and photo library permissions to use this feature.',
              [{ text: 'OK' }]
            );
          }
        } catch (error) {
          console.warn('Error requesting camera permissions:', error);
          setImagePickerAvailable(false);
        }
      }
    })();

    // 检查语音模块是否可用
    if (Audio && Speech) {
      setSpeechAvailable(true);
    }
  }, []);

  const handleTakePhoto = async () => {
    setShowImageAttachmentModal(false);
    
    // 检查模块是否可用
    if (!ImagePicker || !imagePickerAvailable) {
      Alert.alert(
        'Feature Unavailable',
        'Camera functionality is not available on this device.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.warn('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const handleChooseFromLibrary = async () => {
    setShowImageAttachmentModal(false);
    
    // 检查模块是否可用
    if (!ImagePicker || !imagePickerAvailable) {
      Alert.alert(
        'Feature Unavailable',
        'Photo library access is not available on this device.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.warn('Error choosing image from library:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const handleRemoveImage = () => {
    setImageUri(null);
  };

  const handleSend = async () => {
    if ((!input.trim() && !imageUri)) return;
    
    // Add user message
    const userMessage: Message = { 
      id: Date.now(), 
      text: input || "Here's an image I'd like to ask about:", 
      sender: 'user',
      timestamp: Date.now(),
      imageUri: imageUri || undefined
    };
    
    setMessages([...messages, userMessage]);
    setInput('');
    setImageUri(null);
    setLoading(true);
    
    // Scroll to bottom
    scrollToBottom();
    
    // Simulate AI processing delay
    setTimeout(() => {
      // Get response - pass imageAttached flag if there was an image
      const responseText = mockResponse(input, !!userMessage.imageUri);
      
      // Add AI response
      setMessages(prev => [...prev, { 
        id: Date.now() + 1, 
        text: responseText, 
        sender: 'gemini',
        timestamp: Date.now()
      }]);
      setLoading(false);
      
      // Ensure scroll to bottom
      setTimeout(scrollToBottom, 100);
    }, 2000);
  };

  const handleSuggestionPress = (suggestion: string, index: number) => {
    setSelectedSuggestion(index);
    
    // Add user message
    const userMessage = { 
      id: Date.now(), 
      text: suggestion, 
      sender: 'user' as const,
      timestamp: Date.now()
    };
    setMessages([...messages, userMessage]);
    setLoading(true);
    
    // Scroll to bottom
    scrollToBottom();
    
    // Simulate API delay
    setTimeout(() => {
      // Get response
      const responseText = mockResponse(suggestion);
      
      // Add AI response
      setMessages(prev => [...prev, { 
        id: Date.now() + 1, 
        text: responseText, 
        sender: 'gemini' as const,
        timestamp: Date.now()
      }]);
      setLoading(false);
      setSelectedSuggestion(null);
      
      // Ensure scroll to bottom
      setTimeout(scrollToBottom, 100);
    }, 2000);
  };

  const scrollToBottom = () => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  };

  // Handle formatted text in messages
  const formatMessageText = (text: string) => {
    // Handle bold text **text**
    const boldPattern = /\*\*(.*?)\*\*/g;
    const parts = [];
    let lastIndex = 0;
    let match;
    
    while ((match = boldPattern.exec(text)) !== null) {
      if (match.index > lastIndex) {
        parts.push(
          <Text key={`text-${lastIndex}`} style={styles.messageText}>
            {text.slice(lastIndex, match.index)}
          </Text>
        );
      }
      
      parts.push(
        <Text 
          key={`bold-${match.index}`} 
          style={[styles.messageText, { fontWeight: 'bold' }]}
        >
          {match[1]}
        </Text>
      );
      
      lastIndex = match.index + match[0].length;
    }
    
    if (lastIndex < text.length) {
      parts.push(
        <Text key={`text-${lastIndex}`} style={styles.messageText}>
          {text.slice(lastIndex)}
        </Text>
      );
    }
    
    return parts.length > 0 ? parts : <Text style={styles.messageText}>{text}</Text>;
  };

  const renderImageMessage = (uri: string) => {
    return (
      <View style={styles.imageMessageContainer}>
        <Image 
          source={{ uri }} 
          style={styles.imageMessage}
          resizeMode="cover"
        />
      </View>
    );
  };

  // 处理语音识别
  const toggleVoiceRecording = async () => {
    if (!Audio || !speechAvailable) {
      Alert.alert(
        'Feature Unavailable',
        'Voice chat functionality is not available on this device.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      if (isRecording) {
        // 停止录音
        setIsRecording(false);
        if (recordingObject) {
          await recordingObject.stopAndUnloadAsync();
          const uri = recordingObject.getURI();
          
          // 这里可以添加语音识别的逻辑，将识别结果设置到input
          // 模拟识别结果
          setInput('What are the best places to visit in Japan?');
          
          setRecordingObject(null);
        }
      } else {
        // 开始录音
        await Audio.requestPermissionsAsync();
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: true,
          playsInSilentModeIOS: true,
        });
        
        const { recording } = await Audio.Recording.createAsync(
          Audio.RECORDING_OPTIONS_PRESET_HIGH_QUALITY
        );
        
        setRecordingObject(recording);
        setIsRecording(true);
      }
    } catch (error) {
      console.warn('Error with voice recording:', error);
      Alert.alert('Error', 'Failed to process voice. Please try again.');
      setIsRecording(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <Ionicons name="navigate" size={20} color="#4285f4" />
          <Text style={styles.headerTitle}>iGuided AI</Text>
        </View>
        
        <ScrollView 
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesList}
          keyboardShouldPersistTaps="handled"
        >
          {/* Date divider */}
          <View style={styles.dateDivider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dateText}>{date}</Text>
            <View style={styles.dividerLine} />
          </View>
          
          {messages.length === 0 ? (
            <View style={styles.welcomeContainer}>
              <Text style={styles.welcomeTitle}>iGuided AI</Text>
              <Text style={styles.welcomeSubtitle}>Your intelligent travel assistant, providing professional travel advice, itinerary planning, and destination information</Text>
              <View style={styles.suggestionContainer}>
                {suggestions.map((suggestion, index) => (
                  <TouchableOpacity 
                    key={index} 
                    style={[
                      styles.suggestionButton,
                      selectedSuggestion === index && styles.suggestionButtonActive
                    ]}
                    onPress={() => handleSuggestionPress(suggestion, index)}
                    disabled={loading}
                  >
                    <Text style={styles.suggestionText}>{suggestion}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          ) : (
            messages.map((message, index) => (
              <View 
                key={message.id} 
                style={styles.messageRow}
              >
                {/* Message sender avatar */}
                {message.sender === 'gemini' ? (
                  <View style={styles.botAvatarContainer}>
                    <Text style={styles.botAvatarText}>G</Text>
                  </View>
                ) : (
                  <View style={styles.avatarContainer}>
                    <Text style={styles.userAvatarText}>U</Text>
                  </View>
                )}
                
                {/* Message content */}
                <View style={styles.messageContent}>
                  <View 
                    style={[
                      styles.messageBubble, 
                      message.sender === 'user' ? styles.userBubble : styles.geminiBubble
                    ]}
                  >
                    {formatMessageText(message.text)}
                    {message.imageUri && renderImageMessage(message.imageUri)}
                  </View>
                </View>
              </View>
            ))
          )}
          
          {/* Loading animation */}
          {loading && (
            <View style={styles.messageRow}>
              <View style={styles.botAvatarContainer}>
                <Text style={styles.botAvatarText}>G</Text>
              </View>
              <TypingAnimation />
            </View>
          )}
          
          {/* Image preview */}
          {imageUri && (
            <View style={styles.imagePreviewContainer}>
              <Image 
                source={{ uri: imageUri }} 
                style={styles.imagePreview}
                resizeMode="cover"
              />
              <TouchableOpacity 
                style={styles.removeImageButton} 
                onPress={handleRemoveImage}
              >
                <Ionicons name="close" size={16} color="#ffffff" />
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
        
        {/* Input area */}
        <View style={styles.inputContainer}>
          <TouchableOpacity 
            style={styles.attachmentButton}
            onPress={() => setShowImageAttachmentModal(true)}
          >
            <Ionicons 
              name="add-circle" 
              size={24} 
              color="#4285f4" 
            />
          </TouchableOpacity>
          
          <TextInput
            style={styles.input}
            value={input}
            onChangeText={setInput}
            placeholder="Send a message to iGuided..."
            placeholderTextColor="#71767a"
            multiline
            maxLength={1000}
          />

          {/* 语音聊天按钮 */}
          <TouchableOpacity 
            style={[
              styles.voiceButton,
              isRecording && styles.voiceButtonRecording
            ]} 
            onPress={toggleVoiceRecording}
          >
            <Ionicons 
              name={isRecording ? "mic" : "mic-outline"}
              size={24} 
              color="#ffffff"
            />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.sendButton,
              (!input.trim() && !imageUri) && styles.sendButtonDisabled
            ]} 
            onPress={handleSend}
            disabled={(!input.trim() && !imageUri) || loading}
          >
            <Ionicons 
              name="send"
              size={18} 
              color="#ffffff"
              style={styles.sendIcon}
            />
          </TouchableOpacity>
        </View>
        
        {/* Image attachment modal */}
        <Modal
          visible={showImageAttachmentModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowImageAttachmentModal(false)}
        >
          <TouchableWithoutFeedback onPress={() => setShowImageAttachmentModal(false)}>
            <View style={styles.modalContainer}>
              <TouchableWithoutFeedback>
                <View style={styles.modalContent}>
                  <View style={styles.modalHeader}>
                    <Text style={styles.modalTitle}>Add Image</Text>
                    <TouchableOpacity onPress={() => setShowImageAttachmentModal(false)}>
                      <Ionicons name="close" size={24} color="#5f6368" />
                    </TouchableOpacity>
                  </View>
                  
                  <TouchableOpacity 
                    style={styles.modalOption} 
                    onPress={handleTakePhoto}
                  >
                    <Ionicons name="camera" size={24} color="#4285f4" />
                    <Text style={styles.modalOptionText}>Take photo</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.modalOption} 
                    onPress={handleChooseFromLibrary}
                  >
                    <Ionicons name="image" size={24} color="#4285f4" />
                    <Text style={styles.modalOptionText}>Choose from library</Text>
                  </TouchableOpacity>
                </View>
              </TouchableWithoutFeedback>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </View>
    </KeyboardAvoidingView>
  );
};

export default GeminiChat; 