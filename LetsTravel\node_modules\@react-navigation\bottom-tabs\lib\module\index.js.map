{"version": 3, "names": ["default", "createBottomTabNavigator", "BottomTabBar", "BottomTabView", "BottomTabBarHeightCallbackContext", "BottomTabBarHeightContext", "useBottomTabBarHeight"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,wBAAwB,QAAQ,uCAAuC;;AAE3F;AACA;AACA;AACA,SAASD,OAAO,IAAIE,YAAY,QAAQ,sBAAsB;AAC9D,SAASF,OAAO,IAAIG,aAAa,QAAQ,uBAAuB;;AAEhE;AACA;AACA;AACA,SAASH,OAAO,IAAII,iCAAiC,QAAQ,2CAA2C;AACxG,SAASJ,OAAO,IAAIK,yBAAyB,QAAQ,mCAAmC;AACxF,SAASL,OAAO,IAAIM,qBAAqB,QAAQ,+BAA+B;;AAEhF;AACA;AACA"}