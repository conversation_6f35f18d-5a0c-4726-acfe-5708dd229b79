{"version": 3, "file": "ErrorToastContainer.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/toast/ErrorToastContainer.tsx"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAE1B,SAAwB,mBAAmB,CAAC,EAC1C,QAAQ,GAGT;IACC,OAAO,8DAAG,QAAQ,CAAI,CAAC;AACzB,CAAC;AAND,sCAMC", "sourcesContent": ["import React from 'react';\n\nexport default function ErrorToastContainer({\n  children,\n}: {\n  children: React.ReactNode;\n}): React.ReactElement {\n  return <>{children}</>;\n}\n"]}