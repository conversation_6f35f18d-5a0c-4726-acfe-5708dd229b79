{"version": 3, "file": "linking.js", "sourceRoot": "", "sources": ["../../src/link/linking.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,sDAAwC;AACxC,+CAAwC;AAExC,mEAA8E;AAC9E,gFAAwD;AAkG7B,2BAlGpB,0BAAgB,CAkGoB;AAjG3C,gFAAwD;AAiG/C,2BAjGF,0BAAgB,CAiGE;AA9FzB,MAAM,QAAQ,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC;AAEjF,SAAS,wBAAwB;IAC/B,OAAO,OAAO,CAAC,IAAI,CAAC;QAClB,OAAO,CAAC,aAAa,EAAE;QACvB,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;QAC5B,wDAAwD;QACxD,uEAAuE;QACvE,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CACrC;KACF,CAAC,CAAC;AACL,CAAC;AAED,8EAA8E;AAC9E,wEAAwE;AACxE,+EAA+E;AAC/E,8GAA8G;AAC9G,8EAA8E;AAC9E,SAAgB,aAAa;IAG3B,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;QACzB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,OAAO,EAAE,CAAC;SACX;aAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE;YAChC,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;SAC7B;KACF;IAED,OAAO,wBAAwB,EAAE,CAAC,IAAI,CACpC,CAAC,GAAG,EAAE,EAAE,CACN,0BAA0B,CAAC,GAAG,CAAC;QAC/B,uFAAuF;QACvF,4DAA4D;QAC5D,UAAU,EAAE,CACf,CAAC;AACJ,CAAC;AAlBD,sCAkBC;AAED,IAAI,QAA4B,CAAC;AAEjC,SAAgB,UAAU;IACxB,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KACnC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AALD,gCAKC;AAED,2DAA2D;AAC3D,SAAS,0BAA0B,CAA0B,GAAM;IACjE,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE;QACrB,OAAO,GAAG,CAAC;KACZ;IACD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,IAAA,qDAAgC,EAAC,GAAG,CAAC,CAAC;IACxE,+EAA+E;IAC/E,oFAAoF;IACpF,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,GAAG,EAAE;QACjC,OAAO,CAAC,UAAU,EAAE,GAAG,WAAW,CAAM,CAAC;KAC1C;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAgB,gBAAgB,CAAC,aAA4B;IAC3D,OAAO,CAAC,QAA+B,EAAE,EAAE;QACzC,IAAI,QAA0D,CAAC;QAE/D,IAAI,QAAQ,EAAE;YACZ,mDAAmD;YACnD,QAAQ,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;gBAC3B,GAAG,GAAG,0BAA0B,CAAC,GAAG,CAAC,CAAC;gBAEtC,IAAI,GAAG,IAAI,aAAa,EAAE,kBAAkB,EAAE;oBAC5C,GAAG,GAAG,MAAM,aAAa,CAAC,kBAAkB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;iBAC7E;gBAED,QAAQ,CAAC,GAAG,CAAC,CAAC;YAChB,CAAC,CAAC;SACH;aAAM;YACL,QAAQ,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;gBAC3B,IAAI,GAAG,IAAI,aAAa,EAAE,kBAAkB,EAAE;oBAC5C,GAAG,GAAG,MAAM,aAAa,CAAC,kBAAkB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;iBAC7E;gBACD,QAAQ,CAAC,GAAG,CAAC,CAAC;YAChB,CAAC,CAAC;SACH;QAED,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE/D,OAAO,GAAG,EAAE;YACV,2FAA2F;YAC3F,YAAY,EAAE,MAAM,EAAE,EAAE,CAAC;QAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AA/BD,4CA+BC", "sourcesContent": ["import { LinkingOptions } from '@react-navigation/native';\nimport * as Linking from 'expo-linking';\nimport { Platform } from 'react-native';\n\nimport { parsePathAndParamsFromExpoGoLink } from '../fork/extractPathFromURL';\nimport getPathFromState from '../fork/getPathFromState';\nimport getStateFromPath from '../fork/getStateFromPath';\nimport { NativeIntent } from '../types';\n\nconst isExpoGo = typeof expo !== 'undefined' && globalThis.expo?.modules?.ExpoGo;\n\nfunction getInitialURLWithTimeout(): Promise<string | null> {\n  return Promise.race([\n    Linking.getInitialURL(),\n    new Promise<null>((resolve) =>\n      // Timeout in 150ms if `getInitialState` doesn't resolve\n      // Workaround for https://github.com/facebook/react-native/issues/25675\n      setTimeout(() => resolve(null), 150)\n    ),\n  ]);\n}\n\n// A custom getInitialURL is used on native to ensure the app always starts at\n// the root path if it's launched from something other than a deep link.\n// This helps keep the native functionality working like the web functionality.\n// For example, if you had a root navigator where the first screen was `/settings` and the second was `/index`\n// then `/index` would be used on web and `/settings` would be used on native.\nexport function getInitialURL(): ReturnType<\n  NonNullable<LinkingOptions<Record<string, unknown>>['getInitialURL']>\n> {\n  if (Platform.OS === 'web') {\n    if (typeof window === 'undefined') {\n      return '';\n    } else if (window.location?.href) {\n      return window.location.href;\n    }\n  }\n\n  return getInitialURLWithTimeout().then(\n    (url) =>\n      parseExpoGoUrlFromListener(url) ??\n      // The path will be nullish in bare apps when the app is launched from the home screen.\n      // TODO(EvanBacon): define some policy around notifications.\n      getRootURL()\n  );\n}\n\nlet _rootURL: string | undefined;\n\nexport function getRootURL(): string {\n  if (_rootURL === undefined) {\n    _rootURL = Linking.createURL('/');\n  }\n  return _rootURL;\n}\n\n// Expo Go is weird and requires the root path to be `/--/`\nfunction parseExpoGoUrlFromListener<T extends string | null>(url: T): T {\n  if (!url || !isExpoGo) {\n    return url;\n  }\n  const { pathname, queryString } = parsePathAndParamsFromExpoGoLink(url);\n  // If the URL is defined (default in Expo Go dev apps) and the URL has no path:\n  // `exp://192.168.87.39:19000/` then use the default `exp://192.168.87.39:19000/--/`\n  if (!pathname || pathname === '/') {\n    return (getRootURL() + queryString) as T;\n  }\n  return url;\n}\n\nexport function addEventListener(nativeLinking?: NativeIntent) {\n  return (listener: (url: string) => void) => {\n    let callback: (({ url }: { url: string }) => void) | undefined;\n\n    if (isExpoGo) {\n      // This extra work is only done in the Expo Go app.\n      callback = async ({ url }) => {\n        url = parseExpoGoUrlFromListener(url);\n\n        if (url && nativeLinking?.redirectSystemPath) {\n          url = await nativeLinking.redirectSystemPath({ path: url, initial: false });\n        }\n\n        listener(url);\n      };\n    } else {\n      callback = async ({ url }) => {\n        if (url && nativeLinking?.redirectSystemPath) {\n          url = await nativeLinking.redirectSystemPath({ path: url, initial: false });\n        }\n        listener(url);\n      };\n    }\n\n    const subscription = Linking.addEventListener('url', callback);\n\n    return () => {\n      // https://github.com/facebook/react-native/commit/6d1aca806cee86ad76de771ed3a1cc62982ebcd7\n      subscription?.remove?.();\n    };\n  };\n}\n\nexport { getStateFromPath, getPathFromState };\n"]}