{"name": "expo-router", "version": "3.5.23", "description": "Expo Router is a file-based router for React Native and web applications.", "author": "650 Industries, Inc.", "license": "MIT", "main": "build/index", "types": "build/index.d.ts", "sideEffects": ["entry.js"], "files": ["assets", "build", "ios", "node", "plugin", "!src", "types", "_ctx.*", "_ctx-html.js", "_ctx-shared.js", "_error.js", "app.plugin.js", "babel.js", "drawer.js", "drawer.d.ts", "entry.js", "expo-module.config.json", "head.js", "head.d.ts", "html.js", "html.d.ts", "index.d.ts", "stack.js", "stack.d.ts", "server.js", "server.d.ts", "tabs.js", "tabs.d.ts", "testing-library.js", "testing-library.d.ts"], "repository": {"url": "https://github.com/expo/expo.git", "type": "git", "directory": "packages/expo-router"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://docs.expo.dev/routing/introduction/", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "test:tsd": "EXPORT_ROUTER_JEST_TSD=true expo-module test", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo"], "peerDependencies": {"@react-navigation/drawer": "^6.5.8", "expo": "*", "expo-constants": "*", "expo-linking": "*", "expo-status-bar": "*", "react-native-reanimated": "*", "react-native-safe-area-context": "*", "react-native-screens": "*"}, "peerDependenciesMeta": {"react-native-reanimated": {"optional": true}, "@react-navigation/drawer": {"optional": true}, "@testing-library/jest-native": {"optional": true}}, "devDependencies": {"@react-navigation/drawer": "^6.5.0", "@testing-library/jest-native": "^5.4.2", "@testing-library/react": "^14.0.0", "@testing-library/react-native": "^12.0.1", "tsd": "^0.28.1"}, "dependencies": {"@expo/metro-runtime": "3.2.3", "@expo/server": "^0.4.0", "@radix-ui/react-slot": "1.0.1", "@react-navigation/bottom-tabs": "~6.5.7", "@react-navigation/native": "~6.1.6", "@react-navigation/native-stack": "~6.9.12", "expo-splash-screen": "0.27.5", "react-native-helmet-async": "2.0.4", "schema-utils": "^4.0.1"}, "gitHead": "053dc876b774084013425644648f217fe995deb1"}