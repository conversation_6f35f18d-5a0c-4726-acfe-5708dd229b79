{"version": 3, "file": "Orientation.js", "names": ["_Manifest", "data", "require", "_androidPlugins", "SCREEN_ORIENTATION_ATTRIBUTE", "exports", "withOrientation", "createAndroidManifestPlugin", "setAndroidOrientation", "getOrientation", "config", "orientation", "androidManifest", "mainActivity", "getMainActivityOrThrow", "$"], "sources": ["../../src/android/Orientation.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { AndroidManifest, getMainActivityOrThrow } from './Manifest';\nimport { createAndroidManifestPlugin } from '../plugins/android-plugins';\n\nexport const SCREEN_ORIENTATION_ATTRIBUTE = 'android:screenOrientation';\n\nexport const withOrientation = createAndroidManifestPlugin(\n  setAndroidOrientation,\n  'withOrientation'\n);\n\nexport function getOrientation(config: Pick<ExpoConfig, 'orientation'>) {\n  return typeof config.orientation === 'string' ? config.orientation : null;\n}\n\nexport function setAndroidOrientation(\n  config: Pick<ExpoConfig, 'orientation'>,\n  androidManifest: AndroidManifest\n) {\n  const orientation = getOrientation(config);\n  // TODO: Remove this if we decide to remove any orientation configuration when not specified\n  if (!orientation) {\n    return androidManifest;\n  }\n\n  const mainActivity = getMainActivityOrThrow(androidManifest);\n\n  mainActivity.$[SCREEN_ORIENTATION_ATTRIBUTE] =\n    orientation !== 'default' ? orientation : 'unspecified';\n\n  return androidManifest;\n}\n"], "mappings": ";;;;;;;;;AAEA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,gBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,eAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,4BAA4B,GAAAC,OAAA,CAAAD,4BAAA,GAAG,2BAA2B;AAEhE,MAAME,eAAe,GAAAD,OAAA,CAAAC,eAAA,GAAG,IAAAC,6CAA2B,EACxDC,qBAAqB,EACrB,iBACF,CAAC;AAEM,SAASC,cAAcA,CAACC,MAAuC,EAAE;EACtE,OAAO,OAAOA,MAAM,CAACC,WAAW,KAAK,QAAQ,GAAGD,MAAM,CAACC,WAAW,GAAG,IAAI;AAC3E;AAEO,SAASH,qBAAqBA,CACnCE,MAAuC,EACvCE,eAAgC,EAChC;EACA,MAAMD,WAAW,GAAGF,cAAc,CAACC,MAAM,CAAC;EAC1C;EACA,IAAI,CAACC,WAAW,EAAE;IAChB,OAAOC,eAAe;EACxB;EAEA,MAAMC,YAAY,GAAG,IAAAC,kCAAsB,EAACF,eAAe,CAAC;EAE5DC,YAAY,CAACE,CAAC,CAACX,4BAA4B,CAAC,GAC1CO,WAAW,KAAK,SAAS,GAAGA,WAAW,GAAG,aAAa;EAEzD,OAAOC,eAAe;AACxB", "ignoreList": []}