{"version": 3, "file": "index.native.js", "sourceRoot": "", "sources": ["../../src/async-require/index.native.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,wCAAwC;AACxC,8BAA8B;AAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAM,EAAE,iBAAiB,EAAE,GACzB,OAAO,CAAC,qBAAqB,CAAyC,CAAC;IACzE,aAAa;IACb,MAAM,CAAC,GAAG,MAAM,CAAC,uBAAuB,IAAI,EAAE,mBAAmB,CAAC,GAAG,iBAAiB,EAAE,CAAC;CAC1F", "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n// Ensure this is removed in production.\n// TODO: Enable in production.\nif (process.env.NODE_ENV !== 'production') {\n  const { buildAsyncRequire } =\n    require('./buildAsyncRequire') as typeof import('./buildAsyncRequire');\n  // @ts-ignore\n  global[`${global.__METRO_GLOBAL_PREFIX__ ?? ''}__loadBundleAsync`] = buildAsyncRequire();\n}\n"]}